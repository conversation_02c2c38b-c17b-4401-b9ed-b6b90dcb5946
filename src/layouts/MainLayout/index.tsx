import { ReactNode } from 'react';
import { featureFlags } from 'app.config';
import Footer from './Footer';
import TryDemo from './Footer/TryDemo';
import Navbar from './Navbar';
import TopBar from './Navbar/shared/TopBar';

interface MainLayoutProps {
    children: ReactNode;
}

const MainLayout = ({ children }: MainLayoutProps) => {
    return (
        <div className="flex min-h-screen flex-col">
            {featureFlags.shouldShowTopBar && <TopBar />}

            <Navbar />

            <main className="flex-1">{children}</main>

            <TryDemo />

            <Footer />
        </div>
    );
};

export default MainLayout;
