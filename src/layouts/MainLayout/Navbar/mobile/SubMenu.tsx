import { Disclosure } from '@headlessui/react';
import { memo } from 'react';
import { ExtendedLink } from '@src/components/shared';
import { useTrans } from '@src/hooks';
import { MinusIcon, PlusIcon } from '@src/icons/solid';
import { NavigationItem } from '@src/interfaces';

type SubMenuProps = Omit<NavigationItem, 'title'>;

const SubMenu = ({ subLinks }: SubMenuProps) => {
    const t = useTrans();

    return (
        <>
            {subLinks?.map((subLinkItem) => {
                const hasSubChildren =
                    subLinkItem.subChildren?.length !== 0 &&
                    Array.isArray(subLinkItem.subChildren);

                return (
                    <Disclosure key={subLinkItem.title}>
                        {({ open }) => (
                            <>
                                <li className="container">
                                    <Disclosure.Button className="flex w-full items-center justify-between py-4 pl-5 text-lg font-light">
                                        <div className="flex flex-col items-start justify-center">
                                            {hasSubChildren ? (
                                                <p>{t(subLinkItem.title)}</p>
                                            ) : (
                                                <ExtendedLink
                                                    href={subLinkItem.href}
                                                >
                                                    {subLinkItem.title}
                                                </ExtendedLink>
                                            )}

                                            {subLinkItem.description && (
                                                <p className="text-sm text-primary-500">
                                                    {t(subLinkItem.description)}
                                                </p>
                                            )}
                                        </div>

                                        {open
                                            ? hasSubChildren && (
                                                  <MinusIcon className="h-4 w-4" />
                                              )
                                            : hasSubChildren && (
                                                  <PlusIcon className="h-4 w-4 text-primary-500" />
                                              )}
                                    </Disclosure.Button>
                                </li>

                                {hasSubChildren && (
                                    <Disclosure.Panel className="container">
                                        <ul className="ml-10 flex flex-col space-y-2 border-l border-accent-200 pl-5 font-light">
                                            {subLinkItem.subChildren?.map(
                                                (subChildrenItem) => (
                                                    <li
                                                        key={
                                                            subChildrenItem.title
                                                        }
                                                    >
                                                        <ExtendedLink
                                                            href={
                                                                subLinkItem.href +
                                                                subChildrenItem.href
                                                            }
                                                            className="block py-2"
                                                        >
                                                            {t(
                                                                subChildrenItem.title
                                                            )}
                                                        </ExtendedLink>
                                                    </li>
                                                )
                                            )}
                                        </ul>
                                    </Disclosure.Panel>
                                )}
                            </>
                        )}
                    </Disclosure>
                );
            })}
        </>
    );
};

export default memo(SubMenu);
