import { Disclosure } from '@headlessui/react';
import { ExtendedLink } from '@src/components/shared';
import { useTrans } from '@src/hooks';
import { MinusIcon, PlusIcon } from '@src/icons/solid';
import { twMerge } from '@src/utils';
import { navigationItems } from '../shared/NavigationItems';
import SubMenu from './SubMenu';

const MobileMenu = () => {
    const t = useTrans();

    return (
        <ul>
            {navigationItems.map((navItem) => {
                const hasSubLinks =
                    navItem.subLinks?.length !== 0 &&
                    Array.isArray(navItem.subLinks);

                return (
                    <Disclosure key={navItem.title}>
                        {({ open }) => (
                            <>
                                <li>
                                    <Disclosure.Button
                                        className={twMerge(
                                            'container flex items-center justify-between text-lg text-white',
                                            !open &&
                                                'border-b border-neutral-100 border-opacity-20'
                                        )}
                                    >
                                        {/* If there is href it is a link otherwise it is a disclosure and have children */}
                                        {navItem.href ? (
                                            <ExtendedLink
                                                className="w-full py-4 text-left font-means"
                                                href={navItem.href}
                                            >
                                                {t(navItem.title)}
                                            </ExtendedLink>
                                        ) : (
                                            <>
                                                <p className="py-4 font-means">
                                                    {t(navItem.title)}
                                                </p>
                                                {open ? (
                                                    <MinusIcon className="h-4 w-4" />
                                                ) : (
                                                    <PlusIcon className="h-4 w-4 text-primary-500" />
                                                )}
                                            </>
                                        )}
                                    </Disclosure.Button>
                                </li>

                                {hasSubLinks && (
                                    <Disclosure.Panel
                                        as="ul"
                                        className="space-y-4 bg-primary-900 py-4 text-white"
                                    >
                                        <SubMenu subLinks={navItem.subLinks} />
                                    </Disclosure.Panel>
                                )}
                            </>
                        )}
                    </Disclosure>
                );
            })}
        </ul>
    );
};

export default MobileMenu;
