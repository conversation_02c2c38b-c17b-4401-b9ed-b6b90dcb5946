import { Transition } from '@headlessui/react';
import { useRouter } from 'next/router';
import { useEffect, useState } from 'react';
import { ExtendedLink, SolidButton } from '@src/components/shared';
import { useTrans } from '@src/hooks';
import { EnterERPIcon } from '@src/icons/brands';
import { BarsIcon, PhoneIcon, XMarkIcon } from '@src/icons/solid';
import { twMerge } from '@src/utils';
import MobileMenu from './Menu';

interface MobileProps {
    shouldNavColorChange: boolean;
}

const Mobile = ({ shouldNavColorChange }: MobileProps) => {
    const [toggleMobileMenu, setToggleMobileMenu] = useState(false);
    const router = useRouter();

    const t = useTrans();

    useEffect(() => {
        if (toggleMobileMenu) document.body.style.overflow = 'hidden';

        router.events.on('routeChangeStart', () => setToggleMobileMenu(false));

        return () => {
            document.body.style.overflow = 'auto';

            router.events.off('routeChangeStart', () =>
                setToggleMobileMenu(false)
            );
        };
    }, [toggleMobileMenu, router]);

    return (
        <nav className="container mobile-navbar-height flex items-center justify-between lg:hidden">
            <ExtendedLink href="/" title="EnterERP Logo">
                <EnterERPIcon
                    className={
                        shouldNavColorChange
                            ? 'last:fill-white'
                            : 'last:fill-black'
                    }
                />
            </ExtendedLink>

            <button
                onClick={() => setToggleMobileMenu(true)}
                className="py-[18px]"
                aria-label={t('Ana Menü Aç')}
            >
                <BarsIcon
                    className={twMerge(
                        'h-5 w-5',
                        shouldNavColorChange ? 'text-white' : 'text-primary-800'
                    )}
                />
            </button>

            <Transition
                show={toggleMobileMenu}
                enter="transform [transition:transform_.3s,opacity_.5s]"
                enterFrom="-translate-y-full translate-x-0 opacity-0"
                enterTo="translate-y-0 opacity-100"
                leave="transform [transition:transform_.3s,opacity_.5s]"
                leaveFrom="translate-y-0 opacity-100"
                leaveTo="-translate-y-full translate-x-0 opacity-0"
                className="fixed inset-0 z-50 bg-black pt-10"
            >
                <div className="container flex items-center justify-between">
                    <ExtendedLink href="/" title="EnterERP Logo">
                        <EnterERPIcon className="fill-white" />
                    </ExtendedLink>

                    <button
                        onClick={() => setToggleMobileMenu(false)}
                        className="py-[18px]"
                        aria-label={t('Ana Menü Kapat')}
                    >
                        <XMarkIcon className="h-5 w-5 text-white" />
                    </button>
                </div>

                <div className="h-full overflow-y-auto pb-20">
                    <MobileMenu />
                    <div className="grid place-items-center gap-4">
                        <SolidButton href="/signup" className="mt-5 px-12">
                            {t('Ücretsiz Dene')}
                        </SolidButton>

                        <a
                            href="tel:+908505820035"
                            className="inline-flex items-center gap-2.5 rounded-[100vw] border px-5 py-2.5 text-white"
                        >
                            <PhoneIcon className="h-5 w-5 fill-white stroke-white" />
                            <span>0 850 582 00 35</span>
                        </a>
                    </div>
                </div>
            </Transition>
        </nav>
    );
};

export default Mobile;
