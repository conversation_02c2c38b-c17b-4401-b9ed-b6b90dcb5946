import { useRouter } from 'next/router';
import { useCallback, useEffect, useState } from 'react';
import { twMerge } from '@src/utils';
import Desktop from './desktop';
import Mobile from './mobile';

const Navbar = () => {
    const [isNavTouchTop, setIsNavTouchTop] = useState(false);

    const router = useRouter();

    const hasDarkBackground =
        router.asPath === '/' || router.asPath === '/digital-transform';

    const yScrollEvent = useCallback(() => {
        const scroll = document.body.getBoundingClientRect();

        setIsNavTouchTop(scroll.top < -56);
    }, []);

    useEffect(() => {
        yScrollEvent();

        window.addEventListener('scroll', yScrollEvent);
        return () => {
            window.removeEventListener('scroll', yScrollEvent);
        };
    }, [yScrollEvent]);

    const shouldNavColorChange = hasDarkBackground && !isNavTouchTop;

    return (
        <header
            className={twMerge(
                'sticky top-0 z-40',
                shouldNavColorChange ? 'bg-white' : 'border-b bg-white'
            )}
        >
            <Mobile shouldNavColorChange={shouldNavColorChange} />
            <Desktop shouldNavColorChange={shouldNavColorChange} />
        </header>
    );
};

export default Navbar;
