import { NavigationItem } from '@src/interfaces';

export const navigationItems: NavigationItem[] = [
    {
        title: 'Uygulamalar',
        subLinks: [
            {
                title: 'Tüm Uygulamalar',
                href: '/apps',
                description: '<PERSON><PERSON><PERSON><PERSON><PERSON> uygulamaları keşfedin',
                columnSpan: 8,
                subChildren: [
                    { title: 'Satış Yönetimi', href: '/sales' },
                    { title: 'CRM', href: '/crm' },
                    { title: 'Finans Yönetimi', href: '/finance' },
                    { title: '<PERSON><PERSON><PERSON><PERSON> Yönetimi', href: '/accounting' },
                    { title: 'e-Ticaret', href: '/e-commerce' },
                    { title: '<PERSON><PERSON><PERSON>', href: '/inventory' },
                    {
                        title: '<PERSON><PERSON>r-Gider Yönetimi',
                        href: '/income-expense'
                    },
                    {
                        title: '<PERSON>arik Yönetimi',
                        href: '/supply-management'
                    },
                    { title: '<PERSON><PERSON>', href: '/services' },

                    { title: '<PERSON><PERSON><PERSON><PERSON><PERSON>', href: '/health-solution' },
                    {
                        title: '<PERSON><PERSON>',
                        href: '/project-management'
                    },
                    {
                        title: 'Yardım Masası',
                        href: '/help-desk'
                    },
                    {
                        title: 'e-Çözümler',
                        href: '/e-solutions'
                    },
                    {
                        title: 'İnsan Kaynakları Yönetimi',
                        href: '/human-resources'
                    }
                ]
            },
            {
                title: 'Yardımcı Uygulamalar',
                href: '/tools',
                description: 'Yardımcı araçlar ile güç kazanın',
                columnSpan: 4,
                subChildren: [
                    {
                        title: 'Ürün Konfigüratörü',
                        href: '/configurator'
                    },
                    { title: 'Ürün Entegrasyonu', href: '/product-import' },
                    {
                        title: 'Online Bankacılık',
                        href: '/online-banking'
                    },
                    {
                        title: 'Online Tahsilat',
                        href: '/online-collecting'
                    },
                    { title: 'EnterFlow', href: '/enter-flow' },
                    { title: 'Fiyatlandırma Sihirbazı', href: '/price-wizard' },
                    { title: 'Kampanya Yönetimi', href: '/campaign-management' }
                ]
            }
        ]
    },
    {
        title: 'Ürünler',
        href: '/pricing'
    },
    { title: 'Sektörler', href: '/industries' },
    { title: 'Blog', href: '/blog' },
    {
        title: 'Kaynaklar',
        subLinks: [
            {
                title: 'Kurumsal',
                href: '/corporate',
                description: 'EnterERP, Tanışın',
                columnSpan: 6,
                subChildren: [
                    { title: 'Kurumsal', href: '/about-us' },
                    { title: 'İş Ortağı', href: '/partnership' },
                    { title: 'Kariyer', href: '/career' }
                ]
            },
            {
                title: 'EnterERP',
                href: '/company',
                description: 'EnterERP, Tanıyın',
                columnSpan: 6,
                subChildren: [
                    {
                        title: 'Neden EnterERP?',
                        href: '/why-entererp'
                    },
                    {
                        title: 'Tüm İhtiyaçlarınız Tek Programda',
                        href: '/all-in-one'
                    },
                    {
                        title: 'Mobil ERP',
                        href: '/mobile'
                    },
                    // {
                    //     title: 'Müşteri Hikâyeleri',
                    //     href: '/customer-stories'
                    // }
                ]
            }
        ]
    },
    { title: 'İletişim', href: '/contact' }
];
