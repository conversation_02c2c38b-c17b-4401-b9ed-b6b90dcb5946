import { useRouter } from 'next/router';
import { useTrans } from '@src/hooks';
import { ExtendedLink } from '@src/components/shared';
import { ArrowRightIcon, PhoneIcon } from '@src/icons/solid';

const TopBar = () => {
    const t = useTrans();
    const router = useRouter();

    return (
        <div className="flex h-14 items-center justify-center gap-3 bg-primary-400 px-4 text-xs text-white md:text-sm">
            <div className="items-center gap-3 max-md:flex-1 md:flex">
                <p className="text-left lg:tracking-widest">
                    <span className="text-base font-semibold">%20</span>
                    {t("'ye Varan {{discountDate}} İndirimlerini Kaçırmayın!", {
                        discountDate: new Date().toLocaleDateString(
                            router.locale,
                            {
                                month: 'long'
                            }
                        )
                    })}
                </p>
                <ArrowRightIcon className="hidden h-5 w-5 fill-white stroke-white md:block" />
                <a
                    className="phone-number hidden text-sm font-medium md:block"
                    href="tel:+908505820035"
                >
                    0 850 582 00 35
                </a>
            </div>
            <span className="hidden md:block">{t('ya da')}</span>
            <ExtendedLink
                className="call-you group hidden items-center gap-2.5 rounded-[100vw] border px-4 py-1.5 transition hover:bg-white md:inline-flex"
                href="/contact"
            >
                <PhoneIcon className="h-5 w-5 fill-white stroke-white transition group-hover:fill-primary-400 group-hover:stroke-primary-400" />
                <span className="transition group-hover:text-primary-400">
                    {t('Sizi Arayalım')}
                </span>
            </ExtendedLink>
            <a
                className="call-us group inline-flex items-center gap-2.5 rounded-[100vw] border px-4 py-1.5 transition hover:bg-white md:hidden"
                href="tel:+908505820035"
            >
                <PhoneIcon className="h-5 w-5 fill-white stroke-white transition group-hover:fill-primary-400 group-hover:stroke-primary-400" />
                <span className="transition group-hover:text-primary-400">
                    {t('Bizi Arayın')}
                </span>
            </a>
        </div>
    );
};

export default TopBar;
