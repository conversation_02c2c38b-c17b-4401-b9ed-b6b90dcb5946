import { SolidButton, ExtendedLink } from '@src/components/shared';
import { useTrans } from '@src/hooks';
import { EnterERPIcon } from '@src/icons/brands';
import { twMerge } from '@src/utils';
import DesktopMenu from './Menu';

interface DesktopProps {
    shouldNavColorChange: boolean;
}

const Desktop = ({ shouldNavColorChange }: DesktopProps) => {
    const t = useTrans();

    return (
        <div className="desktop-navbar-height hidden items-center justify-between gap-2 px-10 lg:flex xl:gap-5">
            <ExtendedLink href="/" title="EnterERP Logo">
                <EnterERPIcon
                    className={
                        shouldNavColorChange
                            ? 'last:fill-black'
                            : 'last:fill-black'
                    }
                />
            </ExtendedLink>

            <nav className="flex-1" aria-label={t('Ana Navigasyon')}>
                <DesktopMenu shouldNavColorChange={shouldNavColorChange} />
            </nav>

            <div className="group hidden xl:inline-block">
                <ExtendedLink
                    className={twMerge(
                        'btn-transition inline-block cursor-pointer whitespace-nowrap rounded-[100vw]  px-8 py-4 font-medium leading-4 text-primary-800 group-hover:-translate-y-1',
                        shouldNavColorChange
                            ? 'group-hover:shadow-[0px_6px_0px_0px_#ffffff]'
                            : 'text-primary-800 group-hover:shadow-[0px_6px_0px_0px_#011D2A]'
                    )}
                    href="/digital-transform"
                >
                    {t('Randevu Talep Et')}
                </ExtendedLink>
            </div>

            <SolidButton
                variant={shouldNavColorChange ? 'white' : 'dark'}
                href="/signup"
            >
                {t('Ücretsiz Dene')}
            </SolidButton>
        </div>
    );
};

export default Desktop;
