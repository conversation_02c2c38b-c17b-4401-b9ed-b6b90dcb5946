import { ExtendedLink } from '@src/components/shared';
import { useTrans } from '@src/hooks';
import { NavigationItem } from '@src/interfaces';
import { twMerge } from '@src/utils';

type SubMenuProps = Partial<NavigationItem>;

const SubMenu = ({ subLinks }: SubMenuProps) => {
    const t = useTrans();

    return (
        <ul className="grid w-full grid-cols-12">
            {subLinks?.map((subLinkHeader, index) => {
                const hasDifferentBgColor = subLinks.length === index + 1;
                const hasThreeColumns = subLinkHeader.columnSpan >= 8;

                return (
                    <li
                        style={{
                            gridColumn: `span ${subLinkHeader.columnSpan} / span ${subLinkHeader.columnSpan}`
                        }}
                        className={twMerge(
                            'flex flex-col px-10 py-10 text-left text-sm xl:px-8 xl:text-base',
                            hasDifferentBgColor && 'border-l bg-gray-50'
                        )}
                        key={subLinkHeader.title}
                    >
                        <div className="cursor-default border-b border-accent-100">
                            <p className="block pb-5 font-means text-lg font-bold">
                                {t(subLinkHeader.title)}
                            </p>

                            {subLinkHeader.description && (
                                <p className="-mt-2.5 hidden whitespace-nowrap pb-5 text-xs xl:block">
                                    {t(subLinkHeader.description)}
                                </p>
                            )}
                        </div>

                        <div
                            className={twMerge(
                                'mt-5 grid grid-cols-1 gap-5',
                                hasThreeColumns && 'grid-cols-3'
                            )}
                        >
                            {(subLinkHeader?.subChildren ?? []).map(
                                (subChildrenItem) => {
                                    return (
                                        <ExtendedLink
                                            href={
                                                subLinkHeader.href +
                                                subChildrenItem.href
                                            }
                                            key={subChildrenItem.href}
                                            className="desktop-navbar-animation w-28 hover:text-primary-500 xl:w-36 2xl:w-52"
                                        >
                                            {t(subChildrenItem.title)}
                                        </ExtendedLink>
                                    );
                                }
                            )}
                        </div>
                    </li>
                );
            })}
        </ul>
    );
};

export default SubMenu;
