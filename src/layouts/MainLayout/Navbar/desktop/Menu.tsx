import { useRouter } from 'next/router';
import { useRef, useState } from 'react';
import { ExtendedLink } from '@src/components/shared';
import { useTrans } from '@src/hooks';
import { twMerge } from '@src/utils';
import { navigationItems } from '../shared/NavigationItems';
import SubMenu from './SubMenu';

interface DesktopMenuProps {
    shouldNavColorChange: boolean;
}

const DesktopMenu = ({ shouldNavColorChange }: DesktopMenuProps) => {
    const [navigationDelay, setNavigationDelay] = useState(false);

    const t = useTrans();

    const router = useRouter();

    const timerRef = useRef<any>(null);

    const activeLinkHandler = () => {
        clearTimeout(timerRef.current);
        if (!navigationDelay) {
            timerRef.current = setTimeout(() => {
                setNavigationDelay(true);
            }, 200);
        } else {
            setNavigationDelay(true);
        }
    };

    return (
        <ul key={router.asPath} className="flex">
            {navigationItems.map((navItem) => {
                let isHighlightedNavItem;

                if (navItem.href === undefined) {
                    isHighlightedNavItem = navItem.subLinks?.find((subLink) =>
                        router.asPath.startsWith(subLink.href ?? '')
                    );
                } else {
                    isHighlightedNavItem = router.asPath.startsWith(
                        navItem.href ?? ''
                    );
                }

                return (
                    <li
                        onMouseEnter={() => activeLinkHandler()}
                        onMouseLeave={() => {
                            setNavigationDelay(false);
                            clearTimeout(timerRef.current);
                        }}
                        className="group relative"
                        key={navItem.title}
                    >
                        {navItem.href && (
                            <ExtendedLink
                                className={twMerge(
                                    'flex cursor-pointer select-none items-center px-2 py-8 group-hover:text-primary-500 group-hover:underline group-hover:decoration-4 group-hover:underline-offset-[12px] xl:px-5',
                                    shouldNavColorChange
                                        ? 'text-primary-800'
                                        : 'text-primary-800',
                                    isHighlightedNavItem &&
                                        'underline decoration-4 underline-offset-[12px]'
                                )}
                                href={navItem.href}
                            >
                                {navItem.title}
                            </ExtendedLink>
                        )}

                        {!navItem.href && (
                            <p
                                className={twMerge(
                                    'cursor-pointer select-none px-2 py-8 group-hover:text-primary-500 group-hover:underline group-hover:decoration-4 group-hover:underline-offset-[12px] xl:px-5',
                                    shouldNavColorChange
                                        ? 'text-primary-800'
                                        : 'text-primary-800',
                                    isHighlightedNavItem &&
                                        'underline decoration-4 underline-offset-[12px]'
                                )}
                            >
                                {t(navItem.title)}
                            </p>
                        )}

                        {Array.isArray(navItem.subLinks) &&
                            navItem.subLinks && (
                                <div
                                    className={twMerge(
                                        'desktop-navbar-shadow invisible absolute bottom-0 left-0 z-50 min-h-fit w-full min-w-max max-w-5xl translate-y-full overflow-hidden rounded-lg bg-white opacity-0',
                                        navigationDelay &&
                                            'transition group-hover:visible group-hover:opacity-100'
                                    )}
                                >
                                    <SubMenu subLinks={navItem.subLinks} />
                                </div>
                            )}
                    </li>
                );
            })}
        </ul>
    );
};

export default DesktopMenu;
