import {
    FacebookIcon,
    InstagramIcon,
    LinkedInIcon,
    TwitterIcon
} from '@src/icons/brands';

const SocialLinks = () => {
    return (
        <ul className="flex items-center gap-2.5 text-white md:gap-6">
            <li>
                <a
                    className="facebook transition duration-300 hover:opacity-70"
                    title="Facebook"
                    rel="noopener noreferrer"
                    target="_blank"
                    href="https://www.facebook.com/entererpx/"
                >
                    <FacebookIcon className="h-7 w-7" />
                </a>
            </li>
            <li>
                <a
                    className="instagram transition duration-300 hover:opacity-70"
                    title="Instagram"
                    rel="noopener noreferrer"
                    target="_blank"
                    href="https://www.instagram.com/entererpcom/"
                >
                    <InstagramIcon className="h-7 w-7" />
                </a>
            </li>
            <li>
                <a
                    className="linkedin transition duration-300 hover:opacity-70"
                    title="Linkedin"
                    rel="noopener noreferrer"
                    target="_blank"
                    href="https://www.linkedin.com/company/entererp/"
                >
                    <LinkedInIcon className="h-7 w-7" />
                </a>
            </li>
            <li>
                <a
                    className="twitter transition duration-300 hover:opacity-70"
                    title="Twitter"
                    rel="noopener noreferrer"
                    target="_blank"
                    href="https://www.twitter.com/entererp/"
                >
                    <TwitterIcon className="h-7 w-7" />
                </a>
            </li>
        </ul>
    );
};

export default SocialLinks;
