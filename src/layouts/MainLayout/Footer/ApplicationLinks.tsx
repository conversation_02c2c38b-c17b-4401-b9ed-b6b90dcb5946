import { ExtendedImage } from '@src/components/shared';
import { useTrans } from '@src/hooks';
import AppStoreIcon from 'public/images/icons/AppStore.svg';
import PlayStoreIcon from 'public/images/icons/PlayStore.svg';

const ApplicationLinks = () => {
    const t = useTrans();

    return (
        <div className="max-md:mt-8">
            <ul className="grid grid-cols-2 gap-4 md:grid-cols-1">
                <li className="w-fit md:ml-auto">
                    <a
                        className="apple-store transition duration-300 hover:opacity-70"
                        title="Appstore"
                        rel="noopener noreferrer"
                        target="_blank"
                        href="https://apps.apple.com/tr/app/enter-app/id1497945605"
                    >
                        <ExtendedImage
                            src={AppStoreIcon}
                            alt={t('AppStore Logo')}
                            className="w-52 select-none md:ml-auto"
                        />
                    </a>
                </li>
                <li className="w-fit md:ml-auto">
                    <a
                        className="google-play transition duration-300 hover:opacity-70"
                        title="Google Play"
                        rel="noopener noreferrer"
                        target="_blank"
                        href="https://play.google.com/store/apps/details?id=com.enteryazilim.EnterApp"
                    >
                        <ExtendedImage
                            src={PlayStoreIcon}
                            alt={t('PlayStore Logo')}
                            className="w-52 select-none md:ml-auto"
                        />
                    </a>
                </li>
            </ul>
        </div>
    );
};

export default ApplicationLinks;
