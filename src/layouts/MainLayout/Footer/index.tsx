import { ExtendedImage, ExtendedLink } from '@src/components/shared';
import { useTrans } from '@src/hooks';
import FooterLogo from 'public/images/shared/footer-logo.svg';
import ApplicationLinks from './ApplicationLinks';
import Desktop from './Desktop';
import Mobile from './Mobile';
import SocialLinks from './SocialLinks';

const Footer = () => {
    const t = useTrans();

    return (
        <footer className="bg-primary-900">
            <div className="container flex items-center justify-between gap-2 border-b border-[#ffffff0d] pb-4 pt-12">
                <ExtendedLink href="/">
                    <ExtendedImage
                        src={FooterLogo}
                        alt={t('EnterERP Site Logo')}
                        className="w-36 select-none"
                    />
                </ExtendedLink>
                <SocialLinks />
            </div>

            <section className="container py-8 text-white md:grid md:grid-cols-5 md:py-12">
                <Desktop />
                <Mobile />
                <ApplicationLinks />
            </section>

            <div className="container flex flex-wrap items-center justify-between gap-4 border-t border-[#ffffff0d] pb-12 pt-4 text-sm text-white">
                <p>© Copyright {new Date().getFullYear()} EnterERP</p>
                <div className="flex items-center gap-2.5">
                    <ExtendedLink
                        className="hover:underline"
                        href="/privacy-policy"
                    >
                        {t('Gizlilik Politikası')}
                    </ExtendedLink>
                    <ExtendedLink
                        className="hover:underline"
                        href="/personal-data"
                    >
                        {t('Kişisel Veriler')}
                    </ExtendedLink>
                    <ExtendedLink
                        className="hover:underline"
                        href="/cookie-policy"
                    >
                        {t('Çerez Politikası')}
                    </ExtendedLink>
                </div>
            </div>
        </footer>
    );
};

export default Footer;
