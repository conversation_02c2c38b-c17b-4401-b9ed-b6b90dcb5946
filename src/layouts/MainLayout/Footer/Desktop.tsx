import { ExtendedLink } from '@src/components/shared';
import { useTrans } from '@src/hooks';
import { footerItems } from './FooterItems';

const Desktop = () => {
    const t = useTrans();

    return (
        <>
            <div className="hidden md:block">
                <p className="mb-4 font-means text-xl">{t('Kurumsal')}</p>
                <ul className="grid grid-cols-2 md:grid md:grid-cols-1">
                    {footerItems.corporate.map((footerItem) => (
                        <li
                            className="my-3 text-sm last:mb-0"
                            key={footerItem.title}
                        >
                            <ExtendedLink
                                className="block hover:underline md:inline-block"
                                href={footerItem.href}
                            >
                                {t(footerItem.title)}
                            </ExtendedLink>
                        </li>
                    ))}
                </ul>
            </div>

            <div className="hidden md:block">
                <p className="mb-4 mt-12 font-means text-xl md:mt-0">
                    {t('Uygulamalar')}
                </p>
                <ul className="grid grid-cols-2 md:grid md:grid-cols-1">
                    {footerItems.apps.map((footerItem) => (
                        <li
                            className="my-3 text-sm last:mb-0"
                            key={footerItem.title}
                        >
                            <ExtendedLink
                                className="block hover:underline md:inline-block"
                                href={footerItem.href}
                            >
                                {t(footerItem.title)}
                            </ExtendedLink>
                        </li>
                    ))}
                </ul>
            </div>

            <div className="hidden md:block">
                <p className="mb-4 mt-12 font-means text-xl md:mt-0">
                    {t('Yardımcı Uygulamalar')}
                </p>
                <ul className="grid grid-cols-2 md:grid md:grid-cols-1">
                    {footerItems.helperApps.map((footerItem) => (
                        <li
                            className="my-3 text-sm last:mb-0"
                            key={footerItem.title}
                        >
                            <ExtendedLink
                                className="block hover:underline md:inline-block"
                                href={footerItem.href}
                            >
                                {t(footerItem.title)}
                            </ExtendedLink>
                        </li>
                    ))}
                </ul>
            </div>

            <div className="hidden md:block">
                <p className="mb-4 mt-12 font-means text-xl md:mt-0">
                    {t('Kaynaklar')}
                </p>
                <ul className="grid grid-cols-2 md:grid md:grid-cols-1">
                    {footerItems.resources.map((footerItem) => (
                        <li
                            className="my-3 text-sm last:mb-0"
                            key={footerItem.title}
                        >
                            <ExtendedLink
                                className="block hover:underline md:inline-block"
                                href={footerItem.href}
                            >
                                {t(footerItem.title)}
                            </ExtendedLink>
                        </li>
                    ))}
                </ul>
            </div>
        </>
    );
};

export default Desktop;
