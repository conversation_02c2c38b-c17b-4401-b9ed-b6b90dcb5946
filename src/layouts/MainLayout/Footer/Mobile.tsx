import { Disclosure } from '@headlessui/react';
import { ExtendedLink } from '@src/components/shared';
import { useTrans } from '@src/hooks';
import { MinusIcon, PlusIcon } from '@src/icons/solid';
import { footerItems } from './FooterItems';

const Mobile = () => {
    const t = useTrans();

    return (
        <div className="space-y-4 md:hidden">
            <div className="rounded-lg bg-[#041322]">
                <Disclosure>
                    {({ open }) => (
                        <>
                            <Disclosure.Button className="flex w-full items-center justify-between p-3">
                                <p className="flex-1 text-left font-means">
                                    {t('Kurumsal')}
                                </p>
                                {open ? (
                                    <MinusIcon className="h-4 w-4 text-primary-200" />
                                ) : (
                                    <PlusIcon className="h-4 w-4 text-primary-200" />
                                )}
                            </Disclosure.Button>
                            {footerItems.corporate.map((footerItem) => (
                                <Disclosure.Panel
                                    key={footerItem.href}
                                    className="px-3 py-2 last:pb-3"
                                >
                                    <Disclosure.Button
                                        as={ExtendedLink}
                                        className="py-2 text-sm"
                                        href={footerItem.href}
                                    >
                                        {t(footerItem.title)}
                                    </Disclosure.Button>
                                </Disclosure.Panel>
                            ))}
                        </>
                    )}
                </Disclosure>
            </div>

            <div className="rounded-lg bg-[#041322]">
                <Disclosure>
                    {({ open }) => (
                        <>
                            <Disclosure.Button className="flex w-full items-center justify-between p-3">
                                <p className="flex-1 text-left font-means">
                                    {t('Uygulamalar')}
                                </p>
                                {open ? (
                                    <MinusIcon className="h-4 w-4 text-primary-200" />
                                ) : (
                                    <PlusIcon className="h-4 w-4 text-primary-200" />
                                )}
                            </Disclosure.Button>
                            {footerItems.apps.map((footerItem) => (
                                <Disclosure.Panel
                                    key={footerItem.href}
                                    className="px-3 py-2 last:pb-3"
                                >
                                    <Disclosure.Button
                                        as={ExtendedLink}
                                        className="py-2 text-sm"
                                        href={footerItem.href}
                                    >
                                        {t(footerItem.title)}
                                    </Disclosure.Button>
                                </Disclosure.Panel>
                            ))}
                        </>
                    )}
                </Disclosure>
            </div>

            <div className="rounded-lg bg-[#041322]">
                <Disclosure>
                    {({ open }) => (
                        <>
                            <Disclosure.Button className="flex w-full items-center justify-between p-3">
                                <p className="flex-1 text-left font-means">
                                    {t('Yardımcı Uygulamalar')}
                                </p>
                                {open ? (
                                    <MinusIcon className="h-4 w-4 text-primary-200" />
                                ) : (
                                    <PlusIcon className="h-4 w-4 text-primary-200" />
                                )}
                            </Disclosure.Button>
                            {footerItems.helperApps.map((footerItem) => (
                                <Disclosure.Panel
                                    key={footerItem.href}
                                    className="px-3 py-2 last:pb-3"
                                >
                                    <Disclosure.Button
                                        as={ExtendedLink}
                                        className="py-2 text-sm"
                                        href={footerItem.href}
                                    >
                                        {t(footerItem.title)}
                                    </Disclosure.Button>
                                </Disclosure.Panel>
                            ))}
                        </>
                    )}
                </Disclosure>
            </div>

            <div className="rounded-lg bg-[#041322]">
                <Disclosure>
                    {({ open }) => (
                        <>
                            <Disclosure.Button className="flex w-full items-center justify-between p-3">
                                <p className="flex-1 text-left font-means">
                                    {t('Kaynaklar')}
                                </p>
                                {open ? (
                                    <MinusIcon className="h-4 w-4 text-primary-200" />
                                ) : (
                                    <PlusIcon className="h-4 w-4 text-primary-200" />
                                )}
                            </Disclosure.Button>
                            {footerItems.resources.map((footerItem) => (
                                <Disclosure.Panel
                                    key={footerItem.href}
                                    className="px-3 py-2 last:pb-3"
                                >
                                    <Disclosure.Button
                                        as={ExtendedLink}
                                        className="py-2 text-sm"
                                        href={footerItem.href}
                                    >
                                        {t(footerItem.title)}
                                    </Disclosure.Button>
                                </Disclosure.Panel>
                            ))}
                        </>
                    )}
                </Disclosure>
            </div>
        </div>
    );
};

export default Mobile;
