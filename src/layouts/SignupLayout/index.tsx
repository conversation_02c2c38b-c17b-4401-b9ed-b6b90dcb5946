import { ReactNode } from 'react';
import Footer from '../MainLayout/Footer';

interface SignupLayoutProps {
    children: ReactNode;
}

const SignupLayout = ({ children }: SignupLayoutProps) => {
    return (
        <div className="flex min-h-screen flex-col bg-primary-50">
            <main className="flex-1 border-b-4 border-primary-500">
                {children}
            </main>

            <Footer />
        </div>
    );
};

export default SignupLayout;
