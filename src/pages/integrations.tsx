import { GetStaticPropsContext } from 'next';
import { NextSeo } from 'next-seo';
import Industries from '@src/components/pages/industries';
import { useTrans } from '@src/hooks';
import { NextPageWithLayout } from '@src/interfaces';
import MainLayout from '@src/layouts/MainLayout';
import { getStaticPageProps } from '@src/utils/server';
import Integrations from '@src/components/pages/integrations';

const IntegrationsPage: NextPageWithLayout = () => {
    const t = useTrans();

    return (
        <>
            <NextSeo
                title={t('Sektörler')}
                description={t(
                    'Büyüme dostu teknoloji desteğine sahip olduğunuzda nelerin mümkün olduğunu görün.'
                )}
            />

            <Integrations />
        </>
    );
};

IntegrationsPage.PageLayout = MainLayout;

export default IntegrationsPage;

export const getStaticProps = async (ctx: GetStaticPropsContext) => {
    const props = await getStaticPageProps({
        ctx,
        translationPage: 'integrations'
    });

    return { props };
};
