import { GetStaticPropsContext } from 'next';
import { NextSeo } from 'next-seo';
import { useTrans } from '@src/hooks';
import { NextPageWithLayout } from '@src/interfaces';
import MainLayout from '@src/layouts/MainLayout';
import { getStaticPageProps } from '@src/utils/server';

const PrivacyPolicyPage: NextPageWithLayout = () => {
    const t = useTrans();

    return (
        <>
            <NextSeo
                title={t('EnterERP Gizlilik Politikası')}
                description={t(
                    'Gizliliğe son derece önem veren EnterERP; müşterilerine daha iyi ve kişiselleştirilmiş çözümler sunabilmek adına isim, e-posta vb. üyelik formunda istenen kişisel bilgilerinizi sizlerden talep etmektedir.'
                )}
            />

            <section className="spacer container grid gap-3 [&>p]:text-sm">
                <h1 className="text-lg font-semibold md:text-2xl">
                    EnterERP Gizlilik Politikası
                </h1>
                <p>
                    Gizliliğe son derece önem veren EnterERP; müşterilerine daha
                    iyi ve kişiselleştirilmiş çözümler sunabilmek adına isim,
                    e-posta vb. üyelik formunda istenen kişisel bilgilerinizi
                    sizlerden talep etmektedir. Müşterilerimize sunduğumuz veya
                    sunacak olduğumuz tüm hizmetler için yürürlükte olan
                    gizlilik sözleşmemiz doğrultusunda bu bilgiler, kanun
                    kapsamında yalnızca verilecek olan hizmet çerçevesinde olup
                    müşterilerin onayı dışında herhangi bir ticari amaçla
                    kullanılamaz veya 3. kişilerle paylaşılamaz.
                </p>
                <p className="!text-lg font-semibold">
                    EnterERP, Kişisel Verilerinizi Hangi Amaçlarla İşliyor?
                </p>
                <p>
                    Elde ettiğimiz kişisel verileriniz, tarafınızdan başvuru
                    formu gönderimi, satın alım durumunda faturalandırma veya
                    ürün teslimi ve destek süreçlerinin sürdürülmesi,
                    ilerletilmesi çerçevesinde işlenebilecektir.
                </p>
                <p>
                    EnterERP, iş yaparken ve çeşitli web varlıklarımızı ve diğer
                    iletişim kanallarımızı işletirken, iş ortakları ve
                    etkileşimde bulunduğumuz diğer kişiler dahil olmak üzere
                    etkileşim kurduğu kişilerin kişisel verilerini işler. Bu
                    durumlar çerçevesinde EnterERP, kişisel verileri aşağıdaki
                    iş amaçlarından biri veya birkaçı için kullanabilir:
                </p>
                <p className="!text-lg font-semibold">
                    İş ortakları ve diğerleriyle iş ilişkileri sürdürmek
                </p>
                <p>
                    EnterERP, sözleşme öncesi ve sözleşme sonucunda iş
                    ilişkilerini yerine getirmek için iş ortakları ve diğer
                    kullanıcılarla iş ilişkilerinin devamlılığını sağlamak adına
                    kişisel verileri işler. Bu, iş ilişkilerimizi kurmak, yerine
                    getirmek ve sürdürmek için istekleri yerine getirmeyi,
                    siparişleri işleme koymayı, sipariş edilen bir ürün veya
                    hizmeti teslim etmeyi veya başka herhangi bir ilgili eylemde
                    olmayı içerebilir. Kurumsal bir müşteri adına EnterERP ürün
                    veya hizmet satın aldığınızda veya almayı planladığınızda
                    veya EnterERP ile kurumsal bir müşteri veya iş ortağı
                    arasındaki iş ilişkisinde (Müşteri İrtibatı) farklı bir
                    şekilde ilgili kişi olarak ilişkili olduğunuzda, EnterERP
                    kişisel bilgilerinizi kullanacaktır. Sizin veya
                    işvereninizin ürünlerimizi veya hizmetlerimizi kullanması
                    bağlamında EnterERP, sizin, bir kullanıcının veya bir
                    müşterinin soru veya şikayetini çözmek için sizinle posta,
                    e-posta, canlı sohbet, iletişim formları, telefon veya başka
                    herhangi bir ortam aracılığıyla şüpheli işlemleri araştırmak
                    için iletişim kurabilir. Telefon aramaları veya sohbet
                    oturumları söz konusu olduğunda, EnterERP, söz konusu arama
                    sırasında sizi uygun şekilde bilgilendirdikten ve geçerli
                    yasalara uyarak, kayıt başlamadan daha öncesinde onayınızı
                    aldıktan sonra, EnterERP hizmetlerinin kalitesini yüksek
                    oranda iyileştirmek için bu tür aramaları veya sohbet
                    oturumlarını kaydedebilir.
                </p>
                <p className="!text-lg font-semibold">
                    EnterERP Yasa ve Yönetmeliklere Uygun olanı yapmak için
                    Kişisel verilerinizi işler.
                </p>
                <p>
                    Şirket, Veri Sahibi tarafından sağlanan kişisel verileri,
                    Veri Sahibi’nin Site üzerinden sağlanan hizmetlerden
                    faydalanması da dahil olmak üzere Şirket tarafından sunulan
                    ürün ve hizmetlerden ilgili kişileri faydalandırmak için
                    gerekli çalışmaların iş birimleri tarafından yapılması ve
                    ilgili iş süreçlerinin yürütülmesi ile bu ürün ve
                    hizmetlerin ilgili kişilerin beğeni, kullanım alışkanlıkları
                    ve ihtiyaçlarına göre özelleştirilerek ilgili kişilere
                    önerilmesi ve tanıtılması için gerekli olan aktivitelerin
                    planlanması ve icrası, Şirket tarafından yürütülen ticari
                    faaliyetlerin gerçekleştirilmesi için ilgili iş birimleri
                    tarafından gerekli çalışmaların yapılması ve buna bağlı iş
                    süreçlerinin yürütülmesi, Şirket ve iş ilişkisi içerisinde
                    bulunduğu kişilerin hukuki, teknik ve ticari-iş güvenliğinin
                    temini ile Şirket’in ticari ve/veya iş stratejilerinin
                    planlanması ve icrası amaçlarıyla işlenebilecektir. Şirket,
                    Kişisel Verilerin Korunması Kanunu’nun 5 ve 8. maddeleri
                    uyarınca ve/veya ilgili mevzuattaki istisnaların varlığı
                    halinde kişisel verileri Veri Sahibi’nin ayrıca rızasını
                    almaksızın işleyebilecek ve üçüncü kişilerle
                    paylaşabilecektir. Bu durumların başlıcaları aşağıda
                    belirtilmiştir:
                </p>
                <ul className="grid gap-3 text-sm">
                    <li>• Kanunlarda açıkça öngörülmesi,</li>
                    <li>
                        • Fiili imkânsızlık nedeniyle rızasını açıklayamayacak
                        durumda bulunan veya rızasına hukuki geçerlilik
                        tanınmayan kişinin kendisinin ya da bir başkasının
                        hayatı veya beden bütünlüğünün korunması için zorunlu
                        olması,
                    </li>
                    <li>
                        • Veri Sahibi ile Şirket arasında herhangi bir
                        sözleşmenin kurulması veya ifasıyla doğrudan doğruya
                        ilgili olması kaydıyla, kişisel verilerin işlenmesinin
                        gerekli olması,
                    </li>
                    <li>
                        • Şirketin hukuki yükümlülüklerini yerine getirebilmesi
                        için zorunlu olması,
                    </li>
                    <li>
                        • Veri Sahibi’nin kendisi tarafından alenileştirilmiş
                        olması,
                    </li>
                    <li>
                        • Bir hakkın tesisi, kullanılması veya korunması için
                        veri işlemenin zorunlu olması,
                    </li>
                    <li>
                        • Veri Sahibi’nin temel hak ve özgürlüklerine zarar
                        vermemek kaydıyla, Şirket’in meşru menfaatleri için veri
                        işlenmesinin zorunlu olması.
                    </li>
                </ul>
                <p className="!text-lg font-semibold">
                    Verilere Kimler Erişebilmektedir?
                </p>
                <p>
                    Şirket, Veri Sahibi’ne ait kişisel verileri ve bu kişisel
                    verileri kullanılarak elde ettiği yeni verileri, işbu
                    Gizlilik Politikası ile belirlenen amaçların
                    gerçekleştirilebilmesi için Şirket’in hizmetlerinden
                    faydalandığı üçüncü kişilere, söz konusu hizmetlerin temini
                    amacıyla sınırlı olmak üzere aktarılabilecektir. Şirket,
                    Veri Sahibi deneyiminin geliştirilmesi (iyileştirme ve
                    kişiselleştirme dâhil), Veri Sahibi’nin güvenliğini
                    sağlamak, hileli ya da izinsiz kullanımları tespit etmek,
                    operasyonel değerlendirme araştırılması, Site hizmetlerine
                    ilişkin hataların giderilmesi ve işbu Gizlilik
                    Politikası’nda yer alan amaçlardan herhangi birisini
                    gerçekleştirebilmek için SMS gönderimi yapanlar da dahil
                    olmak üzere dış kaynak hizmet sağlayıcıları, barındırma
                    hizmet sağlayıcıları (hosting servisleri), hukuk büroları,
                    araştırma şirketleri, çağrı merkezleri gibi üçüncü kişiler
                    ile paylaşabilecektir. Veri Sahibi, yukarıda belirtilen
                    amaçlarla sınırlı olmak kaydı ile bahsi geçen üçüncü
                    tarafların Veri Sahibi’nin kişisel verilerini dünyanın her
                    yerindeki sunucularında saklayabileceğini, bu hususa peşinen
                    muvafakat ettiğini kabul eder.
                </p>

                <p className="!text-lg font-semibold">
                    Verilere Erişim Hakkı ve Düzeltme Talepleri Hakkında
                </p>
                <p>Kanun’un 11. maddesi uyarınca veri sahipleri,</p>
                <ul className="grid gap-3 text-sm">
                    <li>• Kişisel veri işlenip işlenmediğini öğrenme,</li>
                    <li>
                        • Kişisel verileri işlenmişse buna ilişkin bilgi talep
                        etme,
                    </li>
                    <li>
                        • Kişisel verilerin işlenme amacını ve bunların amacına
                        uygun kullanılıp kullanılmadığını öğrenme,
                    </li>
                    <li>
                        • Yurt içinde veya yurt dışında kişisel verilerin
                        aktarıldığı üçüncü kişileri bilme,
                    </li>
                    <li>
                        • Kişisel verilerin eksik veya yanlış işlenmiş olması
                        hâlinde bunların düzeltilmesini isteme ve bu kapsamda
                        yapılan işlemin kişisel verilerin aktarıldığı üçüncü
                        kişilere bildirilmesini isteme,
                    </li>
                    <li>
                        • Kanun ve ilgili diğer kanun hükümlerine uygun olarak
                        işlenmiş olmasına rağmen, işlenmesini gerektiren
                        sebeplerin ortadan kalkması hâlinde kişisel verilerin
                        silinmesini veya yok edilmesini isteme ve bu kapsamda
                        yapılan işlemin kişisel verilerin aktarıldığı üçüncü
                        kişilere bildirilmesini isteme,
                    </li>
                    <li>
                        • İşlenen verilerin münhasıran otomatik sistemler
                        vasıtasıyla analiz edilmesi suretiyle kişinin kendisi
                        aleyhine bir sonucun ortaya çıkmasına itiraz etme,
                    </li>
                    <li>
                        • Kişisel verilerin kanuna aykırı olarak işlenmesi
                        sebebiyle zarara uğraması hâlinde zararın giderilmesini
                        talep etme haklarına sahiptir.
                    </li>
                </ul>
            </section>
        </>
    );
};

PrivacyPolicyPage.PageLayout = MainLayout;

export default PrivacyPolicyPage;

export const getStaticProps = async (ctx: GetStaticPropsContext) => {
    const props = await getStaticPageProps({
        ctx,
        translationPage: 'privacy-policy'
    });

    return { props };
};
