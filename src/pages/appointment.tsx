import { GetStaticPropsContext } from 'next';
import { NextSeo } from 'next-seo';
import Appointment from '@src/components/pages/appointment';
import { useTrans } from '@src/hooks';
import { NextPageWithLayout } from '@src/interfaces';
import SignupLayout from '@src/layouts/SignupLayout';
import { getStaticPageProps } from '@src/utils/server';

const AppointmentPage: NextPageWithLayout = () => {
    const t = useTrans();

    return (
        <>
            <NextSeo
                title={t('Randevu Talep Et')}
                description={t(
                    'Ücretsiz deneme için bir randevu planlayın, EnterERP ekibi sizinle iletişime geçsin. 7 gün boyunca kurumsal kaynak planlama otomasyonunun gücünü kullanın.'
                )}
            />

            <Appointment />
        </>
    );
};

AppointmentPage.PageLayout = SignupLayout;

export default AppointmentPage;

export const getStaticProps = async (ctx: GetStaticPropsContext) => {
    const props = await getStaticPageProps({
        ctx,
        translationPage: 'appointment'
    });

    return { props };
};
