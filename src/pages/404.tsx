import { GetStaticPropsContext } from 'next';
import { useLottie, useTrans } from '@src/hooks';
import { NextPageWithLayout } from '@src/interfaces';
import MainLayout from '@src/layouts/MainLayout';
import { getStaticPageProps } from '@src/utils/server';

const NotFoundPage: NextPageWithLayout = () => {
    const { animationRef, observerRef } = useLottie({
        path: '/animations/404.json',
        autoplay: true,
        loop: true
    });

    const t = useTrans();

    return (
        <div ref={observerRef}>
            <div className="container aspect-1 max-w-3xl" ref={animationRef} />
            <h1 className="pb-12 text-center text-lg md:text-2xl">
                {t('Hata! Aradığınız sayfayı bulamıyoruz.')}
            </h1>
        </div>
    );
};

NotFoundPage.PageLayout = MainLayout;

export default NotFoundPage;

export const getStaticProps = async (ctx: GetStaticPropsContext) => {
    const props = await getStaticPageProps({ ctx });

    return { props };
};
