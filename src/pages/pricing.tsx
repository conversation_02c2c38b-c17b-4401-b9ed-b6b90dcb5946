import { GetStaticPropsContext } from 'next';
import { NextSeo } from 'next-seo';
import PricingHero from '@src/components/pages/pricing/PricingHero';
import SpecSection from '@src/components/pages/pricing/SpecSection';
import { DemoSection } from '@src/components/shared';
import { useTrans } from '@src/hooks';
import { NextPageWithLayout } from '@src/interfaces';
import MainLayout from '@src/layouts/MainLayout';
import { getStaticPageProps } from '@src/utils/server';

const PricingPage: NextPageWithLayout = () => {
    const t = useTrans();

    return (
        <>
            <NextSeo
                title={t('Ürünler')}
                description={t('İşletmeler için yeni nesil işletim sistemi.')}
            />

            <PricingHero />
            <DemoSection variant="blue" />
            <SpecSection />
        </>
    );
};

PricingPage.PageLayout = MainLayout;

export default PricingPage;

export const getStaticProps = async (ctx: GetStaticPropsContext) => {
    const props = await getStaticPageProps({ ctx, translationPage: 'pricing' });

    return { props };
};
