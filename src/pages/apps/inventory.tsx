import { GetStaticPropsContext } from 'next';
import { NextSeo } from 'next-seo';
import Inventory from '@src/components/pages/apps/inventory';
import { useTrans } from '@src/hooks';
import { NextPageWithLayout } from '@src/interfaces';
import MainLayout from '@src/layouts/MainLayout';
import { getStaticPageProps } from '@src/utils/server';

const InventoryPage: NextPageWithLayout = () => {
    const t = useTrans();

    return (
        <>
            <NextSeo
                title={t('Envanter Yönetimi')}
                description={t(
                    'Bulut tabanlı envanter yönetimi ya<PERSON>ılımımız, büyüyen işletmelerin günümüz piyasasının hızında çalışmasına yardımcı olur.'
                )}
            />

            <Inventory />
        </>
    );
};

InventoryPage.PageLayout = MainLayout;

export default InventoryPage;

export const getStaticProps = async (ctx: GetStaticPropsContext) => {
    const props = await getStaticPageProps({
        ctx,
        translationPage: 'inventory'
    });

    return { props };
};
