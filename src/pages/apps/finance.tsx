import { GetStaticPropsContext } from 'next';
import { NextSeo } from 'next-seo';
import Finance from '@src/components/pages/apps/finance';
import { useTrans } from '@src/hooks';
import { NextPageWithLayout } from '@src/interfaces';
import MainLayout from '@src/layouts/MainLayout';
import { getStaticPageProps } from '@src/utils/server';

const FinancePage: NextPageWithLayout = () => {
    const t = useTrans();

    return (
        <>
            <NextSeo
                title={t('Finans Yönetimi')}
                description={t(
                    'Paranızın tam olarak nerede olduğunu, nereye gittiğini görün ve ihtiyacınıza göre işletme finansmanını planlayın.'
                )}
            />

            <Finance />
        </>
    );
};

FinancePage.PageLayout = MainLayout;

export default FinancePage;

export const getStaticProps = async (ctx: GetStaticPropsContext) => {
    const props = await getStaticPageProps({ ctx, translationPage: 'finance' });

    return { props };
};
