import { GetStaticPropsContext } from 'next';
import { NextSeo } from 'next-seo';
import SupplyManagement from '@src/components/pages/apps/supply-management';
import { useTrans } from '@src/hooks';
import { NextPageWithLayout } from '@src/interfaces';
import MainLayout from '@src/layouts/MainLayout';
import { getStaticPageProps } from '@src/utils/server';

const SupplyManagementPage: NextPageWithLayout = () => {
    const t = useTrans();

    return (
        <>
            <NextSeo
                title={t('Tedarik Yönetimi')}
                description={t(
                    '<PERSON><PERSON>k fiyatların, onaylı tedarikçilerin, ve tedarik kataloglarının peşine düşmenize gerek yok, bunların tümü EnterERP yazılımıyla doğru çalışır.'
                )}
            />

            <SupplyManagement />
        </>
    );
};

SupplyManagementPage.PageLayout = MainLayout;

export default SupplyManagementPage;

export const getStaticProps = async (ctx: GetStaticPropsContext) => {
    const props = await getStaticPageProps({
        ctx,
        translationPage: 'supply-management'
    });

    return { props };
};
