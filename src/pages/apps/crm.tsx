import { GetStaticPropsContext } from 'next';
import { NextSeo } from 'next-seo';
import CRM from '@src/components/pages/apps/crm';
import { useTrans } from '@src/hooks';
import { NextPageWithLayout } from '@src/interfaces';
import MainLayout from '@src/layouts/MainLayout';
import { getStaticPageProps } from '@src/utils/server';

const CRMPage: NextPageWithLayout = () => {
    const t = useTrans();

    return (
        <>
            <NextSeo
                title={t('Müşteri İlişkileri Yönetimi (CRM)')}
                description={t(
                    'Potansiyel müşterileriniz, ortaklarınız ve satıcılarınız için tamamen entegre bir sistem oluşturun.'
                )}
            />

            <CRM />
        </>
    );
};

CRMPage.PageLayout = MainLayout;

export default CRMPage;

export const getStaticProps = async (ctx: GetStaticPropsContext) => {
    const props = await getStaticPageProps({ ctx, translationPage: 'crm' });

    return { props };
};
