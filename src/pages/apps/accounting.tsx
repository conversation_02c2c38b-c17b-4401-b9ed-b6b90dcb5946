import { GetStaticPropsContext } from 'next';
import { NextSeo } from 'next-seo';
import Accounting from '@src/components/pages/apps/accounting';
import { useTrans } from '@src/hooks';
import { NextPageWithLayout } from '@src/interfaces';
import MainLayout from '@src/layouts/MainLayout';
import { getStaticPageProps } from '@src/utils/server';

const AccountingPage: NextPageWithLayout = () => {
    const t = useTrans();

    return (
        <>
            <NextSeo
                title={t('Muhasebe Yönetimi')}
                description={t(
                    'Mali süreçlerinizi yönetirken karmaşık yapılardan, sizi yavaşlatan sistemlerden kurtulun ve ihtiyacınız olanı yeni nesil bir muhasebe sisteminde deyimleyin.'
                )}
            />

            <Accounting />
        </>
    );
};

AccountingPage.PageLayout = MainLayout;

export default AccountingPage;

export const getStaticProps = async (ctx: GetStaticPropsContext) => {
    const props = await getStaticPageProps({
        ctx,
        translationPage: 'accounting'
    });

    return { props };
};
