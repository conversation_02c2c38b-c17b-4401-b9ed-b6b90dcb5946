import { GetStaticPropsContext } from 'next';
import { NextSeo } from 'next-seo';
import ProjectManagement from '@src/components/pages/apps/project-management';
import { useTrans } from '@src/hooks';
import { NextPageWithLayout } from '@src/interfaces';
import MainLayout from '@src/layouts/MainLayout';
import { getStaticPageProps } from '@src/utils/server';

const ProjectManagementPage: NextPageWithLayout = () => {
    const t = useTrans();

    return (
        <>
            <NextSeo
                title={t('Proje Yönetimi')}
                description={t(
                    'İş süreçlerine tamamen entegre sözleşme ve proje yönetimi yeteneklerinden hızla yararlanın.'
                )}
            />

            <ProjectManagement />
        </>
    );
};

ProjectManagementPage.PageLayout = MainLayout;

export default ProjectManagementPage;

export const getStaticProps = async (ctx: GetStaticPropsContext) => {
    const props = await getStaticPageProps({
        ctx,
        translationPage: 'project-management'
    });

    return { props };
};
