import { GetStaticPropsContext } from 'next';
import { NextSeo } from 'next-seo';
import HelpDesk from '@src/components/pages/apps/help-desk';
import { useTrans } from '@src/hooks';
import { NextPageWithLayout } from '@src/interfaces';
import MainLayout from '@src/layouts/MainLayout';
import { getStaticPageProps } from '@src/utils/server';

const HelpDeskPage: NextPageWithLayout = () => {
    const t = useTrans();

    return (
        <>
            <NextSeo
                title={t('Yardım Masası')}
                description={t(
                    'Destek sistemi ile şirket içi herhangi bir konudaki problemleri, şikayetleri ve istekleri hızlıca çözün.'
                )}
            />

            <HelpDesk />
        </>
    );
};

HelpDeskPage.PageLayout = MainLayout;

export default HelpDeskPage;

export const getStaticProps = async (ctx: GetStaticPropsContext) => {
    const props = await getStaticPageProps({
        ctx,
        translationPage: 'help-desk'
    });

    return { props };
};
