import { GetStaticPropsContext } from 'next';
import { NextSeo } from 'next-seo';
import ESolutions from '@src/components/pages/apps/e-solutions/home';
import { useTrans } from '@src/hooks';
import { NextPageWithLayout } from '@src/interfaces';
import MainLayout from '@src/layouts/MainLayout';
import { getStaticPageProps } from '@src/utils/server';

const ESolutionsPage: NextPageWithLayout = () => {
    const t = useTrans();

    return (
        <>
            <NextSeo
                title={t('e-Çözümler')}
                description={t(
                    'Belgelerinizi EnterERP üzerinden e-Çözümler sistemi ile dijitale geçirerek zaman ve maliyet kaybını düşürün.'
                )}
            />

            <ESolutions />
        </>
    );
};

ESolutionsPage.PageLayout = MainLayout;

export default ESolutionsPage;

export const getStaticProps = async (ctx: GetStaticPropsContext) => {
    const props = await getStaticPageProps({
        ctx,
        translationPage: 'e-solutions'
    });

    return { props };
};
