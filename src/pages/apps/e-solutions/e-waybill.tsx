import { GetStaticPropsContext } from 'next';
import { NextSeo } from 'next-seo';
import EWaybill from '@src/components/pages/apps/e-solutions/e-waybill';
import { useTrans } from '@src/hooks';
import { NextPageWithLayout } from '@src/interfaces';
import MainLayout from '@src/layouts/MainLayout';
import { getStaticPageProps } from '@src/utils/server';

const EWaybillPage: NextPageWithLayout = () => {
    const t = useTrans();

    return (
        <>
            <NextSeo
                title={t('e-İrsaliye')}
                description={t(
                    'Faturalarınızı EnterERP üzerinden e-İrsaliye sistemi ile dijitale geçirerek zaman ve maliyet kaybını düşürün.'
                )}
            />

            <EWaybill />
        </>
    );
};

EWaybillPage.PageLayout = MainLayout;

export default EWaybillPage;

export const getStaticProps = async (ctx: GetStaticPropsContext) => {
    const props = await getStaticPageProps({ ctx });

    return { props };
};
