import { GetStaticPropsContext } from 'next';
import { NextSeo } from 'next-seo';
import EArchive from '@src/components/pages/apps/e-solutions/e-archive';
import { useTrans } from '@src/hooks';
import { NextPageWithLayout } from '@src/interfaces';
import MainLayout from '@src/layouts/MainLayout';
import { getStaticPageProps } from '@src/utils/server';

const EArchivePage: NextPageWithLayout = () => {
    const t = useTrans();

    return (
        <>
            <NextSeo
                title={t('e-Arşiv')}
                description={t(
                    'Faturalarınızı EnterERP üzerinden e-Arşiv sistemi ile dijitale geçirerek zaman ve maliyet kaybını düşürün.'
                )}
            />

            <EArchive />
        </>
    );
};

EArchivePage.PageLayout = MainLayout;

export default EArchivePage;

export const getStaticProps = async (ctx: GetStaticPropsContext) => {
    const props = await getStaticPageProps({ ctx });

    return { props };
};
