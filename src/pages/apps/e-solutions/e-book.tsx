import { GetStaticPropsContext } from 'next';
import { NextSeo } from 'next-seo';
import EBook from '@src/components/pages/apps/e-solutions/e-book';
import { useTrans } from '@src/hooks';
import { NextPageWithLayout } from '@src/interfaces';
import MainLayout from '@src/layouts/MainLayout';
import { getStaticPageProps } from '@src/utils/server';

const EBookPage: NextPageWithLayout = () => {
    const t = useTrans();

    return (
        <>
            <NextSeo
                title={t('e-Defter')}
                description={t(
                    'Faturalarınızı EnterERP üzerinden e-Defter sistemi ile dijitale geçirerek zaman ve maliyet kaybını düşürün.'
                )}
            />

            <EBook />
        </>
    );
};

EBookPage.PageLayout = MainLayout;

export default EBookPage;

export const getStaticProps = async (ctx: GetStaticPropsContext) => {
    const props = await getStaticPageProps({ ctx });

    return { props };
};
