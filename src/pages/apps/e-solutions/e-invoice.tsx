import { GetStaticPropsContext } from 'next';
import { NextSeo } from 'next-seo';
import EInvoice from '@src/components/pages/apps/e-solutions/e-invoice';
import { useTrans } from '@src/hooks';
import { NextPageWithLayout } from '@src/interfaces';
import MainLayout from '@src/layouts/MainLayout';
import { getStaticPageProps } from '@src/utils/server';

const EInvoicePage: NextPageWithLayout = () => {
    const t = useTrans();

    return (
        <>
            <NextSeo
                title={t('e-Fatura')}
                description={t(
                    'Faturalarınızı EnterERP üzerinden e-Fatura sistemi ile dijitale geçirerek zaman ve maliyet kaybını düşürün.'
                )}
            />

            <EInvoice />
        </>
    );
};

EInvoicePage.PageLayout = MainLayout;

export default EInvoicePage;

export const getStaticProps = async (ctx: GetStaticPropsContext) => {
    const props = await getStaticPageProps({ ctx });

    return { props };
};
