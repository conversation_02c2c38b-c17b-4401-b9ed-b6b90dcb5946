import { GetStaticPropsContext } from 'next';
import { NextSeo } from 'next-seo';
import HealthSolution from '@src/components/pages/apps/health-solution';
import { useTrans } from '@src/hooks';
import { NextPageWithLayout } from '@src/interfaces';
import MainLayout from '@src/layouts/MainLayout';
import { getStaticPageProps } from '@src/utils/server';

const HealthSolutionPage: NextPageWithLayout = () => {
    const t = useTrans();

    return (
        <>
            <NextSeo
                title={t('Sağlık Çözümleri')}
                description={t(
                    'Enter sağlık çözümleri ile ilaç ve itriyat ürünleriniz için üretim yapabilir depolayabilir veya ecza deponuzu yönetin.'
                )}
            />

            <HealthSolution />
        </>
    );
};

HealthSolutionPage.PageLayout = MainLayout;

export default HealthSolutionPage;

export const getStaticProps = async (ctx: GetStaticPropsContext) => {
    const props = await getStaticPageProps({
        ctx,
        translationPage: 'health-solution'
    });

    return { props };
};
