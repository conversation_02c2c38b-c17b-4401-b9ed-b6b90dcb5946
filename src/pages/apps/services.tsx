import { GetStaticPropsContext } from 'next';
import { NextSeo } from 'next-seo';
import Services from '@src/components/pages/apps/services';
import { useTrans } from '@src/hooks';
import { NextPageWithLayout } from '@src/interfaces';
import MainLayout from '@src/layouts/MainLayout';
import { getStaticPageProps } from '@src/utils/server';

const ServicesPage: NextPageWithLayout = () => {
    const t = useTrans();

    return (
        <>
            <NextSeo
                title={t('Saha Servis Yönetimi')}
                description={t(
                    'Üretkenliği artırmak ve müşteri beklentilerini karşılamak için arka uç operasyonlarını sahaya entegre edin.'
                )}
            />

            <Services />
        </>
    );
};

ServicesPage.PageLayout = MainLayout;

export default ServicesPage;

export const getStaticProps = async (ctx: GetStaticPropsContext) => {
    const props = await getStaticPageProps({
        ctx,
        translationPage: 'services'
    });

    return { props };
};
