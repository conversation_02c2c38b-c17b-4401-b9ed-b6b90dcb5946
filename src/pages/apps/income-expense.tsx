import { GetStaticPropsContext } from 'next';
import { NextSeo } from 'next-seo';
import IncomeExpense from '@src/components/pages/apps/income-expense';
import { useTrans } from '@src/hooks';
import { NextPageWithLayout } from '@src/interfaces';
import MainLayout from '@src/layouts/MainLayout';
import { getStaticPageProps } from '@src/utils/server';

const IncomeExpensePage: NextPageWithLayout = () => {
    const t = useTrans();

    return (
        <>
            <NextSeo
                title={t('Gelir & Gider Yönetimi')}
                description={t(
                    'Faaliyet dışı gelir tanımlayın ve tahsil edin. Masraflarınızı zaman ve kategori bazında kontrol altına alın.'
                )}
            />

            <IncomeExpense />
        </>
    );
};

IncomeExpensePage.PageLayout = MainLayout;

export default IncomeExpensePage;

export const getStaticProps = async (ctx: GetStaticPropsContext) => {
    const props = await getStaticPageProps({
        ctx,
        translationPage: 'income-expense'
    });

    return { props };
};
