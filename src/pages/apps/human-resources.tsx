import { GetStaticPropsContext } from 'next';
import { NextSeo } from 'next-seo';
import HumanResources from '@src/components/pages/apps/human-resources';
import { useTrans } from '@src/hooks';
import { NextPageWithLayout } from '@src/interfaces';
import MainLayout from '@src/layouts/MainLayout';
import { getStaticPageProps } from '@src/utils/server';

const HumanResourcesPage: NextPageWithLayout = () => {
    const t = useTrans();

    return (
        <>
            <NextSeo
                title={t('İnsan Kaynakları Yönetimi')}
                description={t(
                    '<PERSON><PERSON><PERSON> alım, listeleme, zaman çizelgeleri ve maaş bordrosunu tek bir kusursuz çözümde bir araya getirin.'
                )}
            />

            <HumanResources />
        </>
    );
};

HumanResourcesPage.PageLayout = MainLayout;

export default HumanResourcesPage;

export const getStaticProps = async (ctx: GetStaticPropsContext) => {
    const props = await getStaticPageProps({
        ctx,
        translationPage: 'human-resources'
    });

    return { props };
};
