import { GetStaticPropsContext } from 'next';
import { NextSeo } from 'next-seo';
import Sales from '@src/components/pages/apps/sales';
import { useTrans } from '@src/hooks';
import { NextPageWithLayout } from '@src/interfaces';
import MainLayout from '@src/layouts/MainLayout';
import { getStaticPageProps } from '@src/utils/server';

const SalesPage: NextPageWithLayout = () => {
    const t = useTrans();

    return (
        <>
            <NextSeo
                title={t('Satış Yönetimi')}
                description={t(
                    'EnterERP’nin birleştirilmiş ERP ve CRM platformu, ekibinizle ilgili içgörülere gerçek zamanlı satış alt yapısı sunar.'
                )}
            />

            <Sales />
        </>
    );
};

SalesPage.PageLayout = MainLayout;

export default SalesPage;

export const getStaticProps = async (ctx: GetStaticPropsContext) => {
    const props = await getStaticPageProps({ ctx, translationPage: 'sales' });

    return { props };
};
