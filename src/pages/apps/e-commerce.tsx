import { GetStaticPropsContext } from 'next';
import { NextSeo } from 'next-seo';
import ECommerce from '@src/components/pages/apps/e-commerce';
import { useTrans } from '@src/hooks';
import { NextPageWithLayout } from '@src/interfaces';
import MainLayout from '@src/layouts/MainLayout';
import { getStaticPageProps } from '@src/utils/server';

const ECommercePage: NextPageWithLayout = () => {
    const t = useTrans();

    return (
        <>
            <NextSeo
                title={t('e-Ticaret ve e-İhracat Yönetimi (EnterStore)')}
                description={t(
                    'Ürünlerinizi müşterilere ulaştırmak için ihtiyaç duyduğunuz pazarlama araçları, pazaryeri entegrasyonları ve satış kanallarıyla dünya çapında satış yapın.'
                )}
            />

            <ECommerce />
        </>
    );
};

ECommercePage.PageLayout = MainLayout;

export default ECommercePage;

export const getStaticProps = async (ctx: GetStaticPropsContext) => {
    const props = await getStaticPageProps({
        ctx,
        translationPage: 'e-commerce'
    });

    return { props };
};
