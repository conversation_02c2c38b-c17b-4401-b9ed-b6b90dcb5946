import { GetStaticPropsContext } from 'next';
import { NextSeo } from 'next-seo';
import Contact from '@src/components/pages/contact';
import { useTrans } from '@src/hooks';
import { NextPageWithLayout } from '@src/interfaces';
import MainLayout from '@src/layouts/MainLayout';
import { getStaticPageProps } from '@src/utils/server';

const ContactPage: NextPageWithLayout = () => {
    const t = useTrans();

    return (
        <>
            <NextSeo
                title={t('İletişim')}
                description={t(
                    'Yardım etmek için buradayız. Satış, destek veya teknik ekibimizle iletişime geçin ve nasıl yardımcı olabileceğimizi bize bildirin.'
                )}
            />

            <Contact />
        </>
    );
};

ContactPage.PageLayout = MainLayout;

export default ContactPage;

export const getStaticProps = async (ctx: GetStaticPropsContext) => {
    const props = await getStaticPageProps({ ctx, translationPage: 'contact' });

    return { props };
};
