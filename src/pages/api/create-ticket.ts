import type { NextApiRequest, NextApiResponse } from 'next';
import { applyRateLimit, fetcher, sendMail, sendSms } from '@src/utils/server';
import { FormRequestDto } from '@src/interfaces';

export default async function handler(
    req: NextApiRequest,
    res: NextApiResponse
) {
    if (req.method !== 'POST') {
        return res.status(404).json({
            message: 'Only POST method is allowed for this endpoint!'
        });
    }

    try {
        await applyRateLimit(req, res);
    } catch {
        return res.status(429).send('Too many requests!');
    }

    try {
        const payload = req.body as FormRequestDto;

        payload.name = (payload.name ?? '').trim();
        payload.email = (payload.email ?? '').replace(/ /g, '').toLowerCase();
        payload.phone = (payload.phone ?? '').trim();
        payload.message = (payload.message ?? '').trim();

        sendMail(payload);

        sendSms(payload);

        const result = await fetcher({
            url: '/help-desk/create-ticket',
            method: 'POST',
            payload: {
                name: payload.name,
                email: payload.email,
                phone: payload.phone,
                message: payload.message,
                extra: payload.extra
            }
        });

        if (result.status === 'error') {
            console.error('Error while creating ticket: ', result.message);
            console.error(payload);
            console.error('----------------------------');

            return res.status(400).send('Unprocessable!');
        }

        return res.status(200).json(result);
    } catch (error: any) {
        console.error('Error while creating ticket: ', error.message);
        console.error(req.body);
        console.error('----------------------------');

        return res.status(400).send('Unprocessable!');
    }
}
