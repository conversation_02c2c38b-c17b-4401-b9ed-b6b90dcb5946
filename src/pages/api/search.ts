import type { NextApiRequest, NextApiResponse } from 'next';
import { fetcher } from '@src/utils/server';

export default async function handler(
    req: NextApiRequest,
    res: NextApiResponse
) {
    if (req.method !== 'POST') {
        return res
            .status(404)
            .json({ message: 'Only POST method is allowed for this endpoint' });
    }

    const result = await fetcher({
        url: '/blog/posts',
        method: 'POST',
        payload: { search: req.body }
    });

    res.status(200).json(result);
}
