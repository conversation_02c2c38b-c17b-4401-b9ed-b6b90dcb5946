import { useMemo } from 'react';
import { useRouter } from 'next/router';
import { GetStaticPropsContext } from 'next';
import { ArticleJsonLd, NextSeo } from 'next-seo';
import { featureFlags } from 'app.config';
import { useTrans } from '@src/hooks';
import { cleanHtml } from '@src/utils';
import { BlogProvider } from '@src/context/BlogContext';
import { NextPageWithLayout, Tag } from '@src/interfaces';
import { getStaticPageProps, processParams } from '@src/utils/server';
import MainLayout from '@src/layouts/MainLayout';
import BlogContent from '@src/components/pages/blog/content';
import Blog from '@src/components/pages/blog/home';
import {
    getBlogPost,
    getBlogPosts,
    getCategories,
    getPaginatedPosts,
    getTags
} from '@src/services/blog';

const BlogPage: NextPageWithLayout = ({
    blogPost,
    pageType,
    blogPosts,
    categories,
    featuredBlogPosts,
    popularBlogPosts,
    tags,
    totalBlogPostCount,
    pageHeading,
    pageDescription,
    prevPageURL,
    nextPageURL,
    pageNumber
}: any) => {
    const t = useTrans();
    const router = useRouter();

    const seo = useMemo(() => {
        const seo: Record<string, any> = {};

        if (pageNumber && pageHeading) {
            seo.title = `Sayfa ${pageNumber + 1} - ${pageHeading}`;
        } else if (pageHeading) {
            seo.title = pageHeading;
        } else if (pageNumber) {
            seo.title = `Sayfa ${pageNumber + 1} - Blog`;
        } else {
            seo.title = 'Blog';
        }

        seo.description =
            pageDescription ||
            "İşletmenizin boyutu ne olursa olsun veya iş yolculuğunuzun hangi aşamasında olursanız olun, işinizi güvenle başlatmak, yürütmek ve büyütmek için ihtiyacınız olan kaynakları EnterERP Blog'da bulacaksınız";

        seo.additionalLinkTags = [
            {
                ...(prevPageURL && {
                    rel: 'prev',
                    href: prevPageURL
                })
            },
            {
                ...(nextPageURL && {
                    rel: 'next',
                    href: nextPageURL
                })
            }
        ];

        return seo;
        // eslint-disable-next-line
    }, [pageDescription, pageHeading, pageNumber, prevPageURL, nextPageURL]);

    if (pageType === 'content') {
        return (
            <>
                <NextSeo
                    title={t(blogPost.title)}
                    description={t(cleanHtml(blogPost.shortContent))}
                    openGraph={{
                        title: t(blogPost.title),
                        description: t(cleanHtml(blogPost.shortContent)),
                        type: 'article',
                        article: {
                            publishedTime: blogPost.postedOn,
                            modifiedTime: blogPost.updatedAt,
                            authors: [blogPost.author.name],
                            section:
                                Array.isArray(blogPost.subTitles) &&
                                blogPost.subTitles.length > 0
                                    ? blogPost.subTitles[0]?.title
                                    : '',
                            tags: blogPost.tags?.map((tag: Tag) => t(tag.name))
                        },
                        images: [
                            {
                                url:
                                    Array.isArray(blogPost.images) &&
                                    blogPost.images.length > 0
                                        ? blogPost.images[0]
                                        : '',
                                alt: t(blogPost.title)
                            }
                        ]
                    }}
                    additionalMetaTags={[
                        {
                            property: 'keywords',
                            content: blogPost.tags
                                ?.map((tag: Tag) => t(tag.name))
                                ?.join(', ')
                        }
                    ]}
                />

                <ArticleJsonLd
                    type="BlogPosting"
                    url="https://www.entererp.com/blog"
                    title={t(blogPost.title)}
                    images={blogPost.images}
                    datePublished={blogPost.postedOn}
                    dateModified={blogPost.updatedAt}
                    authorName={blogPost.author.name}
                    description={t(cleanHtml(blogPost.shortContent))}
                    isAccessibleForFree
                />

                <BlogProvider
                    categories={blogPost.categories}
                    tags={blogPost.tags}
                >
                    <BlogContent blogPost={blogPost} />
                </BlogProvider>
            </>
        );
    } else {
        return (
            <>
                <NextSeo
                    title={seo.title}
                    description={seo.description}
                    additionalLinkTags={seo.additionalLinkTags}
                    key={router.asPath}
                />

                <BlogProvider
                    blogPosts={blogPosts}
                    popularBlogPosts={popularBlogPosts}
                    featuredBlogPosts={featuredBlogPosts}
                    totalBlogPostCount={totalBlogPostCount}
                    pageDescription={pageDescription}
                    pageHeading={pageHeading}
                    categories={categories}
                    tags={tags}
                >
                    <Blog />
                </BlogProvider>
            </>
        );
    }
};

BlogPage.PageLayout = MainLayout;

export default BlogPage;

export const getStaticPaths = async () => {
    const [blogPosts, categories, tags] = await Promise.all([
        getBlogPosts({ limit: featureFlags.blogPostsCountPerPage }),
        getCategories(),
        getTags()
    ]);

    const blogPostsPaths = blogPosts.map((blogPost) => {
        if (blogPost.status !== 'published') return;
        return { params: { slug: [blogPost.slug] } };
    });
    const categoriesPaths = categories.map((category) => {
        return { params: { slug: ['categories', category.slug] } };
    });
    const tagsPaths = tags.map((tag) => {
        return { params: { slug: ['tags', tag.slug] } };
    });

    return {
        paths: [
            { params: { slug: [''] } }, // For base path
            ...blogPostsPaths,
            ...categoriesPaths,
            ...tagsPaths
        ],
        fallback: 'blocking'
    };
};

export const getStaticProps = async (ctx: GetStaticPropsContext) => {
    const props = await getStaticPageProps({ ctx });
    const revalidate = 60;
    const { slug, pageNumber = 0 } = processParams(ctx.params);
    const [categories, tags] = await Promise.all([getCategories(), getTags()]);

    if (!slug) {
        const {
            blogPosts,
            featuredBlogPosts,
            popularBlogPosts,
            prevPageURL,
            nextPageURL,
            notFound
        } = await getBlogPageData({ pageNumber });

        return {
            props: {
                ...props,
                pageType: 'blog',
                categories,
                tags,
                blogPosts: blogPosts.data,
                totalBlogPostCount: blogPosts.total,
                popularBlogPosts,
                featuredBlogPosts,
                prevPageURL,
                nextPageURL,
                pageNumber
            },
            revalidate,
            notFound
        };
    } else if (slug?.startsWith('categories/')) {
        const categorySlug = slug.split('/').pop();

        const categoriesData = await getCategories({
            categorySlug
        });

        const pageHeading =
            categoriesData?.find((category) => category.slug === categorySlug)
                ?.name ?? '';
        const pageDescription =
            categoriesData?.find((category) => category.slug === categorySlug)
                ?.description ?? '';

        const {
            blogPosts,
            featuredBlogPosts,
            popularBlogPosts,
            prevPageURL,
            nextPageURL,
            notFound
        } = await getBlogPageData({ categorySlug, pageNumber });

        return {
            props: {
                ...props,
                pageType: 'blog',
                categories,
                tags,
                blogPosts: blogPosts.data,
                totalBlogPostCount: blogPosts.total,
                popularBlogPosts,
                featuredBlogPosts,
                pageHeading,
                pageDescription,
                prevPageURL,
                nextPageURL,
                pageNumber
            },
            revalidate,
            notFound
        };
    } else if (slug?.startsWith('tags/')) {
        const tagSlug = slug.split('/').pop();

        const tagsData = await getTags({ tagSlug });

        const pageHeading =
            tagsData?.find((category) => category.slug === tagSlug)?.name ?? '';
        const pageDescription =
            tagsData?.find((category) => category.slug === tagSlug)
                ?.description ?? '';

        const {
            blogPosts,
            featuredBlogPosts,
            popularBlogPosts,
            prevPageURL,
            nextPageURL,
            notFound
        } = await getBlogPageData({ tagSlug, pageNumber });

        return {
            props: {
                ...props,
                pageType: 'blog',
                categories,
                tags,
                blogPosts: blogPosts.data,
                totalBlogPostCount: blogPosts.total,
                popularBlogPosts,
                featuredBlogPosts,
                pageHeading,
                pageDescription,
                prevPageURL,
                nextPageURL,
                pageNumber
            },
            revalidate,
            notFound
        };
    } else {
        const blogPost = await getBlogPost({ slug });
        return {
            props: {
                ...props,
                blogPost,
                pageType: 'content'
            },
            revalidate,
            notFound: blogPost.status !== 'published'
        };
    }
};

type GetBlogPageDataArgs =
    | {
          categorySlug?: string;
          tagSlug?: undefined;
          pageNumber: number;
      }
    | {
          categorySlug?: undefined;
          tagSlug?: string;
          pageNumber: number;
      };

async function getBlogPageData({
    categorySlug,
    tagSlug,
    pageNumber
}: GetBlogPageDataArgs) {
    const POSTS_PER_PAGE = featureFlags.blogPostsCountPerPage;

    const commonProps = {
        ...(categorySlug && { categorySlug }),
        ...(tagSlug && { tagSlug })
    };

    const [blogPosts, popularBlogPosts, featuredBlogPosts] = await Promise.all([
        getPaginatedPosts({
            ...commonProps,
            limit: POSTS_PER_PAGE,
            skip: pageNumber * POSTS_PER_PAGE
        }),
        getBlogPosts({
            ...commonProps,
            isPopular: true,
            limit: 5
        }),
        getBlogPosts({
            ...commonProps,
            isFeatured: true,
            limit: 3
        })
    ]);

    const pageCount = Math.ceil(blogPosts.total / POSTS_PER_PAGE);

    const slugPath = categorySlug ?? tagSlug ?? 'blog';

    const prevPageURL =
        pageNumber === 1
            ? `https://www.entererp.com/${slugPath}`
            : pageNumber > 1
            ? `https://www.entererp.com/${slugPath}/${pageNumber}`
            : null;

    const nextPageURL =
        pageNumber + 2 <= pageCount
            ? `https://www.entererp.com/${slugPath}/${pageNumber + 2}`
            : null;

    const notFound =
        pageNumber < 0 ||
        pageNumber >= pageCount ||
        /* @ts-ignore */
        blogPosts?.length === 0;

    return {
        blogPosts,
        popularBlogPosts,
        featuredBlogPosts,
        prevPageURL,
        nextPageURL,
        notFound
    };
}
