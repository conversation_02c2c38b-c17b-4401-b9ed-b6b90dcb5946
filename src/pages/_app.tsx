import '@src/styles/globals.css';
import dynamic from 'next/dynamic';
import { appWithTranslation } from 'next-i18next';
import { Cookies } from 'react-cookie-consent';
import { useRouter } from 'next/router';
import { Fragment, useEffect } from 'react';
import { NextFont, NextSeo } from '@src/components/shared';
import { AppPropsWithLayout } from '@src/interfaces';
import posthog from 'posthog-js';
import { PostHogProvider } from 'posthog-js/react';

if (typeof window !== 'undefined') {
    posthog.init('phc_9csq8GdcGQIV2sjOYLZwRKSC78TlZM4twsiZg1mI8mE', {
        api_host: 'https://us.i.posthog.com',
        person_profiles: 'always',
        loaded: (posthog) => {
            if (process.env.NODE_ENV === 'development') posthog.debug();
        }
    });
}

const Analytics = dynamic(() => import('../components/shared/Analytics'), {
    ssr: false
});

function MyApp({ Component, pageProps }: AppPropsWithLayout) {
    const Layout = Component.PageLayout ?? Fragment;

    const router = useRouter();

    let CookieConsent: any = Fragment;
    if (Cookies.get('entererp-cookie-consent') === undefined) {
        CookieConsent = dynamic(
            () => import('@src/components/shared/CookieConsent'),
            { ssr: false }
        );
    }

    useEffect(() => {
        const handleRouteChange = () => posthog?.capture('$pageview');
        router.events.on('routeChangeComplete', handleRouteChange);

        return () => {
            router.events.off('routeChangeComplete', handleRouteChange);
        };
    }, [router.events]);

    return (
        <PostHogProvider client={posthog}>
            <NextFont />
            <NextSeo key={router.asPath} />

            <Layout>
                <Component {...pageProps} />
            </Layout>

            {/* <CookieConsent /> */}
            <Analytics />
        </PostHogProvider>
    );
}

export default appWithTranslation(MyApp);
