import { GetStaticPropsContext } from 'next';
import { NextSeo } from 'next-seo';
import Mobile from '@src/components/pages/company/mobile';
import { useTrans } from '@src/hooks';
import { NextPageWithLayout } from '@src/interfaces';
import MainLayout from '@src/layouts/MainLayout';
import { getStaticPageProps } from '@src/utils/server';

const MobilePage: NextPageWithLayout = () => {
    const t = useTrans();

    return (
        <>
            <NextSeo
                title={t('Mobil ERP')}
                description={t(
                    'EnterERP’nin akıllı, gelişmiş, yenilikçi ve çevik mobil işlevleriyle zamandan tasarruf etmeye hemen başlayın.'
                )}
            />

            <Mobile />
        </>
    );
};

MobilePage.PageLayout = MainLayout;

export default MobilePage;

export const getStaticProps = async (ctx: GetStaticPropsContext) => {
    const props = await getStaticPageProps({ ctx, translationPage: 'mobile' });

    return { props };
};
