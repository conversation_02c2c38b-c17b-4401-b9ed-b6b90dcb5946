import { GetStaticPropsContext } from 'next';
import { NextSeo } from 'next-seo';
import AllInOne from '@src/components/pages/company/all-in-one';
import { useTrans } from '@src/hooks';
import { NextPageWithLayout } from '@src/interfaces';
import MainLayout from '@src/layouts/MainLayout';
import { getStaticPageProps } from '@src/utils/server';

const AllInOnePage: NextPageWithLayout = () => {
    const t = useTrans();

    return (
        <>
            <NextSeo
                title={t('Tüm İhtiyaçlarınız Tek Programda')}
                description={t(
                    'Daha fazla verimlilik, esneklik ve içgörü arayan yer<PERSON>şik işletmeler için eşsiz bir bulut ERP yazılımı.'
                )}
            />

            <AllInOne />
        </>
    );
};

AllInOnePage.PageLayout = MainLayout;

export default AllInOnePage;

export const getStaticProps = async (ctx: GetStaticPropsContext) => {
    const props = await getStaticPageProps({
        ctx,
        translationPage: 'all-in-one'
    });

    return { props };
};
