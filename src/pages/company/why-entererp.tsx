import { GetStaticPropsContext } from 'next';
import { NextSeo } from 'next-seo';
import WhyEnterERP from '@src/components/pages/company/why-entererp';
import { useTrans } from '@src/hooks';
import { NextPageWithLayout } from '@src/interfaces';
import MainLayout from '@src/layouts/MainLayout';
import { getStaticPageProps } from '@src/utils/server';

const WhyEnterERPPage: NextPageWithLayout = () => {
    const t = useTrans();

    return (
        <>
            <NextSeo
                title={t('Neden EnterERP?')}
                description={t(
                    'EnterERP, şirketlerin ve müşterilerinin sürekli gelişen ihtiyaçlarını karşılamak için doğrudan bulutta doğdu.'
                )}
            />

            <WhyEnterERP />
        </>
    );
};

WhyEnterERPPage.PageLayout = MainLayout;

export default WhyEnterERPPage;

export const getStaticProps = async (ctx: GetStaticPropsContext) => {
    const props = await getStaticPageProps({
        ctx,
        translationPage: 'why-entererp'
    });

    return { props };
};
