import { GetStaticPropsContext } from 'next';
import { NextSeo } from 'next-seo';
import Signup from '@src/components/pages/signup';
import { useTrans } from '@src/hooks';
import { NextPageWithLayout } from '@src/interfaces';
import SignupLayout from '@src/layouts/SignupLayout';
import { getStaticPageProps } from '@src/utils/server';

const SignupPage: NextPageWithLayout = () => {
    const t = useTrans();

    return (
        <>
            <NextSeo
                title={t('Ücretsiz Demo')}
                description={t(
                    'EnterERP ücretsiz denemenizin kilidini açın ve önümüzdeki 7 gün boyunca kurumsal kaynak planlama otomasyonunun gücünü kullanın.'
                )}
            />

            <Signup />
        </>
    );
};

SignupPage.PageLayout = SignupLayout;

export default SignupPage;

export const getStaticProps = async (ctx: GetStaticPropsContext) => {
    const props = await getStaticPageProps({ ctx, translationPage: 'signup' });

    return { props };
};
