import { GetStaticPropsContext } from 'next';
import { NextSeo } from 'next-seo';
import { useTrans } from '@src/hooks';
import { NextPageWithLayout } from '@src/interfaces';
import MainLayout from '@src/layouts/MainLayout';
import { getStaticPageProps } from '@src/utils/server';

const PersonalDataPage: NextPageWithLayout = () => {
    const t = useTrans();

    return (
        <>
            <NextSeo
                title={t(
                    'Kişisel Verilerin korunmasına ilişkin Aydınlatma Metni'
                )}
                description={t(
                    'EnterERP, Kişisel Verilerin korunmasına ilişkin Aydınlatma Metni konusunda sizlere bilgi vermektedir.'
                )}
            />

            <section className="spacer container grid gap-3">
                <h1 className="text-lg font-semibold md:text-2xl">
                    Kişisel Verilerin korunmasına ilişkin Aydınlatma Metni
                </h1>
                <p className="text-sm">
                    EnterERP olarak 6698 sayılı Kişisel Verilerin Korunması
                    Kanunu’nun (’KVKK’) 10. maddesinden doğan aydınlatma
                    yükümlülüğümüzü yerine getirmek amacıyla ve kanunda açıkça
                    öngörülen veri sorumlusu sıfatıyla kişisel verilerinizi
                    işleyen EnterERP, Gizlilik Politikası’nda kişisel
                    verilerinizin nasıl toplandığı, kullanıldığı ve paylaşıldığı
                    konusunda sizlere bilgi vermektedir.
                </p>
                <p className="text-sm">
                    Bilgi güvenliği süreçlerinin yürütülmesi, faaliyetlerin
                    mevzuata uygun yürütülmesi, müşteri ilişkileri yönetim
                    süreçlerinin yönetilmesi, Talep ve Şikayetlerin Takibi
                    amacıyla Kişisel verileriniz, KVKK’nın “kişisel verilerin
                    işlenmesinde başta özel hayatın gizliliği olmak üzere
                    kişilerin temel hak ve özgürlüklerini korumak ve kişisel
                    verileri işleyen gerçek ve tüzel kişilerin yükümlülükleri
                    ile uyacakları usul ve esasları düzenlemek” olan ilgili 1.
                    Maddesi uyarınca, kişisel verilerin işlenme şartları
                    kapsamında işlenmektedir.
                </p>
                <p className="text-sm">İlgili kişiler,</p>
                <p className="text-sm">
                    Verilerinin işlenip işlenmediğini öğrenme, kişisel verileri
                    işlenmişse buna ilişkin bilgi talep etme, kişisel verilerin
                    işlenme amacını ve bunların amacına uygun kullanılıp
                    kullanılmadığını öğrenme, yurt içinde veya yurt dışında
                    kişisel verilerin aktarıldığı üçüncü kişileri bilme, kişisel
                    verilerin eksik veya yanlış işlenmiş olması halinde bunların
                    düzeltilmesini isteme hakkına sahiptir.
                </p>
                <ol className="grid gap-3 text-sm">
                    <li className="grid gap-2">
                        <p className="text-lg font-semibold">
                            1. İşlediğimiz Kişisel Verileriniz
                        </p>
                        <p>
                            Siz müşterilerimizin kabul ettiği, üyelik koşulları
                            ve formlar yardımıyla paylaştığı kişisel verileriniz
                            işlenebilmektedir,
                        </p>
                        <p>
                            Kişisel bilgileriniz ( isim, soy isim, doğum tarihi,
                            uyruk, T.C. kimlik numarası, cinsiyet), İletişim
                            Bilgileri (E-posta adresi, fatura ve teslimat
                            adresleri, cep telefonu numarası), Finans ve
                            pazarlama bilgileri (kredi kartı bilgileri, çerez
                            kayıtları, alışveriş geçmişi), İşlem güvenliği
                            bilgileri (IP Adresi Bilgileri, İnternet Sitesi
                            Giriş Çıkış Bilgileri, Kullanıcı Adı Bilgileri,
                            Şifre Bilgileri, Bağlantı Zamanı) ve hizmetimizi
                            alırken kullanmakta olduğunuz cihaz veya
                            tarayıcılarda tercih ettiğiniz lokasyon bilgisi.
                        </p>
                    </li>
                    <li className="grid gap-2">
                        <p className="text-lg font-semibold">
                            2. Kişisel Verilerinizin Aktarımı
                        </p>
                        <p>Kişisel verilerinizi,</p>
                        <p>
                            Bilişim teknolojileri, pazarlama/reklam/analiz
                            faaliyetleri, ödeme hizmetleri veya uzmanlık
                            gerektiren danışmanlık konularında ürün ve hizmet
                            desteği almak amaçlarıyla şirketimizin iş ortakları
                            ve hizmet sağlayıcılarıyla (çağrı merkezi,
                            pazarlama/reklam/analiz hizmeti sağlayıcıları, veri
                            tabanı ve sunucu hizmeti sağlayıcıları, Site ve
                            Mobil Uygulama kullanımlarını izleme hizmeti
                            sağlayanlar, e-posta sunucu hizmeti sağlayıcıları,
                            e-fatura ve e-arşiv fatura hizmeti sağlayıcıları,
                            elektronik ileti aracı hizmet sağlayıcıları, banka
                            ve elektronik ödeme kuruluşları, hukuki ve mali
                            danışmanlık hizmeti verenler, bağımsız denetim
                            hizmeti sağlayıcıları, arşivleme hizmeti verenler
                            ile); Yetkili kamu kurum ve kuruluşları ile adli
                            makamlara karşı olan bilgi, belge verme ve ilgili
                            sair yükümlülüklerimizi yerine getirmek ve dava ve
                            cevap hakları gibi yasal haklarımızı kullanabilmek
                            amacıyla bizden istenen bilgileri anılan bu kurum,
                            kuruluş ve makamlara ayrıca yasal yükümlülüklerimiz
                            kapsamında ticari iletişime ilişkin kayıtları İleti
                            Yönetim Sistemi A.Ş. ile; Aynı zamanda hukuka aykırı
                            işlemlerin önüne geçmek adına gerek gördüğümüz
                            durumlarda danışmanlık hizmeti aldığımız
                            tedarikçilerimiz ve ilgili işleme ilişkin ödeme
                            kuruluşlarıyla, İş ortaklarımızla paylaşmaktayız.
                        </p>
                    </li>
                    <li className="grid gap-2">
                        <p className="text-lg font-semibold">
                            3. İlgili kişilerin açık rızası aranmaksızın kişisel
                            verilerini işlemenin hukuki sebepleri
                        </p>
                        <p>
                            Kanunlarda açıkça öngörülmesi, Fiili imkânsızlık
                            nedeniyle rızasını açıklayamayacak durumda bulunan
                            veya rızasına hukuki geçerlilik tanınmayan kişinin
                            kendisinin ya da bir başkasının hayatı veya beden
                            bütünlüğünün korunması için zorunlu olması, bir
                            sözleşmenin kurulması veya ifasıyla doğrudan doğruya
                            ilgili olması kaydıyla, sözleşmenin taraflarına ait
                            kişisel verilerin işlenmesinin gerekli olması, veri
                            sorumlusunun hukuki yükümlülüğünü yerine
                            getirebilmesi için zorunlu olması, ilgili kişinin
                            kendisi tarafından alenileştirilmiş olması, Bir
                            hakkın tesisi/kullanılması veya korunması için veri
                            işlemenin zorunlu olması, İlgili kişinin temel hak
                            ve özgürlüklerine zarar vermemek kaydıyla veri
                            sorumlusunun meşru menfaatleri için veri
                            işlenmesinin zorunlu olması.
                        </p>
                    </li>
                    <li className="grid gap-2">
                        <p className="text-lg font-semibold">
                            4. Veri sorumlusunun aydınlatma yükümlülüğü
                        </p>
                        <p>
                            Veri sorumlusunun ve varsa temsilcisinin kimliği,
                            kişisel verilerin hangi amaçla işleneceği, işlenen
                            kişisel verilerin kimlere ve hangi amaçla
                            aktarılabileceği, kişisel veri toplamanın yöntemi ve
                            hukuki sebebi.
                        </p>
                    </li>
                    <li className="grid gap-2">
                        <p className="text-lg font-semibold">
                            5. Kişisel verilerin yurt dışına aktarılması
                        </p>
                        <p>
                            KVKK’nın madde 8’deki “Kişisel veriler, ilgili
                            kişinin açık rızası olmaksızın aktarılamaz.” ve/veya
                            madde 9’daki “Kişisel veriler, ilgili kişinin açık
                            rızası olmaksızın yurt dışına aktarılamaz” kuralları
                            uyarınca ve gerekli teknik ve idari tedbirler
                            alınarak, sadece ilgili amacın gerçekleşmesi için
                            gerekli olduğu ölçüde teknoloji alanındaki altyapı
                            tedarikçilerimiz tarafından kullanımımıza sunulan
                            programlarımıza veya sistemlerimize
                            kaydedilebilmektedir.
                        </p>
                    </li>
                </ol>
            </section>
        </>
    );
};

PersonalDataPage.PageLayout = MainLayout;

export default PersonalDataPage;

export const getStaticProps = async (ctx: GetStaticPropsContext) => {
    const props = await getStaticPageProps({
        ctx,
        translationPage: 'personal-data'
    });

    return { props };
};
