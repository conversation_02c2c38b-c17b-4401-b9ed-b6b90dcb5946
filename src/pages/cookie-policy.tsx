import { GetStaticPropsContext } from 'next';
import { NextSeo } from 'next-seo';
import { useTrans } from '@src/hooks';
import { NextPageWithLayout } from '@src/interfaces';
import MainLayout from '@src/layouts/MainLayout';
import { getStaticPageProps } from '@src/utils/server';
import { ExtendedLink } from '@src/components/shared';

const CookiePolicyPage: NextPageWithLayout = () => {
    const t = useTrans();

    return (
        <>
            <NextSeo
                title={t('Çerez Politikası')}
                description={t(
                    'EnterERP olarak, www.entererp.com internet sitemizde çerezler, pikseller ve web işaretçileri gibi çerez benzeri teknolojileri kullanıyoruz.'
                )}
            />

            <section className="spacer container grid gap-3 [&>p]:text-sm">
                <h1 className="text-lg font-semibold md:text-2xl">
                    Çerez Politikası
                </h1>
                <p>
                    EnterERP olarak, www.entererp.com internet sitemizde
                    çerezler, pikseller ve web işaretçileri gibi çerez benzeri
                    teknolojileri kullanıyoruz. Bu Çerez Politikasını sizlere
                    çerez kullanımımız hakkında bilgi vermek amacıyla
                    hazırladık.
                </p>
                <p>
                    İnternet sitemizi ziyaretiniz kapsamında çerezler
                    aracılığıyla belirli kişisel verilerinizi işliyoruz. Bunun
                    yanında, sitemizi kullanımınız kapsamında farklı kişisel
                    verileriniz de işlenebilmektedir. Örneğin, ürün siparişinizi
                    almak ve satın aldığınız ürünü size gönderebilmek için
                    kişisel verilerinizi işleriz. Şirketimizin kişisel
                    verilerinizi nasıl işlediği hakkında bilgi almak için{' '}
                    <ExtendedLink
                        className="font-medium text-primary-200 hover:underline"
                        href="/personal-data"
                    >
                        Kişisel Verilerinin İşlenmesine İlişkin Aydınlatma Metni
                    </ExtendedLink>
                    ’ni inceleyebilirsiniz.
                </p>
                <p className="!text-lg font-semibold">
                    A. Çerez Nedir ve Ne işe Yarar?
                </p>
                <p>
                    Çerezler bir internet sitesinden cihazınıza
                    (bilgisayarınıza, akıllı telefonunuza veya tabletinize)
                    gönderilen küçük metin dosyalarıdır. Çerezler temelde,
                    sistemlerimizin cihazınızı tanımasını sağlamak ve çevrimiçi
                    ziyaret deneyiminizi geliştirmek için kullanılır.
                </p>
                <p className="!text-lg font-semibold">
                    B. Çerez Türleri Nelerdir?
                </p>
                <p>
                    Çerez türlerini, kullanım amaçlarına göre çerezler,
                    sürelerine göre çerezler ve taraflarına göre çerezler olmak
                    üzere üçlü bir tasnife tabi tutmak mümkündür.
                </p>
                <p>B-1 Kullanım Amaçlarına Göre Çerezler</p>
                <p>
                    Kullanım amaçlarına göre dört tür çerez kullanmaktayız.
                    Bunlar: Performans Tanımlama (Analitik) Çerezleri, Hedefleme
                    Amaçlı Tanımlama Çerezleri, İşlevsel Tanımlama Çerezleri ve
                    Zorunlu Tanımlama (Kesinlikle Gerekli) Çerezleridir.
                </p>
                <p>
                    B-1.1. Performans Tanımlama (Analitik) Çerezleri: Bu
                    çerezler, web sitemizin performansını ölçebilmemiz ve
                    geliştirebilmemiz için ziyaretleri ve trafiği izlememize;
                    hangi sayfaların en çok ve en az popüler olduğunu
                    öğrenmemize ve müşterilerin sitede nasıl gezindiğini
                    görmemize yardımcı olmaktadır.
                </p>
                <p>
                    B-1.2. Hedefleme Amaçlı Tanımlama Çerezleri: Bu çerezler
                    tercih ve ilgi alanlarınıza yönelik ürün sunmak amacıyla
                    kullanılmaktadır.
                </p>
                <p>
                    B-1.3. İşlevsel Tanımlama Çerezleri: Bu çerezler, web
                    sitemizin gelişmiş bir işlevsellik ve kişiselleştirme
                    sunulmasına olanak sağlar.
                </p>
                <p>
                    B-1.4. Zorunlu Tanımlama (Kesinlikle Gerekli) Çerezler: Bu
                    çerezler web sitemizin çalışması için zorunlu olan ve
                    sistemlerimizce kapatılması mümkün olmayan çerezlerdir.
                    Zorunlu tanımlama çerezleri, kullanıcının talep etmiş olduğu
                    bir bilgi toplumu hizmetinin (örneğin; log-in olma, form
                    doldurma, gizlilik tercihlerinin hatırlanması gibi) yerine
                    getirilebilmesi için zorunlu olarak kullanılmaktadırlar.
                </p>
                <p className="!text-lg font-semibold">
                    C-Sürelerine Göre Çerezler
                </p>
                <p>
                    Sürelerine göre çerezler ikiye ayrılır bunlar oturum
                    çerezleri ve kalıcı çerezlerdir.
                </p>
                <p>
                    C-1.1. Oturum Çerezleri: Geçici çerez olarak da adlandırılan
                    oturum çerezi, oturumun sürekliliğinin sağlanması amacıyla
                    kullanılır. İnternet tarayıcınızı kapattığınızda oturum
                    çerezleri de kendiliğinden silinmektedir.
                </p>
                <p>
                    C-1.2. Kalıcı Çerezler: İnternet tarayıcısı kapatıldığı
                    zaman kendiliğinden silinmeyen kalıcı çerezler, belirli bir
                    tarihte veya belirli bir süre sonra kendiliğinden
                    silinmektedir.
                </p>
                <p>D. Taraflarına Göre Çerezler:</p>
                <p>
                    Taraflarına göre çerezler birinci taraf çerezler ve üçüncü
                    taraf çerezler olarak ikiye ayrılır.
                </p>
                <p>
                    D.1.1. Birinci Taraf Çerezler: Doğrudan ziyaret edilen web
                    sitesi tarafından cihaza yerleştirilmektedir.
                </p>
                <p>
                    D.1.2. Üçüncü Taraf Çerezleri: Firmamız ile iş birliği
                    içerisinde olan, reklam veren veya analitik sistem gibi
                    üçüncü bir tarafça cihaza yerleştirilen çerezlerdir.
                </p>

                <p className="!text-lg font-semibold">
                    ÇEREZ KULLANIMININ HUKUKİ SEBEBİ VE ÇEREZLERİN KULLANIM
                    AMAÇLARI
                </p>
                <p>
                    Firmamız KVKK’nın 5. maddesinde belirtilen ve aşağıda yer
                    verilen hukuka uygunluk sebeplerine dayanarak kişisel
                    verilerinizi işlemektedir. Çerezler vasıtasıyla toplanan
                    kişisel verileriniz;
                </p>
                <p>
                    * Sözleşmenin kurulması veya ifasıyla doğrudan doğruya
                    ilgili olması kaydıyla, kişisel verilerinizin işlenmesinin
                    gerekli olması hukuki sebebine dayanarak; Platformun sunduğu
                    hizmetlerden yararlanmanızı sağlamak için zorunlu tanımlama
                    çerezleri ile temel fonksiyonların gerçekleştirilmesi
                    amacıyla,
                </p>
                <p>
                    * Bir hakkın tesisi, kullanılması veya korunması için veri
                    işlemenin zorunlu olması hukuki sebebine dayanarak;
                    Platformda kullanılan tüm çerez tipleri vasıtasıyla işlenen
                    kişisel veriler, hukuki bir ihtilaf olması ya da kamu
                    kurumlarının bilgi talebinde bulunması halinde hukuk ve dava
                    işlerinin yürütülmesi amacıyla,
                </p>
                <p>
                    * Temel hak ve özgürlüklerine zarar vermemek kaydıyla
                    şirketimizin meşru menfaatleri için veri işlenmesinin
                    zorunlu olması hukuki sebebine dayanarak; Platformun
                    performansının ve işlevselliğinin geliştirilmesi ve
                    iyileştirilmesine yönelik faaliyetlerin yürütülmesi,
                    platformun kullanım kolaylığının sağlanması amacıyla,
                </p>
                <p>
                    *Açık rızanızın bulunması hukuki sebebine dayanarak;
                    Performans Tanımlama Çerezleri, Hedefleme Amaçlı Tanımlama
                    Çerezleri ve İşlevsel Tanımlama Çerezleri kullanılarak
                    alışveriş deneyiminizin iyileştirilmesi, hizmetlerimizin
                    sunulması ve size özel tanıtım yapılması, promosyonlar ve
                    pazarlama tekliflerinin sunulması, web sitesinin veya mobil
                    uygulamanın içeriğinin size göre iyileştirilmesi ve/veya
                    tercihlerinizin belirlenmesi, memnuniyetinizin
                    arttırılmasına yönelik faaliyetlerin yürütülmesi, yurt
                    içinde ve yurtdışına kişisel veri aktarımı yapılması
                    amacıyla işlenmektedir.
                </p>
                <p className="!text-2xl font-semibold">
                    Bizimle İletişime Geçin
                </p>
                <p>
                    Şirketimizin işbu Çerez Politikası, Veri Saklama ve İmha
                    Politikası ve mevzuat kaynaklı sair yükümlülükleri ile
                    ilgili olarak daha fazla bilgi almak ve talep ve
                    şikâyetlerinizi tarafımıza iletmek için bize her zaman
                    aşağıda verilen adres üzerinden ulaşabilirsiniz. Adres:
                    Cevizli Mah. Zuhal Cad. Görgülü Sok. No:14/2
                    Maltepe/İstanbul E-mail: <EMAIL> Çerez
                    Politikamız, ...01.2023 tarihinde güncellenmiştir. EnterERP
                    olarak, internet sitemizde kullandığımız çerezleri
                    değiştirebilir, ilave çerezler kullanabilir veya mevcut
                    çerezlerin kullanımına son verebiliriz. Bu kapsamda Çerez
                    Politikası’nda yer alan bilgileri değiştirme hakkımızın
                    saklı olduğunu, değişiklik halinde Çerez Politikasından
                    güncel bilgiye ulaşabileceğinizi bildirmek isteriz.
                </p>
            </section>
        </>
    );
};

CookiePolicyPage.PageLayout = MainLayout;

export default CookiePolicyPage;

export const getStaticProps = async (ctx: GetStaticPropsContext) => {
    const props = await getStaticPageProps({
        ctx,
        translationPage: 'cookie-policy'
    });

    return { props };
};
