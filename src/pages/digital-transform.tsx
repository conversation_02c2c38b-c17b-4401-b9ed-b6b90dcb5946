import { GetStaticPropsContext } from 'next';
import { NextSeo } from 'next-seo';
import DigitalTransform from '@src/components/pages/digital-transform';
import { useTrans } from '@src/hooks';
import { NextPageWithLayout } from '@src/interfaces';
import MainLayout from '@src/layouts/MainLayout';
import { getStaticPageProps } from '@src/utils/server';

const DigitalTransformPage: NextPageWithLayout = () => {
    const t = useTrans();

    return (
        <>
            <NextSeo
                title={t('Dijital Dönüşüm')}
                description={t(
                    'Dijital dönüşümün ana bileşenleri, gereklilikleri ve faydaları konusunda işletmelerin bilgi düzeylerinin ve farkındalıklarının artırılarak dijitalleşme ihtiyaçlarının analiz edilmesi gerekmektedir.'
                )}
            />

            <DigitalTransform />
        </>
    );
};

DigitalTransformPage.PageLayout = MainLayout;

export default DigitalTransformPage;

export const getStaticProps = async (ctx: GetStaticPropsContext) => {
    const props = await getStaticPageProps({
        ctx,
        translationPage: 'digital-transform'
    });

    return { props };
};
