import { GetStaticPropsContext } from 'next';
import { NextSeo } from 'next-seo';
import OnlineCollecting from '@src/components/pages/tools/online-collecting';
import { useTrans } from '@src/hooks';
import { NextPageWithLayout } from '@src/interfaces';
import MainLayout from '@src/layouts/MainLayout';
import { getStaticPageProps } from '@src/utils/server';

const OnlineCollectingPage: NextPageWithLayout = () => {
    const t = useTrans();

    return (
        <>
            <NextSeo
                title={t('Online Tahsilat (Sanal Pos)')}
                description={t(
                    'Banka ve Sanal POS sistemlerinden aldığınız POS hizmetini EnterERP online tahsilat sistemine kolayca entegre ederek ödeme almaya başlayın.'
                )}
            />

            <OnlineCollecting />
        </>
    );
};

OnlineCollectingPage.PageLayout = MainLayout;

export default OnlineCollectingPage;

export const getStaticProps = async (ctx: GetStaticPropsContext) => {
    const props = await getStaticPageProps({
        ctx,
        translationPage: 'online-collecting'
    });

    return { props };
};
