import { GetStaticPropsContext } from 'next';
import { NextSeo } from 'next-seo';
import OnlineBanking from '@src/components/pages/tools/online-banking';
import { useTrans } from '@src/hooks';
import { NextPageWithLayout } from '@src/interfaces';
import MainLayout from '@src/layouts/MainLayout';
import { getStaticPageProps } from '@src/utils/server';

const OnlineBankingPage: NextPageWithLayout = () => {
    const t = useTrans();

    return (
        <>
            <NextSeo
                title={t('Online Bankacılık (e-Ekstre)')}
                description={t(
                    'Bankacılık işlemlerinin birçoğunu EnterERP’nin eşsiz entegrasyon çözümleri ile kolayca yapın.'
                )}
            />

            <OnlineBanking />
        </>
    );
};

OnlineBankingPage.PageLayout = MainLayout;

export default OnlineBankingPage;

export const getStaticProps = async (ctx: GetStaticPropsContext) => {
    const props = await getStaticPageProps({
        ctx,
        translationPage: 'online-banking'
    });

    return { props };
};
