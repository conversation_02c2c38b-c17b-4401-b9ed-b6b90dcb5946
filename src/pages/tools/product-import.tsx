import { GetStaticPropsContext } from 'next';
import { NextSeo } from 'next-seo';
import ProductImport from '@src/components/pages/tools/product-import';
import { useTrans } from '@src/hooks';
import { NextPageWithLayout } from '@src/interfaces';
import MainLayout from '@src/layouts/MainLayout';
import { getStaticPageProps } from '@src/utils/server';

const ProductIntegrationPage: NextPageWithLayout = () => {
    const t = useTrans();

    return (
        <>
            <NextSeo
                title={t('Ürün Entegrasyonu')}
                description={t(
                    'Ürün Entegrasyon sistemi ile stok, fiyat ve özellikler ölçeğinde ürün entegrasyonu sağlayarak satışa başlayın.'
                )}
            />

            <ProductImport />
        </>
    );
};

ProductIntegrationPage.PageLayout = MainLayout;

export default ProductIntegrationPage;

export const getStaticProps = async (ctx: GetStaticPropsContext) => {
    const props = await getStaticPageProps({
        ctx,
        translationPage: 'product-import'
    });

    return { props };
};
