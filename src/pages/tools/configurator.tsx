import { GetStaticPropsContext } from 'next';
import { NextSeo } from 'next-seo';
import Configurator from '@src/components/pages/tools/configurator';
import { useTrans } from '@src/hooks';
import { NextPageWithLayout } from '@src/interfaces';
import MainLayout from '@src/layouts/MainLayout';
import { getStaticPageProps } from '@src/utils/server';

const ConfiguratorPage: NextPageWithLayout = () => {
    const t = useTrans();

    return (
        <>
            <NextSeo
                title={t('Ürün Konfigüratörü')}
                description={t(
                    'Müşterilerinizin ürünlerinizi kişiselleştirmesi için sınırsız seçenekler ekleyin ve satın almadan önce canlı ön izlemelerini sağlayın.'
                )}
            />

            <Configurator />
        </>
    );
};

ConfiguratorPage.PageLayout = MainLayout;

export default ConfiguratorPage;

export const getStaticProps = async (ctx: GetStaticPropsContext) => {
    const props = await getStaticPageProps({
        ctx,
        translationPage: 'configurator'
    });

    return { props };
};
