import { GetStaticPropsContext } from 'next';
import { NextSeo } from 'next-seo';
import EnterFlow from '@src/components/pages/tools/enter-flow';
import { useTrans } from '@src/hooks';
import { NextPageWithLayout } from '@src/interfaces';
import MainLayout from '@src/layouts/MainLayout';
import { getStaticPageProps } from '@src/utils/server';

const EnterFlowPage: NextPageWithLayout = () => {
    const t = useTrans();

    return (
        <>
            <NextSeo
                title={t('EnterFlow')}
                description={t(
                    'İş akış sürecini sistemli bir şekilde kontrol altına alın. İşletmenizin akışını anlık takip edin ve kurguyu değiştirin.'
                )}
            />

            <EnterFlow />
        </>
    );
};

EnterFlowPage.PageLayout = MainLayout;

export default EnterFlowPage;

export const getStaticProps = async (ctx: GetStaticPropsContext) => {
    const props = await getStaticPageProps({
        ctx,
        translationPage: 'enter-flow'
    });

    return { props };
};
