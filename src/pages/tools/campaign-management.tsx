import { GetStaticPropsContext } from 'next';
import { NextSeo } from 'next-seo';
import CampaignManagement from '@src/components/pages/tools/campaign-management';
import { useTrans } from '@src/hooks';
import { NextPageWithLayout } from '@src/interfaces';
import MainLayout from '@src/layouts/MainLayout';
import { getStaticPageProps } from '@src/utils/server';

const CampaignManagementPage: NextPageWithLayout = () => {
    const t = useTrans();

    return (
        <>
            <NextSeo
                title={t('Kampanya Yönetimi')}
                description={t(
                    'Satışlarınızı artırmak için istediğiniz özelliklerde gelişmiş müşteri kampanyaları oluşturun.'
                )}
            />

            <CampaignManagement />
        </>
    );
};

CampaignManagementPage.PageLayout = MainLayout;

export default CampaignManagementPage;

export const getStaticProps = async (ctx: GetStaticPropsContext) => {
    const props = await getStaticPageProps({
        ctx,
        translationPage: 'campaign-management'
    });

    return { props };
};
