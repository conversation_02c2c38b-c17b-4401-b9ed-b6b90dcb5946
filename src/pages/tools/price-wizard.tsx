import { GetStaticPropsContext } from 'next';
import { NextSeo } from 'next-seo';
import PriceWizard from '@src/components/pages/tools/price-wizard';
import { useTrans } from '@src/hooks';
import { NextPageWithLayout } from '@src/interfaces';
import MainLayout from '@src/layouts/MainLayout';
import { getStaticPageProps } from '@src/utils/server';

const PriceWizardPage: NextPageWithLayout = () => {
    const t = useTrans();

    return (
        <>
            <NextSeo
                title={t('Fiyatlandırma Sihirbazı')}
                description={t(
                    'Çoklu fiyat listelerinizi dinamik olarak seçim kriterlerinize göre güncelleyin.'
                )}
            />

            <PriceWizard />
        </>
    );
};

PriceWizardPage.PageLayout = MainLayout;

export default PriceWizardPage;

export const getStaticProps = async (ctx: GetStaticPropsContext) => {
    const props = await getStaticPageProps({
        ctx,
        translationPage: 'price-wizard'
    });

    return { props };
};
