import { GetStaticPropsContext } from 'next';
import { NextSeo } from 'next-seo';
import PartnerShip from '@src/components/pages/corporate/partnership';
import { useTrans } from '@src/hooks';
import { NextPageWithLayout } from '@src/interfaces';
import MainLayout from '@src/layouts/MainLayout';
import { getStaticPageProps } from '@src/utils/server';

const PartnerShipPage: NextPageWithLayout = () => {
    const t = useTrans();

    return (
        <>
            <NextSeo
                title={t('İş Ortaklığı')}
                description={t(
                    'EnterERP satış ortağı programına katılın ve referanslarınız için yüksek komisyonlar kazanın. Kayıt ücreti yok, minimum satış gereksinimi yok ve kazancınızda üst sınır yok!'
                )}
            />

            <PartnerShip />
        </>
    );
};

PartnerShipPage.PageLayout = MainLayout;

export default PartnerShipPage;

export const getStaticProps = async (ctx: GetStaticPropsContext) => {
    const props = await getStaticPageProps({
        ctx,
        translationPage: 'partnership'
    });

    return { props };
};
