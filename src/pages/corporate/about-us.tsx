import { GetStaticPropsContext } from 'next';
import { NextSeo } from 'next-seo';
import AboutUs from '@src/components/pages/corporate/about-us';
import { useTrans } from '@src/hooks';
import { NextPageWithLayout } from '@src/interfaces';
import MainLayout from '@src/layouts/MainLayout';
import { getStaticPageProps } from '@src/utils/server';

const AboutUsPage: NextPageWithLayout = () => {
    const t = useTrans();

    return (
        <>
            <NextSeo
                title={t('Kurumsal')}
                description={t(
                    'EnterERP her şey dahil kullanıcı lisanslama modeliyle uyarlanabilir bulut ve mobil teknoloji ürünleri ile işinizin her zaman, her yerde, eksiksiz, gerçek zamanlı kullanımını sağlar.'
                )}
            />

            <AboutUs />
        </>
    );
};

AboutUsPage.PageLayout = MainLayout;

export default AboutUsPage;

export const getStaticProps = async (ctx: GetStaticPropsContext) => {
    const props = await getStaticPageProps({ ctx, translationPage: 'about' });

    return { props };
};
