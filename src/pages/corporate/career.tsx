import { GetStaticPropsContext } from 'next';
import { NextSeo } from 'next-seo';
import Career from '@src/components/pages/corporate/career';
import { useTrans } from '@src/hooks';
import { NextPageWithLayout } from '@src/interfaces';
import MainLayout from '@src/layouts/MainLayout';
import { getStaticPageProps } from '@src/utils/server';

const CareerPage: NextPageWithLayout = () => {
    const t = useTrans();

    return (
        <>
            <NextSeo
                title={t('Kariyer')}
                description={t(
                    "Bir işte iki şey önemlidir: her gün kendinize meydan okumak ve önemli olan bir şey inşa etmek. EnterERP'de ikisini de yapıyoruz."
                )}
            />

            <Career />
        </>
    );
};

CareerPage.PageLayout = MainLayout;

export default CareerPage;

export const getStaticProps = async (ctx: GetStaticPropsContext) => {
    const props = await getStaticPageProps({ ctx, translationPage: 'career' });

    return { props };
};
