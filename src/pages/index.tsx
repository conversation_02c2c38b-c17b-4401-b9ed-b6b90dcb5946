import { GetStaticPropsContext } from 'next';
import { NextSeo } from 'next-seo';
import {
    SectionFive,
    SectionFour,
    SectionOne,
    SectionSeven,
    SectionSix,
    SectionThree,
    SectionTwo
} from '@src/components/pages/home/<USER>';
import { useTrans } from '@src/hooks';
import { NextPageWithLayout } from '@src/interfaces';
import MainLayout from '@src/layouts/MainLayout';
import { getStaticPageProps } from '@src/utils/server';
import SectionTwoTop from '@src/components/pages/home/<USER>/SectionTwoTop';

const Home: NextPageWithLayout = () => {
    const t = useTrans();

    return (
        <>
            <NextSeo
                description={t(
                    'EnterERP ile tedarik zincirinden finansal süreçlere kadar tüm işinizin kontrolünü elinize alın.'
                )}
            />

            <SectionOne />
            <SectionTwoTop />
            <SectionTwo />
            <SectionThree />
            <SectionFour />
            <SectionFive />
            <SectionSix />
            {/* <SectionSeven /> */}
        </>
    );
};

Home.PageLayout = MainLayout;

export default Home;

export const getStaticProps = async (ctx: GetStaticPropsContext) => {
    const props = await getStaticPageProps({ ctx, translationPage: 'home' });

    return { props };
};
