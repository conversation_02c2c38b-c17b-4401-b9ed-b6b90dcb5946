table {
    --shadow-color: rgb(235 235 235);
}

/* Desktop pricing table styles */
.desktop-pricing-table tbody tr td:last-child,
.desktop-pricing-table tbody tr td:nth-child(3) {
    border-left: 1px solid var(--shadow-color);
}

.desktop-pricing-table tbody tr td:nth-child(2) {
    box-shadow: -1px 0px 0px 0px var(--shadow-color);
}

.desktop-pricing-table tbody tr td:last-child {
    box-shadow: 1px 0px 0px 0px var(--shadow-color);
}

.desktop-pricing-table tbody tr:first-child td:not(:first-child) {
    box-shadow:
        0px -1px 0px 0px var(--shadow-color),
        1px 0px 0px 0px var(--shadow-color),
        -1px 0px 0px 0px var(--shadow-color);
}

.desktop-pricing-table tbody tr:first-child td:last-child {
    box-shadow:
        1px 0px 0px 0px var(--shadow-color),
        0px -1px 0px 0px var(--shadow-color);
    border-top-right-radius: 0.25rem;
}

.desktop-pricing-table tbody tr:first-child td:nth-child(2) {
    border-top-left-radius: 0.25rem;
}

.desktop-pricing-table tbody tr:first-child td::last-child {
    border-top-right-radius: 0.25rem;
}

.desktop-pricing-table tbody tr:last-child td:not(:first-child) {
    box-shadow:
        0px 1px 0px 0px var(--shadow-color),
        1px 0px 0px 0px var(--shadow-color),
        -1px 0px 0px 0px var(--shadow-color);
}

.desktop-pricing-table tbody tr:last-child td:nth-child(2) {
    box-shadow:
        0px 1px 0px 0px var(--shadow-color),
        -1px 0px 0px 0px var(--shadow-color);
}

.desktop-pricing-table tbody tr:last-child td:last-child {
    box-shadow:
        0px 1px 0px 0px var(--shadow-color),
        1px 0px 0px 0px var(--shadow-color);
    border-bottom-right-radius: 0.25rem;
}

.desktop-pricing-table tbody tr:last-child td:nth-child(2) {
    border-bottom-left-radius: 0.25rem;
}

.desktop-pricing-table tbody tr:last-child td:last-child {
    border-bottom-right-radius: 0.25rem;
}

.desktop-pricing-table tbody tr td:not(:first-child) {
    padding-inline: 2rem;
    padding-block: 1rem;
    text-align: center;
}

.desktop-pricing-table tbody {
    display: block;
    width: calc(100% - 1px);
}

.desktop-pricing-table tbody:before {
    content: '@';
    display: block;
    line-height: 10px;
    text-indent: -99999px;
}

.desktop-pricing-table tbody tr td {
    font-weight: 300;
    font-size: 15px;
}

.desktop-pricing-table tbody tr td:first-child {
    font-weight: 500;
    font-size: 15px;
}

.desktop-pricing-table thead,
.desktop-pricing-table tbody tr {
    display: table;
    width: 100%;
    table-layout: fixed;
}

/* Mobile pricing table styles */
.mobile-table-body tbody tr td {
    font-weight: 300;
    font-size: 14px;
}

.mobile-table-body tr {
    margin-bottom: -1px;
}

.mobile-pricing-table tr td table td {
    padding-inline: 1rem;
    min-width: 220px;
    text-align: center;
    padding-block: 0.5rem;
}

.mobile-pricing-table thead,
.mobile-pricing-table tbody tr {
    display: table;
    table-layout: fixed;
}
