@layer components {
    .desktop-navbar-underline {
        @apply relative after:absolute after:-bottom-[.5px] after:left-0 after:right-0 after:h-1 after:w-full after:bg-primary-500 after:content-[''];
    }

    .top-spacer {
        @apply pt-8 md:pt-12 lg:pt-28;
    }

    .bottom-spacer {
        @apply pb-8 md:pb-12 lg:pb-28;
    }

    .spacer {
        @apply py-8 md:py-12 lg:py-28;
    }
}

.cookie-btn-accept {
    @apply btn-transition inline-block cursor-pointer whitespace-nowrap rounded-[100vw] border border-[#011D2A] bg-primary-200 px-12 py-4 font-medium leading-4 text-white;
}

.cookie-btn-accept:hover {
    @apply -translate-y-1 shadow-[0px_6px_0px_0px_#011D2A];
}

.cookie-btn-decline {
    @apply btn-transition inline-block cursor-pointer whitespace-nowrap rounded-[100vw] border border-primary-200 bg-white px-12 py-4 font-medium leading-4 text-[#011D2A];
}

.cookie-btn-decline:hover {
    @apply -translate-y-1 shadow-[0px_6px_0px_0px_#011D2A];
}

.cookie-container {
    @apply fixed bottom-0 left-0 right-0 z-[1000] flex flex-col items-center gap-4 rounded-none border-t border-primary-600 bg-white px-8 py-5 text-sm text-black shadow-[0_5px_20px_0px_rgba(0,0,0,0.1)] md:flex-row md:px-16 lg:px-32;
}

.cookie-btn-wrapper {
    @apply flex items-center gap-2 whitespace-nowrap;
}

.desktop-navbar-shadow {
    box-shadow:
        8px 12px 1px rgba(29, 17, 51, 0.04),
        0 3px 16px 2px rgba(9, 32, 77, 0.12),
        0 4px 6px -2.5px rgba(29, 17, 51, 0.12);
}

.desktop-navbar-animation {
    position: relative;
}

.desktop-navbar-animation::after {
    content: url("data:image/svg+xml,%3C%3Fxml version='1.0' %3F%3E%3Csvg viewBox='0 0 32 32' fill='%230065FF' xmlns='http://www.w3.org/2000/svg'%3E%3Ctitle/%3E%3Cg data-name='Layer 2' id='Layer_2'%3E%3Cpath d='M22,9a1,1,0,0,0,0,1.42l4.6,4.6H3.06a1,1,0,1,0,0,2H26.58L22,21.59A1,1,0,0,0,22,23a1,1,0,0,0,1.41,0l6.36-6.36a.88.88,0,0,0,0-1.27L23.42,9A1,1,0,0,0,22,9Z'/%3E%3C/g%3E%3C/svg%3E");
    display: inline-block;
    position: absolute;
    width: 18px;
    transform: translateX(0);
    opacity: 0;
    transition: all 200ms;
}

@media screen and (min-width: 1280px) {
    .desktop-navbar-animation::after {
        margin-top: 2px;
    }
}

.desktop-navbar-animation:hover::after {
    opacity: 1;
    transform: translateX(100%);
}

/* Accordion */
.accordion__button {
    color: white;
    position: relative;
    cursor: pointer;
    width: 100%;
    font-size: 1.6rem;
    padding-block: 1rem;
    text-align: left;
    border: none;
    user-select: none;
}

@media screen and (max-width: 675px) {
    .accordion__button {
        font-size: 1.25rem;
    }
}

.accordion__button:before {
    content: url("data:image/svg+xml;utf8,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 448 512' width='24px' height='24px' fill='%230065FF'><path d='M432 256c0 17.69-14.33 32.01-32 32.01H256v144c0 17.69-14.33 31.99-32 31.99s-32-14.3-32-31.99v-144H48c-17.67 0-32-14.32-32-32.01s14.33-31.99 32-31.99H192v-144c0-17.69 14.33-32.01 32-32.01s32 14.32 32 32.01v144h144C417.7 224 432 238.3 432 256z' /></svg>");
    position: absolute;
    display: inline-block;
    height: 24px;
    width: 24px;
    right: 0;
}

.accordion__button[aria-expanded='true']::before,
.accordion__button[aria-selected='true']::before {
    content: url("data:image/svg+xml;utf8,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 448 512' width='24px' height='24px' fill='%230065FF'><path d='M400 288h-352c-17.69 0-32-14.32-32-32.01s14.31-31.99 32-31.99h352c17.69 0 32 14.3 32 31.99S417.7 288 400 288z' /></svg>");
}

.accordion__panel {
    padding-block: 0.75rem;
}

/* Gradients */
.light-gradient {
    background: rgb(243, 250, 251);
    background: radial-gradient(
        circle,
        rgba(243, 250, 251, 1) 0%,
        rgba(243, 243, 254, 1) 50%,
        rgba(246, 254, 250, 1) 100%
    );
}

.card-shadow {
    box-shadow: 0px 0px 6px rgba(0, 0, 0, 0.1);
}

.btn-transition {
    transition:
        transform 0.3s cubic-bezier(0.5, 2.5, 0.7, 0.7),
        box-shadow 0.3s cubic-bezier(0.5, 2.5, 0.7, 0.7),
        -webkit-transform 0.3s cubic-bezier(0.5, 2.5, 0.7, 0.7),
        -webkit-box-shadow 0.3s cubic-bezier(0.5, 2.5, 0.7, 0.7);
}

@media screen and (min-width: 1024px) {
    .why-entererp-bg {
        background-image: url('../../public/images/pages/company/why-entererp/Background.svg');
        background-repeat: no-repeat;
        background-position: center center;
        background-size: contain;
    }
}

.fix-blur {
    transform: unset !important;
}

@media screen and (min-width: 1921px), screen and (min-height: 1081px) {
    .home-page-hero {
        @apply py-28;
    }
}

@media screen and (max-width: 1921px) {
    .home-page-hero {
        @apply py-16;
    }
}

@media screen and (max-width: 1024px) {
    .home-page-hero {
        @apply py-8;
    }
}

@media (pointer: coarse) {
    .scrollbar-hidden {
        -ms-overflow-style: none; /* IE and Edge */
        scrollbar-width: none; /* Firefox */
    }

    .scrollbar-hidden::-webkit-scrollbar {
        display: none;
    }
}

.card-first {
    --card-offset: -1.5;
    --card-angle: 55deg;
    --card-dx: 40%;
}

.card-second {
    --card-offset: -0.5;
    --card-angle: 55deg;
    --card-dx: 40%;
}

.card-third {
    --card-offset: 0.5;
    --card-angle: 55deg;
    --card-dx: 40%;
}

.card-fourth {
    --card-offset: 1.5;
    --card-angle: 55deg;
    --card-dx: 40%;
}

/* Safari Only */
@supports (font: -apple-system-body) and (-webkit-appearance: none) {
    .safari-fix-animation {
        -webkit-transform: translate3d(0, 0, 0);
        transform: translate3d(0, 0, 0);
    }
}

.card {
    position: absolute;
    transform-origin: center;
    transform: translateZ(200px)
        translateX(calc(var(--card-dx) * var(--card-offset) * 1.2)) scale(0.95)
        perspective(1200px) rotateY(var(--card-angle));
    transition: transform 300ms ease-in-out;
}

.card-hover {
    transform: rotateY(0) translateX(calc(var(--card-dx) * var(--card-offset)))
        scale(1) translateZ(200px);
}

.card-child {
    box-shadow: -8px 0 0 #021d63;
    background: linear-gradient(
            273.23deg,
            rgba(0, 0, 0, 0.5) 5.36%,
            rgba(0, 0, 0, 0) 96.38%
        ),
        #0048ff;
}

.line-1 {
    animation: straight-line 0.3s linear forwards infinite;
}

.line-2 {
    animation: straight-line 0.3s linear reverse infinite;
}

.line-1-mobile {
    animation: straight-line-mobile 0.3s linear forwards infinite;
}

.line-2-mobile {
    animation: straight-line-mobile 0.3s linear reverse infinite;
}

@keyframes straight-line-mobile {
    from {
        stroke-dashoffset: 0.2;
    }
    to {
        stroke-dashoffset: 6;
    }
}

@keyframes straight-line {
    from {
        stroke-dashoffset: 8;
    }
    to {
        stroke-dashoffset: 0;
    }
}
