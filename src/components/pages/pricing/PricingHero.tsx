import { Tab } from '@headlessui/react';
import { Fragment } from 'react';
import CommunityIcon from '@public/images/icons/Community.svg';
import ScaleIcon from '@public/images/icons/Scale.svg';
import { useTrans } from '@src/hooks';
import PricingCategoryTab from './PricingCategoryTab';
import TabPanelOne from './TabPanelOne';
import TabPanelTwo from './TabPanelTwo';

const PricingHero = () => {
    const t = useTrans();

    return (
        <Tab.Group as={Fragment}>
            <div className="light-gradient md:border-b">
                <section className="relative grid place-items-center gap-4 py-8 lg:py-20">
                    <h1 className="container text-center font-means text-4xl">
                        {t('EnterERP Ürünler')}
                    </h1>
                    <p className="container text-center text-xl">
                        {t('İşletmeler için yeni nesil işletim sistemi')}
                    </p>
                </section>

                <Tab.List className="container flex flex-wrap justify-center gap-3 md:gap-6">
                    <PricingCategoryTab
                        title="SaaS"
                        description="EnterERP sunucularına kurulum"
                        src={ScaleIcon}
                    />

                    <PricingCategoryTab
                        title="On-premises"
                        description="Müşteri sunucularına kurulum"
                        src={CommunityIcon}
                    />
                </Tab.List>
            </div>

            <Tab.Panels>
                <Tab.Panel as="section">
                    <TabPanelOne />
                </Tab.Panel>
                <Tab.Panel as="section">
                    <TabPanelTwo />
                </Tab.Panel>
            </Tab.Panels>
        </Tab.Group>
    );
};

export default PricingHero;
