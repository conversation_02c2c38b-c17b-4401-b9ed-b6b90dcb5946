import ScaleIcon from '@public/images/icons/Scale.svg';
import { useTrans } from '@src/hooks';
import { CheckIcon } from '@src/icons/solid';
import PricingCardWrapper from './PricingCardWrapper';

const PricingCards = () => {
    const t = useTrans();

    return (
        <div className="grid gap-4 md:grid-cols-2 md:gap-12 xl:grid-cols-3">
            <PricingCardWrapper
                planTitle="LITE"
                description="Küçük işletmeler için hızlı ve dinamik bir çözüm."
                src={ScaleIcon}
            >
                <ul className="grid gap-2">
                    <li className="flex items-center gap-2">
                        <div>
                            <CheckIcon className="h-4 w-4 text-primary-500" />
                        </div>
                        <span>{t("320 GB(SSD)'a kadar depolama")}</span>
                    </li>
                    <li className="flex items-center gap-2">
                        <div>
                            <CheckIcon className="h-4 w-4 text-primary-500" />
                        </div>
                        <span>
                            {t(
                                '<PERSON>naklar, iş yükünüzü karşılamak için sorunsuz bir şekilde önceden hazırdır'
                            )}
                        </span>
                    </li>
                    <li className="flex items-center gap-2">
                        <CheckIcon className="h-4 w-4 text-primary-500" />
                        <span>
                            {t(
                                'İşletme ölçeğiniz için dinamik optimize edilir'
                            )}
                        </span>
                    </li>
                    <li className="flex items-center gap-2">
                        <CheckIcon className="h-4 w-4 text-primary-500" />
                        <span>
                            {t('Her zaman açık güvenlik ve yedeklemeler')}
                        </span>
                    </li>
                    <li className="flex items-center gap-2">
                        <CheckIcon className="h-4 w-4 text-primary-500" />
                        <span>{t('Sürekli sabit bant genişliği')}</span>
                    </li>
                    <li className="flex items-center gap-2">
                        <CheckIcon className="h-4 w-4 text-primary-500" />
                        <span>
                            {t('365 gün kesintisiz teknik destek hizmeti')}
                        </span>
                    </li>
                    <li className="flex items-center gap-2">
                        <CheckIcon className="h-4 w-4 text-primary-500" />
                        <span>
                            {t('Global ölçeklenerek uyarlanabilen alt yapı')}
                        </span>
                    </li>
                </ul>
            </PricingCardWrapper>

            <PricingCardWrapper
                planTitle="PRO"
                description="Küçük ve orta ölçekli işletmeler için esnek ölçeklenebilir çözüm."
                src={ScaleIcon}
                isRecommended
            >
                <ul className="grid gap-2">
                    <li className="flex items-center gap-2">
                        <div>
                            <CheckIcon className="h-4 w-4 text-primary-500" />
                        </div>
                        <span>{t('8 TB(SSD)’a kadar depolama')}</span>
                    </li>
                    <li className="flex items-center gap-2">
                        <div>
                            <CheckIcon className="h-4 w-4 text-primary-500" />
                        </div>
                        <span>
                            {t(
                                'Kaynaklar, iş yükünüzü karşılamak için sorunsuz bir şekilde ölçeklenir'
                            )}
                        </span>
                    </li>
                    <li className="flex items-center gap-2">
                        <div>
                            <CheckIcon className="h-4 w-4 text-primary-500" />
                        </div>
                        <span>
                            {t('Süreçlerinize esnek bir şekilde uyum sağlar')}
                        </span>
                    </li>
                    <li className="flex items-center gap-2">
                        <div>
                            <CheckIcon className="h-4 w-4 text-primary-500" />
                        </div>
                        <span>
                            {t('Her zaman açık güvenlik ve yedeklemeler')}
                        </span>
                    </li>
                    <li className="flex items-center gap-2">
                        <CheckIcon className="h-4 w-4 text-primary-500" />
                        <span>{t('Sürekli sabit bant genişliği')}</span>
                    </li>
                    <li className="flex items-center gap-2">
                        <CheckIcon className="h-4 w-4 text-primary-500" />
                        <span>
                            {t('365 gün kesintisiz teknik destek hizmeti')}
                        </span>
                    </li>
                    <li className="flex items-center gap-2">
                        <CheckIcon className="h-4 w-4 text-primary-500" />
                        <span>
                            {t('Global ölçeklenerek uyarlanabilen alt yapı')}
                        </span>
                    </li>
                </ul>
            </PricingCardWrapper>

            <PricingCardWrapper
                planTitle="ELEGANCE"
                description="Özel iş yükü gereksinimleri olan işletmeler için genişletilebilir çözüm."
                src={ScaleIcon}
            >
                <ul className="grid gap-2">
                    <li className="flex items-center gap-2">
                        <div>
                            <CheckIcon className="h-4 w-4 text-primary-500" />
                        </div>
                        <span>{t('Sınırsız depolama')}</span>
                    </li>
                    <li className="flex items-center gap-2">
                        <div>
                            <CheckIcon className="h-4 w-4 text-primary-500" />
                        </div>
                        <span>
                            {t(
                                'Kaynaklar, iş yükünüzü karşılamak için sorunsuz bir şekilde genişletilir'
                            )}
                        </span>
                    </li>
                    <li className="flex items-center gap-2">
                        <div>
                            <CheckIcon className="h-4 w-4 text-primary-500" />
                        </div>
                        <span>
                            {t(
                                'Gereksinimlerinize özel çözümler üretilerek genişletilebilir'
                            )}
                        </span>
                    </li>
                    <li className="flex items-center gap-2">
                        <div>
                            <CheckIcon className="h-4 w-4 text-primary-500" />
                        </div>
                        <span>
                            {t('Her zaman açık güvenlik ve yedeklemeler')}
                        </span>
                    </li>
                    <li className="flex items-center gap-2">
                        <CheckIcon className="h-4 w-4 text-primary-500" />
                        <span>{t('Sürekli sabit bant genişliği')}</span>
                    </li>
                    <li className="flex items-center gap-2">
                        <CheckIcon className="h-4 w-4 text-primary-500" />
                        <span>
                            {t('365 gün kesintisiz teknik destek hizmeti')}
                        </span>
                    </li>
                    <li className="flex items-center gap-2">
                        <CheckIcon className="h-4 w-4 text-primary-500" />
                        <span>
                            {t('Global ölçeklenerek uyarlanabilen alt yapı')}
                        </span>
                    </li>
                </ul>
            </PricingCardWrapper>
        </div>
    );
};

export default PricingCards;
