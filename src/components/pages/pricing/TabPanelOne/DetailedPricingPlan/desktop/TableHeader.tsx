import ScaleIcon from '@public/images/icons/Scale.svg';
import { ExtendedImage, SolidButton } from '@src/components/shared';
import { useTrans } from '@src/hooks';

const TableHeader = () => {
    const t = useTrans();

    return (
        <thead className="sticky top-[74px] z-10">
            <tr className="bg-white">
                <th></th>
                <th className="py-4">
                    <div className="flex items-center gap-4">
                        <div>
                            <ExtendedImage
                                className="w-6"
                                src={ScaleIcon}
                                alt=""
                            />
                        </div>

                        <p className="font-means">{t('LITE')}</p>
                    </div>
                    <p className="py-4 text-left text-sm font-light">
                        {t('Küçük işletmeler için hızlı ve dinamik bir çözüm.')}
                    </p>

                    <div className="float-left inline-block">
                        <SolidButton className="px-6 py-3" href="signup">
                            {t('Ücretsiz Dene')}
                        </SolidButton>
                    </div>
                </th>
                <th className="py-4">
                    <div className="flex items-center gap-4">
                        <div>
                            <ExtendedImage
                                className="w-6"
                                src={ScaleIcon}
                                alt=""
                            />
                        </div>

                        <p className="font-means">{t('PRO')}</p>
                    </div>
                    <p className="py-4 text-left text-sm font-light">
                        {t(
                            'Küçük ve orta ölçekli işletmeler için esnek ölçeklenebilir çözüm.'
                        )}
                    </p>

                    <div className="float-left inline-block">
                        <SolidButton className="px-6 py-3" href="signup">
                            {t('Ücretsiz Dene')}
                        </SolidButton>
                    </div>
                </th>
                <th className="py-4">
                    <div className="flex items-center gap-4">
                        <div>
                            <ExtendedImage
                                className="w-6"
                                src={ScaleIcon}
                                alt=""
                            />
                        </div>

                        <p className="font-means">{t('ELEGANCE')}</p>
                    </div>
                    <p className="py-4 text-left text-sm font-light">
                        {t(
                            'Özel iş yükü gereksinimleri olan işletmeler  için genişletilebilir çözüm.'
                        )}
                    </p>

                    <div className="float-left inline-block text-left">
                        <SolidButton className="px-6 py-3" href="signup">
                            {t('Ücretsiz Dene')}
                        </SolidButton>
                    </div>
                </th>
            </tr>
        </thead>
    );
};

export default TableHeader;
