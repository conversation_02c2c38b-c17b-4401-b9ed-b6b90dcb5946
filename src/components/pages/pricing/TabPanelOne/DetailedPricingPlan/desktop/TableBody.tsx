import { useCallback } from 'react';
import { useTrans } from '@src/hooks';
import { CheckIcon } from '@src/icons/solid';
import { pricePlanDetails } from '../shared/PricePlanDetails';

const TableBody = () => {
    const t = useTrans();

    const setPriceTableValue = useCallback((value: string | boolean) => {
        if (value === true) {
            return <CheckIcon className="mx-auto h-5 w-5 text-primary-500" />;
        }
        if (value === false) {
            return <span>-</span>;
        }
        if (typeof value === 'string') {
            return t(value);
        }
        // eslint-disable-next-line
    }, []);

    return (
        <tbody>
            <tr>
                <td className="py-4 font-semibold tracking-wider text-accent-200">
                    {t('GENEL')}
                </td>
                <td className="bg-table-100"></td>
                <td className="bg-table-100"></td>
                <td className="bg-table-100"></td>
            </tr>
            {pricePlanDetails.general.map((pricePlan) => {
                return (
                    <tr key={pricePlan.category} className="group">
                        <td className="pl-6 font-semibold group-hover:bg-gray-500 group-hover:bg-opacity-5">
                            {t(pricePlan.category)}
                        </td>
                        <td className="bg-table-100  group-hover:bg-primary-400 group-hover:bg-opacity-20">
                            {setPriceTableValue(pricePlan.light)}
                        </td>
                        <td className="bg-table-100  group-hover:bg-primary-400 group-hover:bg-opacity-20">
                            {setPriceTableValue(pricePlan.pro)}
                        </td>
                        <td className="bg-table-100  group-hover:bg-primary-400 group-hover:bg-opacity-20">
                            {setPriceTableValue(pricePlan.elegance)}
                        </td>
                    </tr>
                );
            })}

            <tr>
                <td className="py-4 font-semibold tracking-wider text-accent-200">
                    {t('ÖZELLİKLER / MODÜLLER')}
                </td>
                <td className="bg-table-100"></td>
                <td className="bg-table-100"></td>
                <td className="bg-table-100"></td>
            </tr>
            {pricePlanDetails.modules.map((pricePlan) => {
                return (
                    <tr key={pricePlan.category} className="group">
                        <td className="pl-6 font-semibold group-hover:bg-gray-500 group-hover:bg-opacity-5">
                            {t(pricePlan.category)}
                        </td>
                        <td className="bg-table-100  group-hover:bg-primary-400 group-hover:bg-opacity-20">
                            {setPriceTableValue(pricePlan.light)}
                        </td>
                        <td className="bg-table-100  group-hover:bg-primary-400 group-hover:bg-opacity-20">
                            {setPriceTableValue(pricePlan.pro)}
                        </td>
                        <td className="bg-table-100  group-hover:bg-primary-400 group-hover:bg-opacity-20">
                            {setPriceTableValue(pricePlan.elegance)}
                        </td>
                    </tr>
                );
            })}

            <tr>
                <td className="py-4 font-semibold tracking-wider text-accent-200">
                    {t('YARDIMCI ÖZELLİKLER / MODÜLLER')}
                </td>
                <td className="bg-table-100"></td>
                <td className="bg-table-100"></td>
                <td className="bg-table-100"></td>
            </tr>
            {pricePlanDetails.helperModules.map((pricePlan) => {
                return (
                    <tr key={pricePlan.category} className="group">
                        <td className="pl-6 font-semibold group-hover:bg-gray-500 group-hover:bg-opacity-5">
                            {t(pricePlan.category)}
                        </td>
                        <td className="bg-table-100  group-hover:bg-primary-400 group-hover:bg-opacity-20">
                            {setPriceTableValue(pricePlan.light)}
                        </td>
                        <td className="bg-table-100  group-hover:bg-primary-400 group-hover:bg-opacity-20">
                            {setPriceTableValue(pricePlan.pro)}
                        </td>
                        <td className="bg-table-100  group-hover:bg-primary-400 group-hover:bg-opacity-20">
                            {setPriceTableValue(pricePlan.elegance)}
                        </td>
                    </tr>
                );
            })}
        </tbody>
    );
};

export default TableBody;
