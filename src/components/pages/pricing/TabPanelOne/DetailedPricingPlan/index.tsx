import { Disclosure } from '@headlessui/react';
import { useTrans } from '@src/hooks';
import { ChevronRightIcon } from '@src/icons/solid';
import { twMerge } from '@src/utils';
import DesktopDetailedPricingPlan from './desktop';
import MobileDetailedPricingPlan from './mobile';

const DetailedPricingPlan = () => {
    const t = useTrans();

    return (
        <Disclosure defaultOpen>
            {({ open }) => (
                <>
                    <Disclosure.Button className="container my-6 flex items-center justify-center gap-4 border-b-2 py-6 text-2xl font-medium">
                        <h2>{t('Tüm Plan Özelliklerini Görüntüle')}</h2>
                        <ChevronRightIcon
                            className={twMerge(
                                'h-5 w-5 rotate-90 transition-transform duration-300',
                                open && '-rotate-90'
                            )}
                        />
                    </Disclosure.Button>

                    <Disclosure.Panel>
                        <DesktopDetailedPricingPlan />
                        <MobileDetailedPricingPlan />
                    </Disclosure.Panel>
                </>
            )}
        </Disclosure>
    );
};

export default DetailedPricingPlan;
