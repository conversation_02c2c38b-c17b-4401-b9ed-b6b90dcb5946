import ScaleIcon from '@public/images/icons/Scale.svg';
import { ExtendedImage, SolidButton } from '@src/components/shared';
import { useTrans } from '@src/hooks';

interface PricePlanCategoryCardProps {
    title: string;
    description: string;
}

const PricePlanCategoryCard = ({
    title,
    description
}: PricePlanCategoryCardProps) => {
    const t = useTrans();

    return (
        <th className="min-w-[220px] px-4">
            <article className="flex flex-col items-center gap-3">
                <div className="flex items-center gap-4">
                    <div>
                        <ExtendedImage
                            className="w-6"
                            src={ScaleIcon}
                            alt={t(title)}
                        />
                    </div>

                    <p>{t(title)}</p>
                </div>

                <p className="text-center text-sm font-light">
                    {t(description)}
                </p>

                <div className="inline-block">
                    <SolidButton className="px-6 py-3" href="signup">
                        {t('Ücretsiz Dene')}
                    </SolidButton>
                </div>
            </article>
        </th>
    );
};

export default PricePlanCategoryCard;
