export const pricePlanDetails = {
    general: [
        {
            category: 'Depolama',
            light: "320 GB(SSD)'a kadar depolama",
            pro: '8 TB(SSD)’a kadar depolama',
            elegance: 'Sınırs<PERSON>z Depolama'
        },
        {
            category: 'RAM',
            light: '16 GB’a kadar bellek',
            pro: '256 GB’a kadar bellek',
            elegance: 'Artırımlı ölçeklenebilir bellek'
        },
        {
            category: 'vCPU',
            light: '8 CPUs’a kadar',
            pro: '32 CPUs’a kadar',
            elegance: 'Artırımlı ölçeklenebilir CPUs'
        },
        {
            category: 'Yedekler',
            light: '<PERSON><PERSON><PERSON>k yedek hizmeti',
            pro: '<PERSON><PERSON><PERSON>k yedek hizmeti',
            elegance: '<PERSON><PERSON><PERSON>k yedek hizmeti'
        },
        {
            category: 'Esnek Ölçeklenebilirlik',
            light: false,
            pro: true,
            elegance: true
        },
        {
            category: 'Özelleştirilebilir',
            light: false,
            pro: false,
            elegance: true
        }
    ],
    modules: [
        {
            category: 'Satı<PERSON>',
            light: true,
            pro: true,
            elegance: true
        },
        {
            category: 'CRM',
            light: true,
            pro: true,
            elegance: true
        },
        {
            category: 'Muhasebe',
            light: true,
            pro: true,
            elegance: true
        },
        {
            category: 'Finans',
            light: true,
            pro: true,
            elegance: true
        },
        {
            category: 'Satın Alma',
            light: true,
            pro: true,
            elegance: true
        },
        {
            category: 'Envanter / Depo-Stok',
            light: true,
            pro: true,
            elegance: true
        },
        {
            category: 'Gelir / Gider',
            light: true,
            pro: true,
            elegance: true
        },
        {
            category: 'Varlık Yönetimi',
            light: true,
            pro: true,
            elegance: true
        },
        {
            category: 'Basit Üretim',
            light: true,
            pro: true,
            elegance: true
        },
        {
            category: 'İnsan Kaynakları',
            light: 'Opsiyonel',
            pro: 'Opsiyonel',
            elegance: true
        },
        {
            category: 'Saha Servis',
            light: false,
            pro: 'Opsiyonel',
            elegance: true
        },
        {
            category: 'Proje Yönetimi',
            light: false,
            pro: 'Opsiyonel',
            elegance: 'Opsiyonel'
        },
        {
            category: 'Gelişmiş Üretim',
            light: false,
            pro: 'Dinamik Opsiyonel',
            elegance: 'Dinamik Opsiyonel'
        }
    ],
    helperModules: [
        {
            category: 'EnterBI - İş zekası',
            light: false,
            pro: 'Opsiyonel',
            elegance: 'Opsiyonel'
        },
        {
            category: 'Analitik',
            light: true,
            pro: true,
            elegance: true
        },
        {
            category: 'PCM / Dinamik Ürün Konfigüratörü',
            light: false,
            pro: 'Opsiyonel',
            elegance: true
        },
        {
            category: 'Yardım Masası',
            light: false,
            pro: true,
            elegance: true
        },
        {
            category: 'Sağlık Medikal',
            light: false,
            pro: 'Sektör Bazlı',
            elegance: 'Sektör Bazlı'
        },
        {
            category: 'EnterFlow - Akış Yönetimi',
            light: false,
            pro: true,
            elegance: true
        },
        {
            category: 'E-Ticaret Yönetimi',
            light: false,
            pro: 'Opsiyonel',
            elegance: 'Opsiyonel'
        },
        {
            category: 'E-İşlemler / E-Fatura',
            light: true,
            pro: true,
            elegance: true
        },
        {
            category: 'Aktivite Yönetimi',
            light: true,
            pro: true,
            elegance: true
        },
        {
            category: 'Sanal Pos Entegrasyonu',
            light: true,
            pro: true,
            elegance: true
        },
        {
            category: 'Kargo Entegrasyonu',
            light: true,
            pro: true,
            elegance: true
        },
        {
            category: 'Online Ekstre',
            light: true,
            pro: true,
            elegance: true
        },
        {
            category: 'Google Maps',
            light: false,
            pro: true,
            elegance: true
        },
        {
            category: 'Pazaryeri Entegrasyonu',
            light: false,
            pro: 'Dinamik Opsiyonel',
            elegance: 'Dinamik Opsiyonel'
        },
        {
            category: 'Mükellef Sorgulama',
            light: true,
            pro: true,
            elegance: true
        }
    ]
};
