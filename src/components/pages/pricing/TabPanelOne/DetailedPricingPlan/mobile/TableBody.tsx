import { useCallback, useState } from 'react';
import { useTrans } from '@src/hooks';
import { CheckIcon } from '@src/icons/solid';
import { twMerge } from '@src/utils';
import { pricePlanDetails } from '../shared/PricePlanDetails';

const categoryNames: Record<string, string> = {
    general: 'GENEL',
    modules: 'ÖZELLİKLER / MODÜLLER',
    helperModules: 'YARDIMCI ÖZELLİKLER / MODÜLLER'
};

const TableBody = () => {
    const [pricePlanCategory, setPricePlanCategory] = useState(
        Object.keys(pricePlanDetails)[0]
    );

    const t = useTrans();

    const setPriceTableValue = useCallback((value: string | boolean) => {
        if (value === true) {
            return <CheckIcon className="mx-auto h-5 w-5 text-primary-500" />;
        }
        if (value === false) {
            return <span>-</span>;
        }
        if (typeof value === 'string') {
            return t(value);
        }
        // eslint-disable-next-line
    }, []);

    return (
        <>
            <tbody>
                <tr>
                    {Object.keys(pricePlanDetails).map((category) => {
                        return (
                            <td
                                key={category}
                                className="py-4 text-sm font-medium tracking-wider text-accent-200"
                            >
                                <button
                                    className={twMerge(
                                        'mr-3 border-t-2 border-transparent px-2 py-1',
                                        pricePlanCategory === category &&
                                            'border-primary-200'
                                    )}
                                    onClick={() =>
                                        setPricePlanCategory(category)
                                    }
                                >
                                    {t(categoryNames[category])}
                                </button>
                            </td>
                        );
                    })}
                </tr>
            </tbody>

            <tbody className="mobile-table-body">
                {/* @ts-ignore */}
                {pricePlanDetails[pricePlanCategory].map((pricePlan) => {
                    return (
                        <tr key={pricePlan.category}>
                            <td>
                                <table>
                                    <thead className="w-full bg-table-200">
                                        <tr>
                                            <th className="flex items-center p-6">
                                                <div className="absolute left-10 text-sm">
                                                    {t(pricePlan.category)}
                                                </div>
                                            </th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                            <td className=" bg-table-100">
                                                {setPriceTableValue(
                                                    pricePlan.light
                                                )}
                                            </td>
                                            <td className="border-l border-r bg-table-100">
                                                {setPriceTableValue(
                                                    pricePlan.pro
                                                )}
                                            </td>
                                            <td className=" bg-table-100">
                                                {setPriceTableValue(
                                                    pricePlan.elegance
                                                )}
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </td>
                        </tr>
                    );
                })}
            </tbody>
        </>
    );
};

export default TableBody;
