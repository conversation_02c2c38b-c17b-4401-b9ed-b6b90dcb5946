import { ReactNode } from 'react';
import { ExtendedImage } from '@src/components/shared';
import { useTrans } from '@src/hooks';
import { twMerge } from '@src/utils';

interface PricingCardWrapperProps {
    src: any;
    planTitle: string;
    description: string;
    children: ReactNode;
    isRecommended?: boolean;
}

const PricingCardWrapper = ({
    description,
    planTitle,
    src,
    children,
    isRecommended
}: PricingCardWrapperProps) => {
    const t = useTrans();

    return (
        <article
            className={twMerge(
                'card-shadow relative grid gap-4 overflow-hidden rounded-4xl border bg-white p-12',
                isRecommended && 'border-primary-600'
            )}
        >
            {isRecommended && (
                <p className="absolute left-0 top-0 w-full bg-primary-600 py-1.5 text-center text-sm text-white">
                    {t('En Çok Tercih Edilen')}
                </p>
            )}
            <div className="flex items-center gap-4 border-b border-accent-100 pb-4">
                <div>
                    <ExtendedImage
                        className="w-8"
                        src={src}
                        alt={t(planTitle)}
                    />
                </div>
                <span className="font-means text-2xl">{t(planTitle)}</span>
            </div>
            <p>{t(description)}</p>
            {children}
        </article>
    );
};

export default PricingCardWrapper;
