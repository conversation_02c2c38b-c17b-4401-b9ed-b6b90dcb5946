import { useTrans } from '@src/hooks';
import DetailedPricingPlan from './DetailedPricingPlan';
import PricingCards from './PricingCards';

const TabPanelOne = () => {
    const t = useTrans();

    return (
        <>
            <div className="container grid gap-4 py-12">
                <p className="text-center font-means text-3xl">
                    {t('EnterERP Planları')}
                </p>
                <p className="text-center text-lg">
                    {t(
                        'Google Cloud, Azure, Türk Telekom ve Digital Oceans’ta tam olarak yönetilen, küresel bulut ERP sistemi'
                    )}
                </p>

                <PricingCards />
            </div>

            <DetailedPricingPlan />
        </>
    );
};

export default TabPanelOne;
