import { Tab } from '@headlessui/react';
import { ExtendedImage } from '@src/components/shared';
import { useTrans } from '@src/hooks';

interface PricingCategoryTabProps {
    title: string;
    description: string;
    src: any;
}

const PricingCategoryTab = ({
    src,
    description,
    title
}: PricingCategoryTabProps) => {
    const t = useTrans();

    return (
        <Tab
            className={({ selected }) =>
                selected
                    ? 'flex w-full items-center gap-6 rounded-4xl rounded-b-none border-4 border-b-0 border-primary-500 bg-white p-6 md:w-fit'
                    : 'group flex w-full items-center gap-6 rounded-4xl rounded-b-none border-4 border-b-0 border-transparent bg-white p-6 md:w-fit'
            }
        >
            <div>
                <ExtendedImage
                    src={src}
                    alt={t(title)}
                    className="w-8 transition-all duration-200 group-hover:scale-110"
                />
            </div>
            <div className="flex flex-col items-start">
                <span className="text-left text-lg font-bold text-accent-200">
                    {t(title)}
                </span>
                <span className="text-left text-accent-200">
                    {t(description)}
                </span>
            </div>
        </Tab>
    );
};

export default PricingCategoryTab;
