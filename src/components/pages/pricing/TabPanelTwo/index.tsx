import { SolidButton } from '@src/components/shared';
import { useTrans } from '@src/hooks';

const TabPanelTwo = () => {
    const t = useTrans();

    return (
        <article className="container grid gap-4 py-12">
            <h2 className="text-center font-means text-3xl">
                {t('EnterERP İsteğe Bağlı Özel Kurulum')}
            </h2>
            <p className="text-center text-lg">
                {t(
                    "EnterERP'yi şirket içinde veya özel bulutunuzda çalıştırmanın en iyi yolu"
                )}
            </p>
            <div className="card-shadow grid place-items-center gap-6 rounded-4xl border bg-white p-8">
                <h3 className="text-center font-means text-2xl">
                    {t('Öne Çıkanlar')}
                </h3>

                <ul className="grid list-disc gap-2 font-light md:grid-cols-2">
                    <li>{t('EnterERP kurumsal sunucu')}</li>
                    <li>{t('Özel local veya bulut yedekleme')}</li>
                    <li>{t('Kurumsal güvenlik özellikleri')}</li>
                    <li>{t('Kesintisiz enerji kaynağı')}</li>
                    <li>{t('Ubuntu işletim sistemi')}</li>
                    <li>{t('Ölçeklenebilir ve genişletilebilir altyapı')}</li>
                    <li>{t('Sabit bant genişliği')}</li>
                </ul>

                <SolidButton href="signup">
                    {t('Daha fazla bilgi edin')}
                </SolidButton>
            </div>
        </article>
    );
};

export default TabPanelTwo;
