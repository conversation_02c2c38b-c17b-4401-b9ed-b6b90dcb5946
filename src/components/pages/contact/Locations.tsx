import { Fragment } from 'react';
import { Tab } from '@headlessui/react';
import { useTrans } from '@src/hooks';
import { twMerge } from '@src/utils';
import { ExtendedImage } from '@src/components/shared';
import WorldIcon from '@public/images/pages/contact/icons/World.svg';
import BuildingsIcon from '@public/images/pages/contact/icons/Buildings.svg';

const locationsData = {
    Türkiye: [
        {
            city: 'İstanbul Genel Merkez',
            address:
                'Piazza Ofis Cevizli Mah. Tugay Yolu Cad. No:69/A Kat:18, 34846 Maltepe/İstanbul'
        },
        {
            city: 'Ankara Ofis',
            address:
                'Kavaklıdere, Atatürk Blv No: 185 D:Kat:5 & 6, 06680 Çankaya/Ankara'
        },
        {
            city: 'İzmir Ofis',
            address:
                'Adalet Mah. Manas Bulv. No: 31/7 B Block Folkart Towers Bayraklı/İzmir'
        },
        {
            city: 'Bursa Ofis',
            address:
                'Konak Mah. Lefkoşe Cad. Barış (120) Sok. Ofis + Plaza No:3 Kat:1 D:10 Nilüfer/Bursa'
        },
        {
            city: 'Konya Ofis',
            address:
                'Musalla Bağları Mahallesi, Kule Cd. Selçuklu Kulesi Sit. No:2, Kat:20 No:31 Selçuklu/Konya'
        },
        {
            city: 'Adana Ofis',
            address:
                'Reşatbey, Mimar Semih Rüstem, Atatürk Cad. Kocavezir İş Merkezi No:18 Seyhan/Adana'
        }
        // {
        //     city: 'Çorum Ofis',
        //     address:
        //         'Ankara Yolu 12. Km. OSB No: 7 Çorum Teknokent Merkez/ÇORUM'
        // },
    ],
    KKTC: [
        {
            city: 'Kuzey Kıbrıs Türk Cumhuriyeti Ofis',
            address:
                '11.SOKAK, No: 4/A, 99100 Orta Köy/Lefkoşa  KUZEY KIBRIS TÜRK CUMHURİYETİ'
        }
    ]
};

interface LocationCardProps {
    city: string;
    address: string;
}

const LocationCard = ({ city, address }: LocationCardProps) => {
    const t = useTrans();

    return (
        <div className="grid gap-5 rounded-4xl border p-7 md:gap-10 md:p-10">
            <h2 className="font-means text-2xl">{t(city)}</h2>
            <div className="flex items-center gap-5">
                <ExtendedImage
                    src={WorldIcon}
                    alt={t('Dünya')}
                    className="aspect-1"
                    containerClassName="w-12 h-auto"
                />
                <p className="flex-1 text-accent-200">{address}</p>
            </div>
        </div>
    );
};

const Locations = () => {
    const t = useTrans();

    return (
        <section className="card-shadow container rounded-4xl border bg-white p-5 md:p-10">
            <Tab.Group>
                <Tab.List className="relative flex flex-wrap items-center justify-between gap-6">
                    <div className="left-0 top-1 flex items-center gap-4 lg:absolute">
                        <ExtendedImage
                            src={BuildingsIcon}
                            alt={t('Topluluk')}
                            className="aspect-1"
                            containerClassName="w-14 h-auto"
                        />
                        <p className="flex-1 font-means text-2xl">
                            {t('Ofislerimiz')}
                        </p>
                    </div>
                    <div className="card-shadow flex w-full flex-col flex-wrap items-center gap-4 rounded-4xl border bg-white p-3 sm:w-fit sm:flex-row lg:mx-auto">
                        {Object.keys(locationsData).map((locationCountry) => (
                            <Tab as={Fragment} key={locationCountry}>
                                {({ selected }) => (
                                    <button
                                        className={twMerge(
                                            'whitespace-nowrap rounded-full border border-transparent px-8 py-2 transition duration-200 hover:border-primary-200',
                                            selected &&
                                                'bg-primary-200 text-white'
                                        )}
                                    >
                                        {locationCountry}
                                    </button>
                                )}
                            </Tab>
                        ))}
                    </div>
                </Tab.List>
                <Tab.Panels as={Fragment}>
                    {Object.values(locationsData).map((locations, index) => (
                        <Tab.Panel
                            key={index}
                            className="mt-4 grid w-full gap-4 lg:grid-cols-2 [&>*:nth-child(odd)]:last:col-span-full"
                        >
                            {locations.map((location) => (
                                <LocationCard
                                    key={location.city}
                                    city={location.city}
                                    address={location.address}
                                />
                            ))}
                        </Tab.Panel>
                    ))}
                </Tab.Panels>
            </Tab.Group>
        </section>
    );
};

export default Locations;
