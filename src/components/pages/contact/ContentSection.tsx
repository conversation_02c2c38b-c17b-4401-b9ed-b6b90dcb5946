import ContactCard from './ContactCard';
import Form from './Form';
import Locations from './Locations';

const ContentSection = () => {
    return (
        <section className="relative mb-10 grid gap-10">
            <svg
                width="1969"
                height="2000"
                viewBox="0 0 1969 2000"
                xmlns="http://www.w3.org/2000/svg"
                className="absolute -top-24 right-0 -z-10 fill-primary-50"
            >
                <path d="M250 0C388 0 500 112 500 250C500 388 612 500 750 500C888 500 1000 388 1000 250C1000 112 1112 0 1250 0H1750C1888 0 2000 112 2000 250V1500C2000 1776 1776 2000 1500 2000H250C112 2000 0 1888 0 1750V1250C0 1112 112 1000 250 1000C388 1000 500 888 500 750V747.999C500 609.999 388 497.999 250 497.999C112 497.999 0 385.999 0 247.999C0 111.999 112 0 250 0Z" />
            </svg>

            <div className="container grid gap-10 xl:grid-cols-2">
                <ContactCard />
                <Form />
            </div>

            <Locations />
        </section>
    );
};

export default ContentSection;
