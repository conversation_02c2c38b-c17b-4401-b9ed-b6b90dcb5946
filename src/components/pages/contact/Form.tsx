import dynamic from 'next/dynamic';
import { FormProvider, useForm } from 'react-hook-form';
import {
    Input,
    Phone,
    PrivacyManifest,
    SubmitPartial,
    TermsManifest,
    Textarea
} from '@src/components/shared/Form';

import { useSubmit, useTrans } from '@src/hooks';
import { FormValues } from '@src/interfaces';

const Toast = dynamic(() => import('@src/components/shared/Form/Toast'));

const Form = () => {
    const t = useTrans();

    const methods = useForm<FormValues>({ mode: 'onChange' });

    const { isSubmitting, status, showToast, onSubmit } = useSubmit({
        methods
    });

    return (
        <div className="card-shadow flex w-full flex-col items-start gap-8 rounded-4xl border bg-white p-6 md:p-10">
            <div className="grid gap-8">
                <h2 className="text-center font-means text-2xl lg:text-4xl">
                    {t('Bize Ulaşın')}
                </h2>
            </div>

            <Toast
                showToast={showToast}
                status={status}
                successMessage="Mesajınız başarıyla gönderildi."
                errorMessage="Bir şeyler ters gitti. Lütfen daha sonra tekrar deneyin."
            />

            <FormProvider {...methods}>
                <form
                    className="contact-form mx-auto flex flex-col justify-between gap-7"
                    onSubmit={methods.handleSubmit(onSubmit)}
                >
                    <fieldset
                        disabled={methods.formState.isSubmitting}
                        className="grid w-full gap-7 lg:grid-cols-2"
                    >
                        <Input name="name" label="Ad *" required />
                        <Input name="surname" label="Soyad *" required />
                        <Input name="mail" label="E-Posta (Opsiyonel)" />
                        <Phone />
                        <div className="lg:col-span-2">
                            <Input
                                name="company"
                                label="İşletme Adı *"
                                required
                            />
                        </div>
                    </fieldset>

                    <Textarea
                        name="description"
                        rows={5}
                        placeholder={t(
                            'Size nasıl yardımcı olabileceğimiz hakkında daha detaylı bilgi verin.'
                        )}
                    />

                    <PrivacyManifest />

                    <TermsManifest />

                    <SubmitPartial
                        isSubmitting={isSubmitting}
                        title="Gönder"
                        type="submit"
                    />
                </form>
            </FormProvider>
        </div>
    );
};

export default Form;
