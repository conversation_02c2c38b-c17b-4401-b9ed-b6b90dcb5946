import CommunityInfoIcon from '@public/images/pages/contact/icons/CommunityInfo.svg';
import SupportIcon from '@public/images/pages/contact/icons/Support.svg';
import { ExtendedImage } from '@src/components/shared';
import { useTrans } from '@src/hooks';

const ContactCard = () => {
    const t = useTrans();

    return (
        <div className="grid gap-10">
            <div className="card-shadow flex items-center gap-6 rounded-4xl border bg-white p-10 lg:gap-10">
                <ExtendedImage
                    src={SupportIcon}
                    alt={t('Destek')}
                    className="aspect-1"
                    containerClassName="w-14 h-auto"
                />
                <div className="grid gap-3 lg:gap-6">
                    <h2 className="font-means text-xl lg:text-4xl">
                        {t('<PERSON>tek Hattımız')}
                    </h2>
                    <a
                        href="tel:+908505820035"
                        className="text-accent-200 underline transition duration-200 hover:opacity-70 lg:text-2xl"
                    >
                        0850 582 0035
                    </a>
                </div>
            </div>
            <div className="card-shadow flex items-center gap-6 rounded-4xl border bg-white p-10 lg:gap-10">
                <ExtendedImage
                    src={CommunityInfoIcon}
                    alt={t('Topluluk')}
                    className="aspect-1"
                    containerClassName="w-14 h-auto"
                />
                <div className="grid gap-3 lg:gap-6">
                    <h2 className="font-means text-xl lg:text-4xl">
                        {t('E-Posta')}
                    </h2>
                    <a
                        href="mailto:<EMAIL>"
                        className="text-accent-200 underline transition duration-200 hover:opacity-70 lg:text-2xl"
                    >
                        <EMAIL>
                    </a>
                </div>
            </div>
        </div>
    );
};

export default ContactCard;
