import { useRouter } from 'next/router';
import { useMemo } from 'react';
import { ExtendedImage } from '@src/components/shared';
import { useTrans } from '@src/hooks';

interface HeroSectionProps {
    title: string;
    image: string;
    category: string;
    date: string;
}

const HeroSection = ({ category, date, image, title }: HeroSectionProps) => {
    const t = useTrans();

    const router = useRouter();

    const calculatedDate = useMemo(() => {
        return new Date(date).toLocaleDateString(router.locale, {
            month: 'long',
            year: 'numeric'
        });
    }, [date, router.locale]);

    return (
        <div className="relative">
            <ExtendedImage
                src={image}
                alt={t(title)}
                fill
                priority
                quality={90}
                className="rounded-4xl object-cover object-center drop-shadow-xl"
                containerClassName="aspect-w-2 aspect-h-1"
            />
            <div className="bottom-6 left-0 right-0 grid gap-4 rounded-3xl bg-white py-6 pb-0 text-center lg:absolute lg:mx-6 lg:px-6 lg:pb-6">
                <p className="text-sm uppercase tracking-widest text-primary-400">
                    {t(category)}
                </p>
                <h1 className="font-means text-2xl md:text-4xl">{t(title)}</h1>
                <time dateTime={date} className="text-right text-accent-200">
                    {calculatedDate}
                </time>
            </div>
        </div>
    );
};

export default HeroSection;
