import { Transition } from '@headlessui/react';
import { useEffect, useState } from 'react';
import { ExtendedLink } from '@src/components/shared';
import { useTrans, useWindowDimensions } from '@src/hooks';
import { ChevronRightIcon } from '@src/icons/solid';
import { twMerge } from '@src/utils';
import { useBlog } from '@src/context';
import {
    FacebookIcon,
    InstagramIcon,
    LinkedInIcon,
    TwitterIcon
} from '@src/icons/brands';
import { useRouter } from 'next/router';

type SubTitles = {
    id: string;
    title: string;
};

interface IngredientsProps {
    subTitles: SubTitles[];
    activeSubTitle: SubTitles | undefined;
    scrollIndicator: number;
}

const MobileIngredients = ({
    activeSubTitle,
    subTitles,
    scrollIndicator
}: IngredientsProps) => {
    const [openModal, setOpenModal] = useState(false);

    const router = useRouter();

    const { tags } = useBlog();

    const t = useTrans();

    const { viewportWidth } = useWindowDimensions();

    useEffect(() => {
        if (viewportWidth > 1024) setOpenModal(false);
    }, [viewportWidth]);

    return (
        <>
            {Array.isArray(subTitles) && subTitles.length > 0 ? (
                <div
                    onClick={() => setOpenModal((prev) => !prev)}
                    className="fixed left-0 right-0 top-14 z-20 flex h-14 cursor-pointer items-center justify-center bg-primary-200 lg:hidden"
                >
                    <div
                        style={{ width: `${scrollIndicator}%` }}
                        className="absolute inset-0 h-14 max-w-full bg-primary-600 transition-all duration-500"
                    ></div>
                    <div className="z-30 flex items-center justify-center gap-4 px-6 text-center text-sm text-white">
                        <span>{activeSubTitle?.title}</span>
                        <ChevronRightIcon
                            className={twMerge(
                                'h-5 w-5 transition duration-300',
                                openModal && 'rotate-90'
                            )}
                        />
                    </div>
                </div>
            ) : null}

            <Transition
                show={openModal}
                enter="transform [transition:transform_.3s,opacity_.5s]"
                enterFrom="-translate-y-full translate-x-0 opacity-0"
                enterTo="translate-y-0 opacity-100"
                leave="transform [transition:transform_.3s,opacity_.5s]"
                leaveFrom="translate-y-0 opacity-100"
                leaveTo="-translate-y-full translate-x-0 opacity-0"
                className="fixed bottom-0 left-0 right-0 top-28 z-30 bg-primary-600"
            >
                <div className="container h-full overflow-y-auto pt-12 text-white">
                    <p className="mb-4 font-means text-xl">
                        {t('İçindekiler')}
                    </p>
                    {subTitles?.map((subTitle) => {
                        const isActiveSubTitle =
                            subTitle.id === activeSubTitle?.id;

                        return (
                            <div key={subTitle.id}>
                                <ExtendedLink
                                    onClick={() => setOpenModal(false)}
                                    href={`#${subTitle.id}`}
                                    scroll={false}
                                    className={twMerge(
                                        'flex items-center gap-2 py-2 text-sm',
                                        isActiveSubTitle && 'text-primary-200'
                                    )}
                                >
                                    <p>{subTitle.title}</p>
                                </ExtendedLink>
                            </div>
                        );
                    })}

                    {Array.isArray(tags) && tags.length > 0 && (
                        <>
                            <p className="mb-6 mt-4 font-means text-xl">
                                {'Etiketler'}
                            </p>
                            <ul className="flex flex-wrap gap-2 gap-y-5">
                                {tags?.map((tag) => {
                                    return (
                                        <li key={tag._id}>
                                            <ExtendedLink
                                                href={`/blog/tags/${tag.slug}`}
                                                className={twMerge(
                                                    'w-fit whitespace-nowrap rounded-full border px-4 py-2 text-sm font-medium transition duration-200'
                                                )}
                                            >
                                                <span
                                                    className={twMerge(
                                                        'text-primary-400'
                                                    )}
                                                >
                                                    #
                                                </span>
                                                {t(
                                                    tag.name.replaceAll(' ', '')
                                                )}
                                            </ExtendedLink>
                                        </li>
                                    );
                                })}
                            </ul>
                        </>
                    )}

                    <p className="my-6 font-means text-xl">{'Paylaş'}</p>
                    <ul className="flex items-center gap-6">
                        <li>
                            <a
                                title="Facebook"
                                href={`https://www.facebook.com/sharer/sharer.php?u=https://www.entererp.com${router.asPath}`}
                                target="_blank"
                                rel="noopener noreferrer"
                            >
                                <FacebookIcon className="h-7 w-7 transition duration-300 hover:text-primary-400" />
                            </a>
                        </li>
                        <li>
                            <a
                                title="Instagram"
                                href={`https://www.instagram.com/?url=https://www.entererp.com${router.asPath}`}
                                target="_blank"
                                rel="noopener noreferrer"
                            >
                                <InstagramIcon className="h-7 w-7 transition duration-300 hover:text-primary-400" />
                            </a>
                        </li>
                        <li>
                            <a
                                title="Linkedin"
                                href={`https://www.linkedin.com/shareArticle?url=https://www.entererp.com${router.asPath}`}
                                target="_blank"
                                rel="noopener noreferrer"
                            >
                                <LinkedInIcon className="h-7 w-7 transition duration-300 hover:text-primary-400" />
                            </a>
                        </li>
                        <li>
                            <a
                                title="Twitter"
                                href={`https://twitter.com/intent/tweet?url=https://www.entererp.com${router.asPath}`}
                                target="_blank"
                                rel="noopener noreferrer"
                            >
                                <TwitterIcon className="h-7 w-7 transition duration-300 hover:text-primary-400" />
                            </a>
                        </li>
                    </ul>
                </div>
            </Transition>
        </>
    );
};

export default MobileIngredients;
