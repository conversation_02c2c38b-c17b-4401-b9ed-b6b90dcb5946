import { ExtendedLink } from '@src/components/shared';
import { useTrans } from '@src/hooks';
import { twMerge } from '@src/utils';

type SubTitles = {
    id: string;
    title: string;
};

interface IngredientsProps {
    subTitles: SubTitles[];
    activeSubTitle: SubTitles | undefined;
}

const Ingredients = ({ subTitles, activeSubTitle }: IngredientsProps) => {
    const t = useTrans();

    return Array.isArray(subTitles) && subTitles.length > 0 ? (
        <aside className="card-shadow grid gap-4 rounded-4xl p-5">
            <p className="font-means text-lg">{t('İçindekiler')}</p>
            <ul className="grid gap-4">
                {subTitles.map((subTitle) => {
                    const isActiveSubTitle = subTitle.id === activeSubTitle?.id;

                    return (
                        <li key={subTitle.id}>
                            <ExtendedLink
                                href={`#${subTitle.id}`}
                                scroll={false}
                                className={twMerge(
                                    'flex items-center gap-2 text-sm text-primary-200 transition duration-200',
                                    !isActiveSubTitle &&
                                        'text-primary-800 hover:opacity-50'
                                )}
                            >
                                <span
                                    className={twMerge(
                                        'inline-block h-2.5 w-2.5 rounded-full',
                                        isActiveSubTitle
                                            ? 'bg-primary-200'
                                            : 'bg-black'
                                    )}
                                />
                                <p className="flex-1">{subTitle.title}</p>
                            </ExtendedLink>
                        </li>
                    );
                })}
            </ul>
        </aside>
    ) : null;
};

export default Ingredients;
