import { useEffect, useRef, useState } from 'react';
import { DemoSection, ProductCards } from '@src/components/shared';
import { BlogPost } from '@src/interfaces';
import Tags from '../shared/Tags';
import { isValidCategory, isValidImage } from '../shared/utils';
import HeroSection from './HeroSection';
import Ingredients from './Ingredients';
import MobileIngredients from './MobileIngredients';
import SocialLinks from './SocialLinks';

const SCROLL_OFFSET = 150;

const BlogContent = ({
    blogPost: { content, images, title, postedOn, categories, subTitles }
}: {
    blogPost: BlogPost;
}) => {
    const image = isValidImage(images);
    const category = isValidCategory(categories);

    const [scrollIndicator, setScrollIndicator] = useState(0);
    const [showIndicator, setShowIndicator] = useState(false);
    const [activeItems, setActiveItems] = useState<Record<string, boolean>>({});

    const ref = useRef<HTMLDivElement>(null);

    useEffect(() => {
        subTitles?.map((subTitle) =>
            setActiveItems((prev) => ({
                ...prev,
                [subTitle.id]:
                    document.getElementById(subTitle.id)?.offsetTop! <
                    window.scrollY + SCROLL_OFFSET * 1.25
            }))
        );

        const elementDimensions = ref.current?.getBoundingClientRect();

        const handleScroll = () => {
            const offsetTop = window.pageYOffset;

            setShowIndicator(elementDimensions?.top! < SCROLL_OFFSET);

            setScrollIndicator(
                Math.floor((offsetTop / elementDimensions?.height!) * 100)
            );
        };

        window.addEventListener('scroll', handleScroll);

        return () => {
            window.removeEventListener('scroll', handleScroll);
        };
    }, [scrollIndicator, subTitles]);

    useEffect(() => {
        document.getElementsByTagName('html')[0].style.scrollBehavior =
            'smooth';
    }, []);

    const activeSubTitle = subTitles
        ?.slice()
        .reverse()
        .find((subTitle) => activeItems[subTitle.id] === true);

    return (
        <>
            {showIndicator && (
                <MobileIngredients
                    subTitles={subTitles}
                    activeSubTitle={activeSubTitle}
                    scrollIndicator={scrollIndicator}
                />
            )}

            <section className="container pt-10">
                <HeroSection
                    image={image}
                    title={title}
                    category={category}
                    date={postedOn}
                />

                <div className="grid gap-10 py-10 lg:grid-cols-12 lg:place-items-start">
                    <div
                        ref={ref}
                        className="prose max-w-none lg:col-span-8"
                        dangerouslySetInnerHTML={{ __html: content }}
                    />

                    <div className="hidden w-full gap-10 lg:sticky lg:top-24 lg:col-span-4 lg:grid lg:gap-4">
                        <Ingredients
                            subTitles={subTitles}
                            activeSubTitle={activeSubTitle}
                        />
                        <Tags />
                        <SocialLinks />
                    </div>
                </div>
            </section>

            <div className="grid gap-10">
                <DemoSection variant="blue" />
                <ProductCards />
            </div>
        </>
    );
};

export default BlogContent;
