import { useRouter } from 'next/router';
import {
    FacebookIcon,
    InstagramIcon,
    LinkedInIcon,
    TwitterIcon
} from '@src/icons/brands';

const SocialLinks = () => {
    const router = useRouter();

    return (
        <aside className="card-shadow rounded-full p-5">
            <ul className="grid w-full grid-cols-4 place-items-center">
                <li>
                    <a
                        title="Facebook"
                        href={`https://www.facebook.com/sharer/sharer.php?u=https://www.entererp.com${router.asPath}`}
                        target="_blank"
                        rel="noopener noreferrer"
                    >
                        <FacebookIcon className="h-7 w-7 transition duration-300 hover:text-primary-400" />
                    </a>
                </li>
                <li>
                    <a
                        title="Instagram"
                        href={`https://www.instagram.com/?url=https://www.entererp.com${router.asPath}`}
                        target="_blank"
                        rel="noopener noreferrer"
                    >
                        <InstagramIcon className="h-7 w-7 transition duration-300 hover:text-primary-400" />
                    </a>
                </li>
                <li>
                    <a
                        title="Linkedin"
                        href={`https://www.linkedin.com/shareArticle?url=https://www.entererp.com${router.asPath}`}
                        target="_blank"
                        rel="noopener noreferrer"
                    >
                        <LinkedInIcon className="h-7 w-7 transition duration-300 hover:text-primary-400" />
                    </a>
                </li>
                <li>
                    <a
                        title="Twitter"
                        href={`https://twitter.com/intent/tweet?url=https://www.entererp.com${router.asPath}`}
                        target="_blank"
                        rel="noopener noreferrer"
                    >
                        <TwitterIcon className="h-7 w-7 transition duration-300 hover:text-primary-400" />
                    </a>
                </li>
            </ul>
        </aside>
    );
};

export default SocialLinks;
