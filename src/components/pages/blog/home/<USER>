import { useMemo } from 'react';
import { useRouter } from 'next/router';
import ReactPaginate from 'react-paginate';
import appConfig from 'app.config';
import { useBlog } from '@src/context';
import { useWindowDimensions } from '@src/hooks';
import BlogCard from '../shared/BlogCard';

const PaginatedBlogPosts = () => {
    const { blogPosts, totalBlogPostCount, hasSearch, scrollToWrapper } =
        useBlog();
    const { viewportWidth } = useWindowDimensions();
    const router = useRouter();

    const pageCount = useMemo(() => {
        const countPerPage = appConfig.featureFlags.blogPostsCountPerPage;
        return Math.ceil((totalBlogPostCount ?? countPerPage) / countPerPage);
    }, [totalBlogPostCount]);

    const handlePageClick = ({ selected }: { selected: number }) => {
        scrollToWrapper();

        const pathArrays = router.asPath.split('/');
        if (pathArrays.length === 5) pathArrays.pop();
        const linkTo = pathArrays.join('/');

        const pageParam = selected + 1;

        if (
            router.asPath.startsWith('/blog/categories') ||
            router.asPath.startsWith('/blog/tags')
        ) {
            router.push(
                selected === 0 ? linkTo : `${linkTo}/${pageParam}`,
                undefined,
                { scroll: false }
            );
        } else {
            router.push(
                selected === 0 ? '/blog' : `/blog/${pageParam}`,
                undefined,
                { scroll: false }
            );
        }
    };

    const activePage = useMemo(() => {
        const pageNumber = Number(router.asPath.split('/').pop());
        return isNaN(pageNumber) ? 0 : pageNumber - 1;
    }, [router.asPath]);

    const pageRange = useMemo(() => {
        if (viewportWidth < 410) return 0;
        if (viewportWidth < 480) return 1;
        if (viewportWidth < 768) return 3;
        if (viewportWidth < 1024) return 4;
    }, [viewportWidth]);

    const marginRange = useMemo(
        () => (viewportWidth > 1024 ? 3 : 0),
        [viewportWidth]
    );

    return (
        <>
            {blogPosts?.map((blogPost) => (
                <BlogCard key={blogPost._id} blogPost={blogPost} />
            ))}

            {!hasSearch && (
                <ReactPaginate
                    className="flex select-none items-center gap-3 [&>li:first-child]:mr-auto [&>li:last-child]:ml-auto"
                    nextLinkClassName="btn-transition inline-block cursor-pointer whitespace-nowrap rounded-[100vw] border border-primary-200 px-8 py-4 font-medium leading-4 hover:-translate-y-1 text-primary-800 hover:shadow-[0px_6px_0px_0px_#011D2A] max-md:text-xs"
                    previousLinkClassName="btn-transition inline-block cursor-pointer whitespace-nowrap rounded-[100vw] border border-primary-200 px-8 py-4 font-medium leading-4 hover:-translate-y-1 text-primary-800 hover:shadow-[0px_6px_0px_0px_#011D2A] max-md:text-xs"
                    pageLinkClassName="inline-flex items-center justify-center hover:bg-primary-50 transition z-50 rounded-3xl border px-5 py-1.5 font-medium"
                    breakLinkClassName="inline-flex items-center justify-center hover:bg-primary-50 transition z-50 rounded-3xl border px-5 py-1.5 font-medium"
                    activeLinkClassName="bg-primary-100 text-white hover:!bg-primary-100 border-primary-100"
                    disabledLinkClassName="opacity-40 pointer-events-none"
                    breakClassName="hidden md:inline-block"
                    disableInitialCallback
                    breakLabel="..."
                    nextLabel="İleri"
                    previousLabel="Geri"
                    onPageChange={handlePageClick}
                    pageCount={pageCount}
                    marginPagesDisplayed={marginRange}
                    pageRangeDisplayed={pageRange}
                    prevRel={activePage === 0 ? null : 'prev'}
                    nextRel={activePage + 1 === pageCount ? null : 'next'}
                    hrefAllControls
                    hrefBuilder={(page) => {
                        if (page === 0 || page > pageCount) return;

                        if (
                            router.asPath.startsWith('/blog/categories') ||
                            router.asPath.startsWith('/blog/tags')
                        ) {
                            const basePath = router.asPath.split('/');
                            if (basePath.length === 5) basePath.pop();
                            const linkTo = basePath.join('/');

                            return page === 1 ? linkTo : `${linkTo}/${page}`;
                        } else {
                            return page === 1 ? '/blog' : `/blog/${page}`;
                        }
                    }}
                    forcePage={activePage}
                />
            )}
        </>
    );
};

export default PaginatedBlogPosts;
