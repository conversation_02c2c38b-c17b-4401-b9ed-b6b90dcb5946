import { useBlog } from '@src/context';
import { useTrans } from '@src/hooks';
import BlogCard from '../shared/BlogCard';
import CardWrapper from '../shared/CardWrapper';

const PopularBlogPosts = () => {
    const t = useTrans();

    const { popularBlogPosts } = useBlog();

    return Array.isArray(popularBlogPosts) && popularBlogPosts.length > 0 ? (
        <CardWrapper>
            <h3 className="font-means text-lg">{t('Popüler Yazılar')}</h3>
            {popularBlogPosts.map((popularBlogPost) => (
                <BlogCard
                    isSmall
                    key={popularBlogPost._id}
                    blogPost={popularBlogPost}
                />
            ))}
        </CardWrapper>
    ) : null;
};

export default PopularBlogPosts;
