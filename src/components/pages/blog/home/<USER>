import { useBlog } from '@src/context';
import { useTrans } from '@src/hooks';
import Categories from '../shared/Categories';
import FeaturedBlogCard from '../shared/FeaturedBlogCard';
// import Tags from '../shared/Tags';
import PopularBlogPosts from './PopularBlogPosts';
import SearchBox from './SearchBox';
import PaginatedBlogPosts from './PaginatedBlogPosts';

const Blog = () => {
    const t = useTrans();

    const { featuredBlogPosts, blogPosts, pageHeading, pageDescription } =
        useBlog();

    return (
        <div className="container grid gap-10 py-10">
            <section className="grid gap-5 text-center lg:gap-10">
                <h1 className="font-means text-4xl font-semibold">
                    {t(pageHeading || 'EnterERP Bloguna Göz Atın')}
                </h1>
                <p className="text-lg text-accent-200">
                    {t(
                        pageDescription ||
                            "İşletmenizin boyutu ne olursa olsun veya iş yolculuğunuzun hangi aşamasında olursanız olun işinizi güvenle başlatmak, yürütmek ve büyütmek için ihtiyacınız olan kaynakları EnterERP Blog'da bulacaksınız."
                    )}
                </p>

                <div className="grid gap-10 md:grid-cols-2 xl:grid-cols-3">
                    {featuredBlogPosts?.map((featuredBlogPost) => (
                        <FeaturedBlogCard
                            key={featuredBlogPost._id}
                            blogPost={featuredBlogPost}
                        />
                    ))}
                </div>
            </section>

            <section>
                <h2 className="text-center font-means text-4xl font-semibold">
                    {t('En Son Eklenenler')}
                </h2>
                <p className="py-5 text-center text-lg text-accent-200 lg:py-10">
                    {t(
                        'Her Yerde Satın Alın, Her Yerde Teslim Edin, Her Yerde İade Edin'
                    )}
                </p>

                <div className="pb-4 xl:hidden">
                    <Categories />
                </div>

                <SearchBox />

                <div className="grid gap-10 xl:grid-cols-12 xl:place-items-start">
                    <div className="card-shadow grid w-full gap-4 rounded-4xl p-5 xl:col-span-8 [&>article]:border-b [&>article]:pb-4 last:[&>article]:border-b-0 last:[&>article]:pb-0">
                        <PaginatedBlogPosts />
                        {blogPosts?.length === 0 && (
                            <p className="text-center font-means text-2xl">
                                {t(
                                    'Aradığınız kriterlere uygun blog yazısı bulunamadı.'
                                )}
                            </p>
                        )}
                    </div>

                    <div className="grid w-full gap-10 xl:sticky xl:top-24 xl:col-span-4 xl:gap-4">
                        <div className="hidden xl:block">
                            <Categories />
                        </div>
                        <PopularBlogPosts />
                        {/* <Tags /> */}
                    </div>
                </div>
            </section>
        </div>
    );
};

export default Blog;
