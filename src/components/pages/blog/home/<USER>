import { useEffect, useState } from 'react';
import { useRouter } from 'next/router';
import { useBlog } from '@src/context';
import { useDebounce, useTrans } from '@src/hooks';
import { MagnifyIcon } from '@src/icons/outline';

const SearchBox = () => {
    const [value, setValue] = useState('');

    const t = useTrans();

    const debouncedValue = useDebounce(value);

    const handleChange = (event: React.ChangeEvent<HTMLInputElement>) => {
        setValue(event.target.value.toLowerCase());
    };

    const { onSearchHandler } = useBlog();

    const router = useRouter();

    useEffect(() => {
        onSearchHandler(debouncedValue);

        const cleanUpTextField = () => setValue('');

        router.events.on('routeChangeStart', cleanUpTextField);

        return () => {
            router.events.on('routeChangeStart', cleanUpTextField);
        };
    }, [debouncedValue, onSearchHandler, router.events]);

    return (
        <form
            id="blog-posts-wrapper"
            onSubmit={(e) => e.preventDefault()}
            className="relative pb-4 transition duration-200 hover:opacity-70"
        >
            <input
                onChange={handleChange}
                value={value}
                type="text"
                className="card-shadow w-full rounded-full border-transparent py-4 pl-14 outline-none"
                placeholder={t('Ara')}
                id="blog-search"
            />
            <label htmlFor="blog-search">
                <MagnifyIcon className="absolute left-4 top-4 h-6 w-6 text-accent-200" />
            </label>
        </form>
    );
};

export default SearchBox;
