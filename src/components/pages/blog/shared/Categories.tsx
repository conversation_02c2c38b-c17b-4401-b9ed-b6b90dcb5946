import { useRouter } from 'next/router';
import { ExtendedLink } from '@src/components/shared';
import { useBlog } from '@src/context';
import { twMerge } from '@src/utils';
import CardTitle from './CardTitle';
import CardWrapper from './CardWrapper';

const Categories = () => {
    const { categories } = useBlog();

    const router = useRouter();

    return Array.isArray(categories) && categories.length > 0 ? (
        <CardWrapper>
            <CardTitle title="Kategoriler" slug="categories" />
            <ul className="grid gap-3 [&>li]:border-b [&>li]:pb-1 last:[&>li]:border-b-0 last:[&>li]:pb-0">
                {categories.map((category) => {
                    const isActiveCategory =
                        router.query.slug?.[1] === category.slug &&
                        router.asPath.includes('categories');

                    return (
                        <li key={category._id}>
                            <ExtendedLink
                                href={`/blog/categories/${category.slug}`}
                                className={twMerge(
                                    'flex items-center justify-between transition duration-200',
                                    !isActiveCategory && 'hover:opacity-50'
                                )}
                            >
                                <p
                                    className={twMerge(
                                        isActiveCategory && 'text-primary-400'
                                    )}
                                >
                                    {category.name}
                                </p>
                                <p
                                    className={twMerge(
                                        'flex h-7 w-7 items-center justify-center rounded-full bg-neutral-100 text-sm',
                                        isActiveCategory &&
                                            'bg-primary-200 text-white'
                                    )}
                                >
                                    {category.postCount}
                                </p>
                            </ExtendedLink>
                        </li>
                    );
                })}
            </ul>
        </CardWrapper>
    ) : null;
};

export default Categories;
