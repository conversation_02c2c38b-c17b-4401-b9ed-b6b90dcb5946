import {
    ExtendedImage,
    ExtendedLink,
    SolidButton
} from '@src/components/shared';
import { useTrans } from '@src/hooks';
import { BlogPost } from '@src/interfaces';
import { isValidCategory, isValidCategoryLink, isValidImage } from './utils';

interface FeaturedBlogCardProps {
    blogPost: BlogPost;
}

const FeaturedBlogCard = ({
    blogPost: { title, thumbnails, categories, shortContent, slug }
}: FeaturedBlogCardProps) => {
    const t = useTrans();

    const thumbnail = isValidImage(thumbnails);
    const category = isValidCategory(categories);
    const categoryLink = isValidCategoryLink(categories);

    return (
        <article className="card-shadow grid rounded-4xl">
            <ExtendedLink href={`/blog/${slug}`}>
                <ExtendedImage
                    src={thumbnail}
                    alt={title}
                    fill
                    priority
                    className="object-cover object-center"
                    containerClassName="overflow-hidden rounded-t-4xl aspect-w-8 aspect-h-5"
                />
            </ExtendedLink>

            <div className="grid gap-4 p-4 text-center">
                <ExtendedLink
                    href={`/blog/categories/${categoryLink}`}
                    className="text-sm uppercase tracking-widest text-primary-400"
                >
                    {category}
                </ExtendedLink>
                <ExtendedLink href={`/blog/${slug}`}>
                    <h2 className="font-means">{title}</h2>
                </ExtendedLink>
                <div
                    className="text-sm text-accent-200"
                    dangerouslySetInnerHTML={{ __html: shortContent }}
                />
                <div>
                    <SolidButton href={`/blog/${slug}`}>
                        {t('Devamını Oku')}
                    </SolidButton>
                </div>
            </div>
        </article>
    );
};

export default FeaturedBlogCard;
