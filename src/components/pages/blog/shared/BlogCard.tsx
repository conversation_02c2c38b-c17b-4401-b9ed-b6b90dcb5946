import {
    ExtendedImage,
    ExtendedLink,
    GhostButton
} from '@src/components/shared';
import { useTrans } from '@src/hooks';
import { BlogPost } from '@src/interfaces';
import {
    isValidCategory,
    isValidCategoryLink,
    isValidImage,
    isValidTag,
    isValidTagLink
} from './utils';

interface BlogCardProps {
    blogPost: BlogPost;
    isSmall?: boolean;
}

const BlogCard = ({
    blogPost: { slug, title, thumbnails, categories, tags, shortContent },
    isSmall = false
}: BlogCardProps) => {
    const t = useTrans();

    const thumbnail = isValidImage(thumbnails);
    const category = isValidCategory(categories);
    const categoryLink = isValidCategoryLink(categories);
    const tag = isValidTag(tags);
    const tagLink = isValidTagLink(tags);

    return isSmall ? (
        <article>
            <ExtendedLink
                href={`/blog/${slug}`}
                className="flex gap-4 transition duration-200 hover:opacity-80"
            >
                <ExtendedImage
                    src={`${thumbnail}?w=240`}
                    alt={title}
                    fill
                    className="object-cover object-center"
                    containerClassName="overflow-hidden rounded-xl aspect-w-4 aspect-h-1 w-5/12"
                />

                <div className="grid w-7/12 gap-1 p-2">
                    <p className="text-sm uppercase tracking-widest text-primary-400">
                        {category}
                    </p>
                    <h2 className="line-clamp-3 font-means">{title}</h2>
                </div>
            </ExtendedLink>
        </article>
    ) : (
        <article className="grid gap-4 md:grid-cols-12">
            <ExtendedImage
                src={`${thumbnail}?w=540`}
                alt={title}
                fill
                className="object-cover object-center"
                containerClassName="overflow-hidden rounded-xl aspect-w-2 md:aspect-w-4 aspect-h-1 md:aspect-h-3 md:col-span-4"
            />

            <div className="grid gap-4 p-3 md:col-span-8">
                <ExtendedLink
                    href={`/blog/categories/${categoryLink}`}
                    className="text-sm uppercase tracking-widest text-primary-400"
                >
                    {category}
                </ExtendedLink>
                <ExtendedLink href={`/blog/${slug}`}>
                    <h2 className="font-means text-lg">{title}</h2>
                </ExtendedLink>
                <div
                    className="text-sm text-accent-200"
                    dangerouslySetInnerHTML={{ __html: shortContent }}
                />
                <div className="flex flex-wrap items-center justify-between gap-4">
                    <GhostButton href={`/blog/${slug}`} color="dark">
                        {t('Devamını Oku')}
                    </GhostButton>
                    {/* {tag?.length > 0 && (
                        <ExtendedLink
                            href={`/blog/tags/${tagLink}`}
                            className="rounded-full border px-8 py-4 text-sm font-medium leading-4"
                        >
                            <span className="text-primary-400">#</span>
                            {tag}
                        </ExtendedLink>
                    )} */}
                </div>
            </div>
        </article>
    );
};

export default BlogCard;
