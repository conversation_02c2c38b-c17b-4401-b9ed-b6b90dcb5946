import { useRouter } from 'next/router';
import { ExtendedLink } from '@src/components/shared';
import { useTrans } from '@src/hooks';
import { XMarkIcon } from '@src/icons/solid';

interface CardTitleProps {
    title: string;
    slug: string;
}

const CardTitle = ({ title, slug }: CardTitleProps) => {
    const t = useTrans();

    const router = useRouter();

    return (
        <div className="flex items-center justify-between">
            <h3 className="font-means text-lg">{title}</h3>
            {router.asPath.includes(slug) && (
                <ExtendedLink
                    href="/blog"
                    className="group flex items-center gap-1 rounded-full border px-2.5 py-1 text-xs transition duration-200 hover:border-primary-200"
                >
                    <span className="group-hover:text-primary-400">
                        {t('Geri')}
                    </span>
                    <XMarkIcon className="h-3 w-3 group-hover:text-primary-400" />
                </ExtendedLink>
            )}
        </div>
    );
};

export default CardTitle;
