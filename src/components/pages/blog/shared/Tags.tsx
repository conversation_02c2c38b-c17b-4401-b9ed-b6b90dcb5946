import { useRouter } from 'next/router';
import { ExtendedLink } from '@src/components/shared';
import { useBlog } from '@src/context';
import { twMerge } from '@src/utils';
import CardTitle from './CardTitle';
import CardWrapper from './CardWrapper';

const Tags = () => {
    const { tags } = useBlog();

    const router = useRouter();

    return Array.isArray(tags) && tags.length > 0 ? (
        <CardWrapper>
            <CardTitle title="Etiketler" slug="tags" />
            <ul className="flex flex-wrap gap-2 gap-y-5">
                {tags.map((tag) => {
                    const isActiveTag =
                        router.query.slug?.[1] === tag.slug &&
                        router.asPath.includes('tags');

                    return (
                        <li key={tag._id}>
                            <ExtendedLink
                                href={`/blog/tags/${tag.slug}`}
                                className={twMerge(
                                    'w-fit whitespace-nowrap rounded-full border px-4 py-2 text-sm font-medium transition duration-200',
                                    isActiveTag
                                        ? 'bg-primary-200 text-white'
                                        : 'hover:opacity-50'
                                )}
                            >
                                <span
                                    className={twMerge(
                                        'text-primary-400',
                                        isActiveTag && 'text-white'
                                    )}
                                >
                                    #
                                </span>
                                {tag.name}
                            </ExtendedLink>
                        </li>
                    );
                })}
            </ul>
        </CardWrapper>
    ) : null;
};

export default Tags;
