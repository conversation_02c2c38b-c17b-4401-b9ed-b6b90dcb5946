import { Category, Tag } from '@src/interfaces';

export const isValidCategory = (categories: Category[]) =>
    Array.isArray(categories) && categories.length > 0
        ? categories[0]?.name
        : '';

export const isValidCategoryLink = (categories: Category[]) =>
    Array.isArray(categories) && categories.length > 0
        ? categories[0]?.slug
        : '';

export const isValidImage = (images: string[]) =>
    Array.isArray(images) && images.length > 0
        ? images[0]
        : '/images/shared/no-image.png';

export const isValidTag = (tags: Tag[]) =>
    Array.isArray(tags) && tags.length > 0 ? tags[0]?.name : '';

export const isValidTagLink = (tags: Tag[]) =>
    Array.isArray(tags) && tags.length > 0 ? tags[0]?.slug : '';
