import { RefObject, useEffect, useMemo, useRef, useState } from 'react';
import ConnectionIcon from '@public/images/pages/digital-transform/Connection.svg';
import EnterErpBorder from '@public/images/pages/digital-transform/EnterErpBorder.svg';
import HumanIcon from '@public/images/pages/digital-transform/Human.svg';
import ProductIcon from '@public/images/pages/digital-transform/Product.svg';
import WorldIcon from '@public/images/pages/digital-transform/World.svg';
import { ExtendedImage } from '@src/components/shared';
import { useTrans } from '@src/hooks';
import { twMerge } from '@src/utils';
import AnimatedCard from './AnimatedCard';
import InputAnimation from './InputAnimation';
import OutputAnimation from './OutputAnimation';

type TAnimation = 'first' | 'second' | 'third' | 'fourth' | 'none';

type Card = {
    id: TAnimation;
    title: string;
    descriptin: string;
    image: any;
    ref: RefObject<HTMLDivElement>;
};

const TransformAnimation = () => {
    const [focus, setFocus] = useState<TAnimation>('none');
    const [isTouchDevice, setIsTouchDevice] = useState(false);

    const ref1 = useRef<HTMLDivElement>(null);
    const ref2 = useRef<HTMLDivElement>(null);
    const ref3 = useRef<HTMLDivElement>(null);
    const ref4 = useRef<HTMLDivElement>(null);

    const containerRef = useRef<HTMLDivElement>(null);

    useTouchOutside(containerRef, () => {
        setFocus('none');
    });

    const t = useTrans();

    const cards: Card[] = useMemo(
        () => [
            {
                id: 'first',
                title: 'İş Süreçleri',
                descriptin:
                    'Aktif iş süreçlerinin yönetebilir ve uyarlanabilir olması.',
                image: ConnectionIcon,
                ref: ref1
            },
            {
                id: 'second',
                title: 'Bilgisayar Teknolojileri',
                descriptin:
                    'Disiplinler arası iletişiminizi tümüyle dijitalleştirin.',
                image: WorldIcon,
                ref: ref2
            },
            {
                id: 'third',
                title: 'Dijital Kaynaklar',
                descriptin:
                    'İnsan kaynağınızı dijital dönüşüm için hazırlayın.',
                image: HumanIcon,
                ref: ref3
            },
            {
                id: 'fourth',
                title: 'Tedarik Süreçleri',
                descriptin:
                    'Tedarik zincirinizin tüm süreçlerinizi optimize edin.',
                image: ProductIcon,
                ref: ref4
            }
        ],
        []
    );

    useEffect(() => {
        const cardOne = ref1.current;
        const cardTwo = ref2.current;
        const cardThree = ref3.current;
        const cardFour = ref4.current;

        /* Detect touch devices */
        if ('ontouchstart' in window || navigator.maxTouchPoints > 0) {
            setIsTouchDevice(true);
        }

        if (focus === 'first') {
            cardTwo?.style.setProperty('--card-angle', '65deg');
            cardTwo?.style.setProperty('--card-dx', '45%');
            cardTwo?.style.setProperty('--card-offset', '0.5');
            cardThree?.style.setProperty('--card-angle', '65deg');
            cardThree?.style.setProperty('--card-dx', '45%');
            cardThree?.style.setProperty('--card-offset', '0.75');
            cardFour?.style.setProperty('--card-angle', '65deg');
            cardFour?.style.setProperty('--card-dx', '45%');
            cardFour?.style.setProperty('--card-offset', '1');
        } else if (focus === 'second') {
            cardOne?.style.setProperty('--card-angle', '65deg');
            cardOne?.style.setProperty('--card-dx', '45%');
            cardOne?.style.setProperty('--card-offset', '-1.5');
            cardThree?.style.setProperty('--card-angle', '65deg');
            cardThree?.style.setProperty('--card-dx', '45%');
            cardThree?.style.setProperty('--card-offset', '1.25');
            cardFour?.style.setProperty('--card-angle', '65deg');
            cardFour?.style.setProperty('--card-dx', '45%');
            cardFour?.style.setProperty('--card-offset', '1.5');
        } else if (focus === 'third') {
            cardOne?.style.setProperty('--card-angle', '65deg');
            cardOne?.style.setProperty('--card-dx', '45%');
            cardOne?.style.setProperty('--card-offset', '-1.5');
            cardTwo?.style.setProperty('--card-angle', '65deg');
            cardTwo?.style.setProperty('--card-dx', '45%');
            cardTwo?.style.setProperty('--card-offset', '-1.25');
            cardThree?.style.setProperty('--card-offset', '-0.25');
            cardFour?.style.setProperty('--card-angle', '65deg');
            cardFour?.style.setProperty('--card-dx', '45%');
            cardFour?.style.setProperty('--card-offset', '1.5');
        } else if (focus === 'fourth') {
            cardOne?.style.setProperty('--card-angle', '65deg');
            cardOne?.style.setProperty('--card-dx', '45%');
            cardOne?.style.setProperty('--card-offset', '-1.5');
            cardTwo?.style.setProperty('--card-angle', '65deg');
            cardTwo?.style.setProperty('--card-dx', '45%');
            cardTwo?.style.setProperty('--card-offset', '-1.25');
            cardThree?.style.setProperty('--card-angle', '65deg');
            cardThree?.style.setProperty('--card-dx', '45%');
            cardThree?.style.setProperty('--card-offset', '-1');
            cardFour?.style.setProperty('--card-offset', '0.75');
        } else if (focus === 'none') {
            cardOne?.style.setProperty('--card-angle', '55deg');
            cardOne?.style.setProperty('--card-dx', '40%');
            cardOne?.style.setProperty('--card-offset', '-1.5');
            cardTwo?.style.setProperty('--card-angle', '55deg');
            cardTwo?.style.setProperty('--card-dx', '40%');
            cardTwo?.style.setProperty('--card-offset', '-0.5');
            cardThree?.style.setProperty('--card-angle', '55deg');
            cardThree?.style.setProperty('--card-dx', '40%');
            cardThree?.style.setProperty('--card-offset', '0.5');
            cardFour?.style.setProperty('--card-angle', '55deg');
            cardFour?.style.setProperty('--card-dx', '40%');
            cardFour?.style.setProperty('--card-offset', '1.5');
        }

        return () => {
            cardOne?.style.setProperty('--card-angle', '55deg');
            cardOne?.style.setProperty('--card-dx', '40%');
            cardOne?.style.setProperty('--card-offset', '-1.5');
            cardTwo?.style.setProperty('--card-angle', '55deg');
            cardTwo?.style.setProperty('--card-dx', '40%');
            cardTwo?.style.setProperty('--card-offset', '-0.5');
            cardThree?.style.setProperty('--card-angle', '55deg');
            cardThree?.style.setProperty('--card-dx', '40%');
            cardThree?.style.setProperty('--card-offset', '0.5');
            cardFour?.style.setProperty('--card-angle', '55deg');
            cardFour?.style.setProperty('--card-dx', '40%');
            cardFour?.style.setProperty('--card-offset', '1.5');
        };
    }, [focus]);

    return (
        <div className="spacer container relative grid place-items-center lg:grid-cols-3">
            <ExtendedImage
                src={EnterErpBorder}
                alt={t('EnterERP Çerçeve')}
                containerClassName="lg:absolute w-fit left-1/2 -translate-x-1/2 lg:flex items-center justify-center hidden"
            />
            <ExtendedImage
                src={EnterErpBorder}
                alt={t('EnterERP Çerçeve')}
                containerClassName="absolute w-fit lg:hidden flex items-center justify-center"
                className="h-72"
            />

            <InputAnimation />

            <div
                ref={containerRef}
                className="safari-fix-animation flex h-72 w-full items-center justify-center lg:min-h-full"
            >
                {cards.map((card) => (
                    <div
                        key={card.id}
                        ref={card.ref}
                        className={twMerge(
                            `card max-w-[260px] max-md:w-1/2 card-${card.id}`,
                            focus === card.id && 'card-hover'
                        )}
                        onMouseEnter={() => {
                            if (isTouchDevice) return;
                            setFocus(card.id);
                        }}
                        onMouseLeave={() => {
                            if (isTouchDevice) return;
                            setFocus('none');
                        }}
                        onTouchStart={() => {
                            setFocus(card.id);
                            if (focus === card.id) {
                                document
                                    .querySelector(`#${card.id}`)
                                    ?.scrollIntoView({ behavior: 'smooth' });
                                setFocus('none');
                            }
                        }}
                        onClick={() => {
                            if (isTouchDevice) return;
                            document
                                .querySelector(`#${card.id}`)
                                ?.scrollIntoView({ behavior: 'smooth' });
                        }}
                    >
                        <AnimatedCard
                            title={card.title}
                            description={card.descriptin}
                            src={card.image}
                        />
                    </div>
                ))}
            </div>

            <OutputAnimation />
        </div>
    );
};

function useTouchOutside(ref: RefObject<HTMLElement>, handler: () => void) {
    useEffect(() => {
        const listener = (event: any) => {
            if (!ref.current || ref.current.contains(event.target)) return;

            handler();
        };
        document.addEventListener('touchstart', listener);

        return () => {
            document.removeEventListener('touchstart', listener);
        };
    }, [ref, handler]);
}

export default TransformAnimation;
