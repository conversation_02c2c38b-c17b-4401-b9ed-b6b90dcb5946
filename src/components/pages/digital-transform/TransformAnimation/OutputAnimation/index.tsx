import TransformOutput from '@public/images/pages/digital-transform/TransformOutput.svg';
import TransformOutputMobile from '@public/images/pages/digital-transform/TransformOutputMobile.svg';
import { ExtendedImage } from '@src/components/shared';
import { useTrans } from '@src/hooks';
import MobileOutputLineAnimation from './MobileOutputLineAnimation';
import OutputLineAnimation from './OutputLineAnimation';

const OutputAnimation = () => {
    const t = useTrans();

    return (
        <>
            <div className="flex flex-col lg:hidden">
                <MobileOutputLineAnimation />
                <ExtendedImage src={TransformOutputMobile} alt={t('İkonlar')} />
            </div>

            <div className="hidden lg:flex">
                <OutputLineAnimation />
                <ExtendedImage src={TransformOutput} alt={t('İkonlar')} />
            </div>
        </>
    );
};

export default OutputAnimation;
