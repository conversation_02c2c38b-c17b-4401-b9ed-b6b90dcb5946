const MobileOutputLineAnimation = () => {
    return (
        <svg
            xmlns="http://www.w3.org/2000/svg"
            viewBox="0 0 320 80"
            fill="none"
        >
            <g
                className="line-2-mobile"
                stroke="#006CFA"
                strokeDasharray="0.24 5.6"
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                mask="url(#a)"
            >
                <path stroke="transparent" d="M10 81V1" />
                <path d="M136 1v26.825a16 16 0 0 1-13.059 15.727L61.059 55.123A16 16 0 0 0 48 70.85V81M160 1v80m24-78.889v26.004a16 16 0 0 0 13.058 15.727l61.883 11.572A16 16 0 0 1 272 71.141V81" />
            </g>
            <defs>
                <linearGradient id="b" y2={1} x2={0}>
                    <stop offset={0} stopColor="#fff" />
                    <stop offset={0.8} stopColor="#fff" />
                    <stop offset={1} stopColor="#fff" stopOpacity={0} />
                </linearGradient>
                <mask id="a" maskContentUnits="objectBoundingBox">
                    <path fill="url(#b)" d="M0 0h2v1H0z" />
                </mask>
            </defs>
        </svg>
    );
};

export default MobileOutputLineAnimation;
