const InputLineAnimation = () => {
    return (
        <svg
            viewBox="0 0 300 360"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
            className="w-full"
        >
            <path
                d="M5.617 22H83.15c18.187 0 34.874 8.944 43.339 23.227l42.635 71.946c8.464 14.283 25.152 23.227 43.338 23.227l125.202.008"
                stroke="#006CFA"
                strokeWidth={3}
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeDasharray="1 8"
                opacity={0.2}
                className="line-1"
            />
            <path
                d="M5.617 76h76.735c15.49 0 30.066 6.303 39.304 16.996l37.03 42.86c9.238 10.693 23.813 16.996 39.303 16.996l136.766.015"
                stroke="#006CFA"
                strokeWidth={3}
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeDasharray="1 8"
                opacity={0.5}
                className="line-1"
            />
            <path
                d="M5.617 128H84.21c10.577 0 20.869 2.817 29.338 8.031l42.189 22.207c8.469 5.214 18.761 8.032 29.339 8.032l148.761-.045"
                stroke="#006CFA"
                strokeWidth={3}
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeDasharray="1 8"
                opacity={0.8}
                className="line-1"
            />
            <path
                d="M5 179.711h332.811"
                stroke="#006CFA"
                strokeWidth={3}
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeDasharray="1 8"
                className="line-1"
            />
            <path
                d="M5.617 337H83.15c18.187 0 34.874-8.537 43.339-22.169l42.635-68.668c8.464-13.632 25.152-22.169 43.338-22.169l125.202-.008"
                stroke="#006CFA"
                strokeWidth={3}
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeDasharray="1 8"
                opacity={0.2}
                className="line-1"
            />
            <path
                d="M5.617 284h76.735c15.49 0 30.066-6.147 39.304-16.574l37.03-41.796c9.238-10.428 23.813-16.574 39.303-16.574l136.766-.015"
                stroke="#006CFA"
                strokeWidth={3}
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeDasharray="1 8"
                opacity={0.5}
                className="line-1"
            />
            <path
                d="M2 232.5h79c10.576 0 40.531 4.035 49-4.5l25.737-20.458c8.469-8.535 18.761-13.147 29.339-13.147l148.761.072"
                stroke="#006CFA"
                strokeWidth={3}
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeDasharray="1 8"
                opacity={0.8}
                className="line-1"
            />
        </svg>
    );
};

export default InputLineAnimation;
