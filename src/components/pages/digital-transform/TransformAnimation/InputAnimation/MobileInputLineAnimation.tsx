const MobileInputLineAnimation = () => {
    return (
        <svg
            xmlns="http://www.w3.org/2000/svg"
            viewBox="0 0 320 83"
            fill="none"
        >
            <g
                className="line-1-mobile"
                stroke="#006CFA"
                strokeDasharray="0.24 5.6"
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                mask="url(#a)"
            >
                <path stroke="transparent" d="M10 56V0" />
                <path d="M124 81V62.074a16 16 0 0 0-12.436-15.598L28.436 27.482A16 16 0 0 1 16 11.884V1" />
                <path d="M136 81.08V55.708a20 20 0 0 0-13.856-19.033L74.85 21.437A16 16 0 0 1 63.765 6.211V1" />
                <path d="M148 81V49.028a24 24 0 0 0-13.769-21.71l-13.051-6.15A16 16 0 0 1 112 6.694V1m48 79V0m12 81V49.028a24 24 0 0 1 13.769-21.71l13.051-6.15A16 16 0 0 0 208 6.694V1" />
                <path d="M184.088 81.08V55.708a20 20 0 0 1 13.856-19.033l47.206-15.238a16 16 0 0 0 11.085-15.226V1" />
                <path d="M196 81V62.074a16 16 0 0 1 12.436-15.598l83.128-18.994A16 16 0 0 0 304 11.884V1" />
            </g>
            <defs>
                <linearGradient id="b" y2={1} x2={0}>
                    <stop offset={0} stopColor="#fff" />
                    <stop offset={0.8} stopColor="#fff" />
                    <stop offset={1} stopColor="#fff" stopOpacity={0} />
                </linearGradient>
                <mask id="a" maskContentUnits="objectBoundingBox">
                    <path fill="url(#b)" d="M0 0h2v1H0z" />
                </mask>
            </defs>
        </svg>
    );
};

export default MobileInputLineAnimation;
