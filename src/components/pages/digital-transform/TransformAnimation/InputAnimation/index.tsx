import TransformInput from '@public/images/pages/digital-transform/TransformInput.svg';
import TransformInputMobile from '@public/images/pages/digital-transform/TransformInputMobile.svg';
import { ExtendedImage } from '@src/components/shared';
import { useTrans } from '@src/hooks';
import InputLineAnimation from './InputLineAnimation';
import MobileInputLineAnimation from './MobileInputLineAnimation';

const InputAnimation = () => {
    const t = useTrans();

    return (
        <>
            <div className="hidden lg:flex">
                <ExtendedImage
                    src={TransformInput}
                    alt={t('İkonlar')}
                    containerClassName="w-full basis-[320px]"
                />
                <InputLineAnimation />
            </div>

            <div className="flex flex-col lg:hidden">
                <ExtendedImage src={TransformInputMobile} alt={t('İkonlar')} />
                <MobileInputLineAnimation />
            </div>
        </>
    );
};

export default InputAnimation;
