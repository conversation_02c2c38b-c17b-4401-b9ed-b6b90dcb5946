import { StaticImageData } from 'next/image';
import { useRouter } from 'next/router';
import { ExtendedImage } from '@src/components/shared';
import { useTrans } from '@src/hooks';
import { ChevronRightIcon } from '@src/icons/solid';
import { twMerge } from '@src/utils';

interface AnimatedCardProps {
    title: string;
    description: string;
    src: string | StaticImageData;
}

const AnimatedCard = ({ title, description, src }: AnimatedCardProps) => {
    const t = useTrans();

    const router = useRouter();

    const isHomePage = router.asPath === '/';

    return (
        <article
            className={twMerge(
                'card-child group grid h-60 gap-4 rounded-4xl p-6 transition-shadow duration-300 ease-in-out hover:border-2 hover:border-[#021D63] hover:bg-none hover:shadow-none lg:h-72',
                isHomePage ? 'cursor-default' : 'cursor-pointer'
            )}
        >
            <div>
                <p className="w-12 origin-left scale-110 font-means text-xl text-white transition-transform duration-300 ease-in-out group-hover:scale-100">
                    {t(title)}
                </p>
                <p className="mt-1 text-[10px] text-white opacity-0 transition duration-300 ease-in-out group-hover:opacity-100 md:mt-2 md:text-sm">
                    {t(description)}
                </p>
            </div>
            <div className="relative">
                <ExtendedImage
                    src={src}
                    alt={t(title)}
                    containerClassName={twMerge(
                        'self-end w-14 h-14 absolute bottom-0',
                        !isHomePage &&
                            'group-hover:opacity-0 ease-in-out transition duration-300'
                    )}
                />
                {!isHomePage && (
                    <div className="absolute bottom-0 flex cursor-pointer items-center text-[8px] font-medium text-white opacity-0 transition duration-300 ease-in-out group-hover:opacity-100 md:text-base">
                        {t('Devamını Oku')}
                        <span className="-mr-3 ml-3 h-[2px] w-6 origin-left scale-x-0 overflow-hidden bg-white opacity-0 transition-all delay-150 duration-300 ease-in-out group-hover:visible group-hover:scale-x-100 group-hover:opacity-100 md:w-8"></span>
                        <ChevronRightIcon className="h-4 w-4 -translate-x-4 text-white transition-transform delay-150 duration-300 ease-in-out group-hover:translate-x-0" />
                    </div>
                )}
            </div>
        </article>
    );
};

export default AnimatedCard;
