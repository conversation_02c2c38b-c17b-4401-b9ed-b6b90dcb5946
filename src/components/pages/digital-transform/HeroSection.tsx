import { useTrans } from '@src/hooks';
import TransformAnimation from './TransformAnimation';

const HeroSection = () => {
    const t = useTrans();

    return (
        <div className="spacer relative bg-primary-900">
            <svg
                viewBox="0 0 2160 167"
                xmlns="http://www.w3.org/2000/svg"
                className="absolute -bottom-[2px] left-0 right-0 hidden fill-white lg:block"
            >
                <path d="M2162.5 166.037H-2C-2 166.037 -2 86.0371 -2 18.0371C420.5 -49.9629 606.374 95.947 1103 99.5358C1686.18 103.751 1710 -60.4629 2162.5 49.5371C2162.5 61.5373 2162.5 166.037 2162.5 166.037Z" />
            </svg>

            <div className="container grid gap-8 text-center">
                <p className="text-primary-200">{t('YOL HARİTASI')}</p>
                <h1 className="font-means text-3xl text-white lg:text-5xl">
                    {t('İşletmenizin Dijital Dönüşüm Yolculuğunu Başlatın')}
                </h1>
                <p className="mx-auto text-lg text-accent-100 lg:w-9/12">
                    {t(
                        'Dijital dönüşümün ana bileşenleri, gereklilikleri ve faydaları konusunda işletmelerin bilgi düzeylerinin ve farkındalıklarının artırılarak dijitalleşme ihtiyaçlarının analiz edilmesi gerekmektedir. İşletmelerin dijital dönüşüm seviyelerinin dört ana başlık altında değerlendirilmesi, ölçülmesi, analiz edilmesi ve yol haritalarının oluşturulması adımları takip ederek inceleyebilirsiniz.'
                    )}
                </p>
            </div>

            <TransformAnimation />
        </div>
    );
};

export default HeroSection;
