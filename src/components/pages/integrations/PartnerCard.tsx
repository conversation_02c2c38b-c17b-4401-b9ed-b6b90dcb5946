import { useTrans } from '@src/hooks';

interface Partner {
    id: number;
    name: string;
    description: string;
    logo: string;
    category: string;
    tags: string[];
}

interface PartnerCardProps {
    partner: Partner;
}

const PartnerCard = ({ partner }: PartnerCardProps) => {
    const t = useTrans();

    return (
        <article className="card-shadow card-shadow group relative flex cursor-pointer flex-col overflow-hidden rounded-4xl border bg-white   p-6 py-8 transition-all duration-200  hover:-translate-y-1 hover:shadow-lg md:flex-row lg:gap-6">
            <div className="grid gap-4">
                {/* Logo */}
                <div className="mx-auto flex h-16 w-16 items-center justify-center rounded-xl bg-gray-50 p-2">
                    <div className="flex h-10 w-10 items-center justify-center rounded-lg bg-primary-100">
                        <span className="text-lg font-bold text-white">
                            {partner.name.charAt(0)}
                        </span>
                    </div>
                </div>

                {/* Partner Name */}
                <h3 className="text-center font-means text-xl transition-colors duration-200 group-hover:text-primary-500">
                    {partner.name}
                </h3>

                {/* Category Badge */}
                <div className="flex justify-center">
                    <span className="rounded-4xl bg-primary-50 px-3 py-1 text-xs font-medium text-primary-600">
                        {t(partner.category)}
                    </span>
                </div>

                {/* Description */}
                <p className="line-clamp-3 text-center text-sm text-accent-200">
                    {t(partner.description)}
                </p>

                {/* Tags */}
                <div className="flex flex-wrap justify-center gap-2">
                    {partner.tags.slice(0, 3).map((tag, index) => (
                        <span
                            key={index}
                            className="rounded-4xl bg-gray-100 px-2 py-1 text-xs text-gray-600"
                        >
                            {tag}
                        </span>
                    ))}
                    {partner.tags.length > 3 && (
                        <span className="rounded-4xl bg-gray-100 px-2 py-1 text-xs text-gray-600">
                            +{partner.tags.length - 3}
                        </span>
                    )}
                </div>
            </div>
        </article>
    );
};

export default PartnerCard;
