import { useState } from 'react';
import FilterSidebar from './FilterSidebar';
import IntegrationGrid from './IntegrationGrid';

const Integrations = () => {
    const [searchTerm, setSearchTerm] = useState('');
    const [selectedFilters, setSelectedFilters] = useState({
        collections: [],
        useCases: [],
        industries: []
    });

    return (
        <div className="min-h-screen bg-gray-50">
            <div className="container mx-auto px-4 py-8">
                <div className="flex gap-8">
                    {/* Left Sidebar */}
                    <div className="w-80 flex-shrink-0">
                        <FilterSidebar
                            selectedFilters={selectedFilters}
                            onFiltersChange={setSelectedFilters}
                        />
                    </div>

                    {/* Main Content */}
                    <div className="flex-1">
                        <IntegrationGrid
                            searchTerm={searchTerm}
                            onSearchChange={setSearchTerm}
                            selectedFilters={selectedFilters}
                        />
                    </div>
                </div>
            </div>
        </div>
    );
};

export default Integrations;
