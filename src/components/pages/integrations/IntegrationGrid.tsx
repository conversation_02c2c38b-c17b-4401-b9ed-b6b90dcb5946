import { useTrans } from '@src/hooks';
import IntegrationCard from './IntegrationCard';

interface IntegrationGridProps {
    searchTerm: string;
    onSearchChange: (term: string) => void;
    selectedFilters: {
        collections: string[];
        useCases: string[];
        industries: string[];
    };
}

const integrations = [
    {
        id: 1,
        name: 'Anthropic',
        description:
            'Develop safe, reliable, enterprise-grade AI solutions with Claude and MongoDB',
        logo: 'AI',
        category: 'AI & Machine Learning',
        tags: ['AI/ML', 'ENTERPRISE', 'RELIABLE', 'SAFE'],
        bgColor: 'bg-black'
    },
    {
        id: 2,
        name: 'Cohere',
        description:
            'Build state-of-the-art RAG and search on your MongoDB data with LLMs from Cohere',
        logo: 'C',
        category: 'AI & Machine Learning',
        tags: ['INTEGRATION', 'RAG', 'SEARCH', 'ENTERPRISE'],
        bgColor: 'bg-green-600'
    },
    {
        id: 3,
        name: '<PERSON><PERSON><PERSON><PERSON>',
        description:
            'Simplify development and deployment of genAI applications with MongoDB and LangChain',
        logo: 'L',
        category: 'AI & Machine Learning',
        tags: ['INTEGRATION', 'GENAI', 'DEVELOPMENT'],
        bgColor: 'bg-blue-600'
    },
    {
        id: 4,
        name: 'Accenture for Generative AI',
        description:
            'MongoDB makes data accessible for Gen AI, from the Mainframe to the Cloud',
        logo: '>',
        category: 'Consulting',
        tags: ['AI/ML', 'ENTERPRISE', 'CLOUD', 'MAINFRAME'],
        bgColor: 'bg-purple-600'
    },
    {
        id: 5,
        name: 'Confluent Cloud',
        description:
            'Combine Confluent and MongoDB to power real-time, responsive applications',
        logo: '⚡',
        category: 'Data Streaming',
        tags: ['STREAMING', 'INTEGRATION', 'REAL-TIME', 'ANALYTICS'],
        bgColor: 'bg-blue-500'
    },
    {
        id: 6,
        name: 'LlamaIndex',
        description:
            'Accelerate genAI app development by deploying LlamaIndex with MongoDB',
        logo: '🦙',
        category: 'AI & Machine Learning',
        tags: ['INTEGRATION', 'GENAI', 'DEVELOPMENT'],
        bgColor: 'bg-gray-800'
    }
];

const IntegrationGrid = ({
    searchTerm,
    onSearchChange,
    selectedFilters
}: IntegrationGridProps) => {
    const t = useTrans();

    const filteredIntegrations = integrations.filter((integration) => {
        const matchesSearch =
            integration.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
            integration.description
                .toLowerCase()
                .includes(searchTerm.toLowerCase());

        // For now, show all integrations regardless of filters
        // You can implement filter logic here based on selectedFilters
        return matchesSearch;
    });

    return (
        <div>
            {/* Search Bar */}
            <div className="mb-8">
                <div className="relative">
                    <div className="pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3">
                        <svg
                            className="h-5 w-5 text-gray-400"
                            fill="none"
                            stroke="currentColor"
                            viewBox="0 0 24 24"
                        >
                            <path
                                strokeLinecap="round"
                                strokeLinejoin="round"
                                strokeWidth={2}
                                d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
                            />
                        </svg>
                    </div>
                    <input
                        type="text"
                        value={searchTerm}
                        onChange={(e) => onSearchChange(e.target.value)}
                        className="block w-full rounded-4xl border border-gray-300 bg-white py-3 pl-10 pr-3 leading-5 placeholder-gray-500 focus:border-blue-500 focus:placeholder-gray-400 focus:outline-none focus:ring-1 focus:ring-blue-500"
                        placeholder="Search Partners and Integrations"
                    />
                </div>
            </div>

            {/* Integration Cards Grid */}
            <div className="grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-3">
                {filteredIntegrations.map((integration) => (
                    <IntegrationCard
                        key={integration.id}
                        integration={integration}
                    />
                ))}
            </div>
        </div>
    );
};

export default IntegrationGrid;
