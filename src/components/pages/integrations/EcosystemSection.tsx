import { useState } from 'react';
import { useTrans } from '@src/hooks';
import SearchBar from './SearchBar';
import CategoryFilter from './CategoryFilter';
import PartnerCard from './PartnerCard';

// Mock partner data - in a real app this would come from an API
const partners = [
    {
        id: 1,
        name: 'Amazon Web Services',
        description:
            'Cloud computing platform offering scalable infrastructure and services for modern applications.',
        logo: '',
        category: 'Cloud Infrastructure',
        tags: ['Cloud', 'Infrastructure', 'Compute']
    },
    {
        id: 2,
        name: 'Microsoft Azure',
        description:
            'Comprehensive cloud platform for building, deploying, and managing applications at scale.',
        logo: '',
        category: 'Cloud Infrastructure',
        tags: ['Cloud', 'Infrastructure', 'AI']
    },
    {
        id: 3,
        name: 'Google Cloud Platform',
        description:
            "Google's cloud computing platform with advanced AI and machine learning capabilities.",
        logo: '',
        category: 'Cloud Infrastructure',
        tags: ['Cloud', 'AI', 'Analytics']
    },
    {
        id: 4,
        name: 'Tableau',
        description:
            'Leading analytics platform that helps people see and understand data through visualization.',
        logo: '',
        category: 'Analytics & BI',
        tags: ['Analytics', 'Visualization', 'BI']
    },
    {
        id: 5,
        name: 'Power BI',
        description:
            'Business analytics solution that delivers insights throughout your organization.',
        logo: '',
        category: 'Analytics & BI',
        tags: ['Analytics', 'Microsoft', 'BI']
    },
    {
        id: 6,
        name: 'Databricks',
        description:
            'Unified analytics platform for big data and machine learning workloads.',
        logo: '',
        category: 'AI & Machine Learning',
        tags: ['AI', 'ML', 'Analytics']
    },
    {
        id: 7,
        name: 'Snowflake',
        description:
            'Cloud data platform that enables data warehousing, data lakes, and secure data sharing.',
        logo: '',
        category: 'Data Management',
        tags: ['Data', 'Cloud', 'Warehouse']
    },
    {
        id: 8,
        name: 'Okta',
        description:
            'Identity and access management platform for secure authentication and authorization.',
        logo: '',
        category: 'Security',
        tags: ['Security', 'Identity', 'Authentication']
    },
    {
        id: 9,
        name: 'Splunk',
        description:
            'Platform for searching, monitoring, and analyzing machine-generated data in real-time.',
        logo: '',
        category: 'Monitoring & Observability',
        tags: ['Monitoring', 'Analytics', 'Security']
    },
    {
        id: 10,
        name: 'Elasticsearch',
        description:
            'Search and analytics engine for all types of data including textual, numerical, and geospatial.',
        logo: '',
        category: 'Search & Analytics',
        tags: ['Search', 'Analytics', 'Observability']
    },
    {
        id: 11,
        name: 'Apache Kafka',
        description:
            'Distributed streaming platform for building real-time data pipelines and streaming applications.',
        logo: '',
        category: 'Data Streaming',
        tags: ['Streaming', 'Data', 'Real-time']
    },
    {
        id: 12,
        name: 'Redis',
        description:
            'In-memory data structure store used as database, cache, and message broker.',
        logo: '',
        category: 'Database',
        tags: ['Database', 'Cache', 'Memory']
    },
    {
        id: 13,
        name: 'Docker',
        description:
            'Platform for developing, shipping, and running applications using containerization.',
        logo: '',
        category: 'DevOps & Deployment',
        tags: ['Containers', 'DevOps', 'Deployment']
    },
    {
        id: 14,
        name: 'Kubernetes',
        description:
            'Open-source container orchestration platform for automating deployment and scaling.',
        logo: '',
        category: 'DevOps & Deployment',
        tags: ['Orchestration', 'Containers', 'DevOps']
    },
    {
        id: 15,
        name: 'Stripe',
        description:
            'Payment processing platform for online businesses and e-commerce applications.',
        logo: '',
        category: 'Payment Processing',
        tags: ['Payments', 'E-commerce', 'API']
    },
    {
        id: 16,
        name: 'Twilio',
        description:
            'Cloud communications platform for building SMS, voice, and messaging applications.',
        logo: '',
        category: 'Communications',
        tags: ['SMS', 'Voice', 'Messaging']
    }
];

const categories = [
    'All',
    'Cloud Infrastructure',
    'Analytics & BI',
    'AI & Machine Learning',
    'Data Management',
    'Security',
    'Monitoring & Observability',
    'Search & Analytics',
    'Data Streaming',
    'Database',
    'DevOps & Deployment',
    'Payment Processing',
    'Communications'
];

const EcosystemSection = () => {
    const t = useTrans();
    const [searchTerm, setSearchTerm] = useState('');
    const [selectedCategory, setSelectedCategory] = useState('All');

    const filteredPartners = partners.filter((partner) => {
        const matchesSearch =
            partner.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
            partner.description
                .toLowerCase()
                .includes(searchTerm.toLowerCase()) ||
            partner.tags.some((tag) =>
                tag.toLowerCase().includes(searchTerm.toLowerCase())
            );

        const matchesCategory =
            selectedCategory === 'All' || partner.category === selectedCategory;

        return matchesSearch && matchesCategory;
    });

    return (
        <section className="spacer container">
            <div className="grid gap-8">
                {/* Header */}
                <div className="text-center">
                    <h2 className="mb-4 font-means text-3xl font-light lg:text-5xl lg:leading-tight">
                        {t('Discover Our Partner Ecosystem')}
                    </h2>
                    <p className="mx-auto max-w-3xl text-lg text-accent-200">
                        {t(
                            'Find the perfect integration for your needs from our comprehensive catalog of technology partners and solutions.'
                        )}
                    </p>
                </div>

                {/* Search and Filter */}
                <div className="grid  gap-6">
                    <div className="w-full">
                        <SearchBar
                            searchTerm={searchTerm}
                            onSearchChange={setSearchTerm}
                        />
                    </div>
                    <CategoryFilter
                        categories={categories}
                        selectedCategory={selectedCategory}
                        onCategoryChange={setSelectedCategory}
                    />
                </div>

                {/* Partner Grid */}
                <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4">
                    {filteredPartners.map((partner) => (
                        <PartnerCard key={partner.id} partner={partner} />
                    ))}
                </div>

                {/* No Results */}
                {filteredPartners.length === 0 && (
                    <div className="py-12 text-center">
                        <p className="mb-4 text-xl text-accent-200">
                            {t('No partners found matching your criteria')}
                        </p>
                        <p className="text-accent-200">
                            {t(
                                'Try adjusting your search terms or category filter'
                            )}
                        </p>
                    </div>
                )}
            </div>
        </section>
    );
};

export default EcosystemSection;
