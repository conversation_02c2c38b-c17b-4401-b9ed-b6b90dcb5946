import InfoIcon from '@public/images/shared/Info.svg';
import { InfoCard } from '@src/components/shared';

const SpecSection = () => {
    return (
        <section className="spacer container">
            <div className="grid place-items-start gap-12 md:grid-cols-2 lg:gap-36 xl:grid-cols-4">
                <InfoCard
                    title="Banka Sanal POS"
                    description="Tüm bankaların Sanal POS sistemlerine anında entegre olun ve kullanmaya başlayın."
                    src={InfoIcon}
                />
                <InfoCard
                    title="Ödeme Sistemleri"
                    description="Yurt içi Özel POS sağlayıcılardan almış olduğunuz Sanal POS sistemini kolayca entegre edin."
                    src={InfoIcon}
                />
                <InfoCard
                    title="Yurtdışı Ödeme Sistemleri"
                    description="Yurt dışı Stripe, Paypal gibi Özel POS sağlayıcılardan almış olduğunuz Sanal POS sistemini kolayca entegre edin."
                    src={InfoIcon}
                />
                <InfoCard
                    title="Masterpass Entegrasyonu"
                    description="Kredi kartı kayıt sistemi olan Masterpass özelliğini aktif ederek ödemelerinizi hızla alın."
                    src={InfoIcon}
                />
            </div>
        </section>
    );
};

export default SpecSection;
