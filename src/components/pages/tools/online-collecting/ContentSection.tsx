import { useState } from 'react';
import FirstImage from '@public/images/pages/tools/online-collecting/First.png';
import SecondImage from '@public/images/pages/tools/online-collecting/Second.png';
import {
    CommonPagesInfo,
    CommonPagesSection,
    DemoSection,
    SectionTabs
} from '@src/components/shared';
import { TabEnum } from '@src/interfaces';

const ContentSection = () => {
    const [activeTab, setActiveTab] = useState<TabEnum>('first');

    const intersectionHandler = (activeTab: TabEnum) => {
        setActiveTab(activeTab);
    };

    return (
        <>
            <SectionTabs
                intersectionHandler={intersectionHandler}
                activeTab={activeTab}
                sections={[
                    { id: 'first', title: 'Online Tahsilat' },
                    { id: 'second', title: 'Taksit Planlama' }
                ]}
            />

            <CommonPagesInfo
                title="DAHA HIZLI, DAHA GÜVENLİ, DAHA PRATİK"
                description="Sanal POS sistemlerini ve geleceğin ödeme yöntemlerini entegre ederek sipariş bazında çoklu ödeme almaya başlayın."
            />

            <CommonPagesSection
                order="1"
                title="Kredi kartı tahsilatlarınızı Online POS Tahsilat ile alın."
                description="Müşteri tahsilatlarını banka ve özel POS sistemleri ile entegre çalışan EnterERP’nin online POS tahsilatı ile alın ve takip edin."
                src={FirstImage}
                id="first"
                info="ONLİNE TAHSİLAT YÖNETİMİ"
                intersectionHandler={intersectionHandler}
            />

            <DemoSection />

            <CommonPagesSection
                order="0"
                title="Tüm kartların taksit oranları tek ekranda."
                description="Müşteri tahsilatlarını banka ve özel POS sistemleri ile entegre çalışan EnterERP’nin online POS tahsilatı ile alın ve takip edin."
                src={SecondImage}
                id="second"
                info="TAKSİT PLANLAMA VE İADE"
                intersectionHandler={intersectionHandler}
            />
        </>
    );
};

export default ContentSection;
