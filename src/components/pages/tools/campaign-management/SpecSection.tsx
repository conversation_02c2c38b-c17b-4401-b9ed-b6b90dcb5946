import InfoIcon from '@public/images/shared/Info.svg';
import { InfoCard } from '@src/components/shared';

const SpecSection = () => {
    return (
        <section className="spacer container">
            <div className="grid place-items-start gap-12 md:grid-cols-2 lg:gap-36 xl:grid-cols-4">
                <InfoCard
                    title="Sipariş Kampanyası"
                    description="Sipariş bazında genel toplam üzerinden kampanyalar düzenleyerek satışlarınızı artırın."
                    src={InfoIcon}
                />
                <InfoCard
                    title="Ürün Kampanyası"
                    description="Ürün ve ürün grubu bazında kampanyalar hazırlayarak fazla stoklarınızı eritin."
                    src={InfoIcon}
                />
                <InfoCard
                    title="X al Y öde"
                    description="Ürün miktar bazlı satış hedeflerinize ulaşmak için çoklu al az öde kampanyaları kurun."
                    src={InfoIcon}
                />
                <InfoCard
                    title="Kargo Kampanyası"
                    description="Ekstra maliyet ödemek istemeyen müşterileriniz için kargo bedava kampanyaları hazırlayın."
                    src={InfoIcon}
                />
            </div>
        </section>
    );
};

export default SpecSection;
