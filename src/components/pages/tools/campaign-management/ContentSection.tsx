import { useState } from 'react';
import FirstImage from '@public/images/pages/tools/campaign-management/First.png';
import SecondImage from '@public/images/pages/tools/campaign-management/Second.png';
import ThirdImage from '@public/images/pages/tools/campaign-management/Third.png';
import {
    CommonPagesInfo,
    CommonPagesSection,
    DemoSection,
    SectionTabs
} from '@src/components/shared';
import { TabEnum } from '@src/interfaces';

const ContentSection = () => {
    const [activeTab, setActiveTab] = useState<TabEnum>('first');

    const intersectionHandler = (activeTab: TabEnum) => {
        setActiveTab(activeTab);
    };

    return (
        <>
            <SectionTabs
                intersectionHandler={intersectionHandler}
                activeTab={activeTab}
                sections={[
                    { id: 'first', title: 'Kampanya' },
                    { id: 'second', title: 'Kupon' },
                    { id: 'third', title: 'Uygulama' }
                ]}
            />

            <CommonPagesInfo
                title="DAHA BASİT, DAHA KOLAY, DAHA HIZLI"
                description="<PERSON>ha hızlı satış yapmak ve başarılı olmak için ihtiyacınız olan pazarlama araçlarını şimdi edinin."
            />

            <CommonPagesSection
                order="0"
                title="Satış kampanyalarınızı kolayca oluşturun."
                description="Satışlarınızı artırmak, yeni müşteriler çekmek için gelişmiş kampanya yönetimi ile istediğiniz özelliklerdeki kampanyaları kolayca oluşturun. Kampanyaları e-ticaret ve mağaza kapsamı bazında oluşturarak farklı satış kanallarınız için ayrı ayrı yönetin."
                src={FirstImage}
                id="first"
                info="KAMPANYA YÖNETİMİ"
                intersectionHandler={intersectionHandler}
            />

            <CommonPagesSection
                order="1"
                title="Özel kampanyalar için kuponlar dağıtın."
                description="Özel gün ve müşteri grupları için kampanya hazırlayın bu kampanya için üretmiş olduğunuz kupon kodlarını sınırlı kullanım miktarı ile paylaşın. Kupon kodu kullanım miktarı ve satış tutarını anlık olarak raporlayın."
                src={SecondImage}
                id="second"
                info="KUPON YÖNETİMİ"
                intersectionHandler={intersectionHandler}
            />

            <DemoSection />

            <CommonPagesSection
                order="0"
                title="Hazırlanan kampanyaları otomatik olarak uygulayın."
                description="Oluşturmuş olduğunuz kampanyaları teklif, sipariş, fatura ve e-ticaret siparişleri bazında otomatik veya manuel olarak uygulayın."
                src={ThirdImage}
                id="third"
                info="KAMPANYA UYGULAMA YÖNETİMİ"
                intersectionHandler={intersectionHandler}
            />
        </>
    );
};

export default ContentSection;
