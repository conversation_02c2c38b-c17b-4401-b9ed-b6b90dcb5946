import { useState } from 'react';
import FirstImage from '@public/images/pages/tools/price-wizard/First.png';
import FourthImage from '@public/images/pages/tools/price-wizard/Fourth.png';
import SecondImage from '@public/images/pages/tools/price-wizard/Second.png';
import ThirdImage from '@public/images/pages/tools/price-wizard/Third.png';
import {
    CommonPagesInfo,
    CommonPagesSection,
    SectionTabs
} from '@src/components/shared';
import { TabEnum } from '@src/interfaces';

const ContentSection = () => {
    const [activeTab, setActiveTab] = useState<TabEnum>('first');

    const intersectionHandler = (activeTab: TabEnum) => {
        setActiveTab(activeTab);
    };

    return (
        <>
            <SectionTabs
                intersectionHandler={intersectionHandler}
                activeTab={activeTab}
                sections={[
                    { id: 'first', title: 'Yöntem' },
                    { id: 'second', title: 'Kriter' },
                    { id: 'third', title: 'Si<PERSON><PERSON>lasyon' },
                    { id: 'fourth', title: '<PERSON>zet' }
                ]}
            />

            <CommonPagesInfo
                title="DAHA HIZLI, DAHA GÜVENLİ, DAHA KAPSAMLI"
                description="Bu sihirbaz, fiyatları güncellemek için gerekli parametrelerin tanımında size adım adım kılavuzluk eder."
            />

            <div className="relative">
                <CommonPagesSection
                    order="0"
                    title="Fiyatlandırma Yöntemleri"
                    description="Satış fiyat listelerini güncellemek için referans alacağınız kaynakları belirleyin."
                    src={FirstImage}
                    id="first"
                    info={1}
                    intersectionHandler={intersectionHandler}
                />

                <svg
                    width="629"
                    height="392"
                    viewBox="0 0 629 392"
                    fill="none"
                    xmlns="http://www.w3.org/2000/svg"
                    className="absolute -bottom-1/4 right-[30%] -z-10 hidden lg:block"
                >
                    <path
                        d="M0.5 390.5H365.5C365.5 390.5 493.343 395.999 577.5 279.999C633 203.499 627.5 117.999 627.5 117.999V0"
                        stroke="#001F2C"
                        strokeDasharray="2 2"
                    />
                    <path
                        d="M540.921 300L558.75 327.796L544.703 357.852L512.829 360.114L495.001 332.318L509.047 302.261L540.921 300Z"
                        fill="#F5F7FA"
                        stroke="#001E2B"
                        strokeWidth="0.75"
                        strokeMiterlimit="10"
                    />
                    <path
                        d="M512.828 360.114L495 332.318L526.875 330.057L544.703 357.852L512.828 360.114Z"
                        fill="#0048FF"
                        stroke="#001E2B"
                        strokeWidth="0.75"
                        strokeMiterlimit="10"
                    />
                    <path
                        d="M540.921 300L526.875 330.057"
                        stroke="#001E2B"
                        strokeWidth="0.75"
                        strokeMiterlimit="10"
                    />
                </svg>
            </div>

            <div className="relative">
                <CommonPagesSection
                    order="1"
                    title="Fiyatlandırma Kriterleri"
                    description="Tüm listede değişiklik yapmadan sadece istediğiniz kriterlerde değişiklik yapın."
                    src={SecondImage}
                    id="second"
                    info={2}
                    intersectionHandler={intersectionHandler}
                />

                <svg
                    width="639"
                    height="392"
                    viewBox="0 0 639 392"
                    fill="none"
                    xmlns="http://www.w3.org/2000/svg"
                    className="absolute -bottom-1/4 left-[30%] -z-10 hidden lg:block"
                >
                    <path
                        d="M639 390.5H424.4H263.15C263.15 390.5 135.307 395.999 51.1504 279.999C-4.34953 203.499 1.15039 117.999 1.15039 117.999V0"
                        stroke="#001F2C"
                        strokeDasharray="2 2"
                    />
                    <path
                        d="M102.061 299.001C83.8907 299.001 69.1609 313.731 69.1609 331.901C69.1609 350.071 83.8907 364.801 102.061 364.801C120.231 364.801 134.961 350.071 134.961 331.901C134.961 313.731 120.231 299.001 102.061 299.001Z"
                        fill="#F5F7FA"
                        stroke="#001F2C"
                        strokeMiterlimit="10"
                    />
                    <path
                        d="M122.062 305.802C122.062 305.802 80.5625 319.602 74.6625 350.102C67.5625 340.602 64.8629 318.702 80.3625 307.202C95.9625 295.602 110.962 299.602 122.062 305.802Z"
                        fill="#0048FF"
                        stroke="#001F2C"
                        strokeWidth="0.75"
                        strokeMiterlimit="10"
                    />
                </svg>
            </div>

            <div className="relative">
                <CommonPagesSection
                    order="0"
                    title="Fiyatlandırma Simülasyonu"
                    description="Kaynaklardan alınan değişkenlerle gerçekleştirilen değişiklikleri simülasyanda izleyin."
                    src={ThirdImage}
                    id="third"
                    info={3}
                    intersectionHandler={intersectionHandler}
                />

                <svg
                    width="629"
                    height="392"
                    viewBox="0 0 629 392"
                    fill="none"
                    xmlns="http://www.w3.org/2000/svg"
                    className="absolute -bottom-1/4 right-[30%] -z-10 hidden lg:block"
                >
                    <path
                        d="M0.5 390.5H365.5C365.5 390.5 493.343 395.999 577.5 279.999C633 203.499 627.5 117.999 627.5 117.999V0"
                        stroke="#001F2C"
                        strokeDasharray="2 2"
                    />
                    <path
                        d="M540.921 300L558.75 327.796L544.703 357.852L512.829 360.114L495.001 332.318L509.047 302.261L540.921 300Z"
                        fill="#F5F7FA"
                        stroke="#001E2B"
                        strokeWidth="0.75"
                        strokeMiterlimit="10"
                    />
                    <path
                        d="M512.828 360.114L495 332.318L526.875 330.057L544.703 357.852L512.828 360.114Z"
                        fill="#0048FF"
                        stroke="#001E2B"
                        strokeWidth="0.75"
                        strokeMiterlimit="10"
                    />
                    <path
                        d="M540.921 300L526.875 330.057"
                        stroke="#001E2B"
                        strokeWidth="0.75"
                        strokeMiterlimit="10"
                    />
                </svg>
            </div>

            <CommonPagesSection
                order="1"
                title="Fiyatlandırma Özeti"
                description="Yapılan değişikliklerin gerçekleşerek nasıl yansıdığını kontrol edin."
                src={FourthImage}
                id="fourth"
                info={4}
                intersectionHandler={intersectionHandler}
            />
        </>
    );
};

export default ContentSection;
