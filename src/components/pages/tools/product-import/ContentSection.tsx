import { useState } from 'react';
import FirstImage from '@public/images/pages/tools/product-import/First.png';
import FourthImage from '@public/images/pages/tools/product-import/Fourth.png';
import SecondImage from '@public/images/pages/tools/product-import/Second.png';
import ThirdImage from '@public/images/pages/tools/product-import/Third.png';
import {
    CommonPagesInfo,
    CommonPagesSection,
    DemoSection,
    SectionTabs
} from '@src/components/shared';
import { TabEnum } from '@src/interfaces';

const ContentSection = () => {
    const [activeTab, setActiveTab] = useState<TabEnum>('first');

    const intersectionHandler = (activeTab: TabEnum) => {
        setActiveTab(activeTab);
    };

    return (
        <>
            <SectionTabs
                intersectionHandler={intersectionHandler}
                activeTab={activeTab}
                sections={[
                    { id: 'first', title: 'Dosya Tanımlama' },
                    { id: 'second', title: '<PERSON>' },
                    { id: 'third', title: '<PERSON><PERSON><PERSON>tirme' },
                    { id: 'fourth', title: '<PERSON><PERSON><PERSON>' }
                ]}
            />

            <CommonPagesInfo
                title="DAHA BASİT, DAHA KOLAY, DAHA HIZLI"
                description="Üçüncü taraf satış için EnterERP’nin gelişmiş XML entegrasyon çözümlerini kullanmaya başlayın."
            />

            <CommonPagesSection
                order="1"
                title="Tedarikçi dosya uzantılarını kolayca tanımlayın."
                description="Tedarikçilerinizden gelen dosya uzantılarını saniyeler içerisinde tanımlayarak ürünlerinizi hemen oluşturun ve satışa başlayın."
                src={FirstImage}
                id="first"
                info="DOSYA TANIMLAMA YÖNETİMİ"
                intersectionHandler={intersectionHandler}
            />

            <CommonPagesSection
                order="0"
                title="XML’den gelen alanlar ile sistem alanlarını eşleştirin."
                description="EnterERP’de yer alan sistem alanları ile XML verisinden gelen alanları bir havuz içerisinde eşleştirerek iletişimi sağlayın. Alana eşleştirme yöntemi ile tüm sistemlerden gelen dosya uzantılarındaki farklı alanları eşleştirin."
                src={SecondImage}
                id="second"
                info="ALAN EŞLEŞTİRME YÖNETİMİ"
                intersectionHandler={intersectionHandler}
            />

            <DemoSection />

            <CommonPagesSection
                order="1"
                title="XML’den gelen değerler ile sistem değerlerini eşleştirin."
                description="EnterERP’de yer alan sistem değerleri ile XML verisinden gelen değerleri seçim sıralı olarak eşleştirin. Değer eşleştirme yöntemi ile tüm sistemlerden gelen dosya uzantılarındaki farklı değerleri eşleştirin."
                src={ThirdImage}
                id="third"
                info="DEĞER EŞLEŞTİRME YÖNETİMİ"
                intersectionHandler={intersectionHandler}
            />

            <CommonPagesSection
                order="0"
                title="Alan ve değer eşleştirmelerinin doğruluğunu kontrol edin."
                description="Sonuç ekranında eşleşen alanların ve değerlerin üzerinde doğruluk, toplam ürün, konfigure ürün ve basit ürün sayılarının analizini yaparak ürünlerin aktarımını sağlayın."
                src={FourthImage}
                id="fourth"
                info="XML SONUÇ ANALİZİ"
                intersectionHandler={intersectionHandler}
            />
        </>
    );
};

export default ContentSection;
