import { useState } from 'react';
import FirstImage from '@public/images/pages/tools/enter-flow/First.png';
import FourthImage from '@public/images/pages/tools/enter-flow/Fourth.png';
import SecondImage from '@public/images/pages/tools/enter-flow/Second.png';
import ThirdImage from '@public/images/pages/tools/enter-flow/Third.png';
import {
    CommonPagesInfo,
    CommonPagesSection,
    DemoSection,
    SectionTabs
} from '@src/components/shared';
import { TabEnum } from '@src/interfaces';

const ContentSection = () => {
    const [activeTab, setActiveTab] = useState<TabEnum>('first');

    const intersectionHandler = (activeTab: TabEnum) => {
        setActiveTab(activeTab);
    };

    return (
        <>
            <SectionTabs
                intersectionHandler={intersectionHandler}
                activeTab={activeTab}
                sections={[
                    { id: 'first', title: 'Onay' },
                    { id: 'second', title: 'Ak<PERSON><PERSON>' },
                    { id: 'third', title: '<PERSON>rk<PERSON><PERSON>' },
                    { id: 'fourth', title: '<PERSON><PERSON><PERSON><PERSON>' }
                ]}
            />

            <CommonPagesInfo
                title="DAHA AKICI, DAHA KONTROLLÜ, DAHA MERKEZİ"
                description="Manuel olarak yapılan süreç takibinin dağınık ve karmaşıklığından kaynaklanan yapısal hatalardan kurtulun."
            />

            <CommonPagesSection
                order="1"
                title="İş akış isteklerinizi belgeler üzerinde takip edin."
                description="Tüm iş süreçlerinde belirlediğiniz hiyerarşiye göre onay isteklerinizi EnterERP belgeler üzerinde izleyin."
                src={FirstImage}
                id="first"
                info="ONAY TAKİP"
                intersectionHandler={intersectionHandler}
            />

            <CommonPagesSection
                order="0"
                title="Akışı kontrol altına alarak sorumlulukları takip edin."
                description="Çalışanlarınızın onay, red ve bildirim gibi akış süreçlerini yöneterek aldıkları sorumlulukları sistem üzerinden evrak bazında kontrol edin."
                src={SecondImage}
                id="second"
                info="AKIŞ YÖNETİMİ"
                intersectionHandler={intersectionHandler}
            />

            <DemoSection />

            <CommonPagesSection
                order="1"
                title="EnterFlow ile süreçlerinizde akışa takılan tüm belgeleri görün."
                description="Tüm departmanlarda kurulan EnterFlow akış parametrelerine takılan onay, red, doğrulama ve atama gibi iş süreçlerini tek bir panel üzerinden yöneterek takibini yapın."
                src={ThirdImage}
                id="third"
                info="MERKEZİ TAKİP"
                intersectionHandler={intersectionHandler}
            />

            <CommonPagesSection
                order="0"
                title="Akış parametrelerinizi kolayca tanımlayın."
                description="İş akış tanımları ile iş süreçlerinize etki eden durumlar için onaylama, atama, bildirim ve doğrulama türleri oluşturarak görev dağılımını sistem üzerinden yönetin."
                src={FourthImage}
                id="fourth"
                info="SÜREÇ YAPILANDIRMA"
                intersectionHandler={intersectionHandler}
            />
        </>
    );
};

export default ContentSection;
