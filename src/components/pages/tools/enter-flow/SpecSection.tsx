import InfoIcon from '@public/images/shared/Info.svg';
import { InfoCard } from '@src/components/shared';

const SpecSection = () => {
    return (
        <section className="spacer container">
            <div className="grid place-items-start gap-12 md:grid-cols-2 lg:gap-36 xl:grid-cols-4">
                <InfoCard
                    title="Onaylama"
                    description="Akış parametrelerindeki belgeleri kimin ne zaman onaylayacağını tanımlayın."
                    src={InfoIcon}
                />
                <InfoCard
                    title="Atama"
                    description="Belgelerdeki durumlar ve veriye dayalı kritik noktalar için görev tanımlamaları oluşturun."
                    src={InfoIcon}
                />
                <InfoCard
                    title="Bildirim"
                    description="Belgelerdeki durumlar ve veriye dayalı kritik noktalar için bildirim tanımlamaları çalıştırarak haberdar olun."
                    src={InfoIcon}
                />
                <InfoCard
                    title="Doğrulama"
                    description="Belgelerdeki durumlar ve veriye dayalı kritik noktalar için doğrulama çalıştırarak yanlışlardan kaçının."
                    src={InfoIcon}
                />
            </div>
        </section>
    );
};

export default SpecSection;
