import { useState } from 'react';
import FirstImage from '@public/images/pages/tools/online-banking/First.png';
import SecondImage from '@public/images/pages/tools/online-banking/Second.png';
import {
    CommonPagesInfo,
    CommonPagesSection,
    DemoSection,
    SectionTabs
} from '@src/components/shared';
import { TabEnum } from '@src/interfaces';

const ContentSection = () => {
    const [activeTab, setActiveTab] = useState<TabEnum>('first');

    const intersectionHandler = (activeTab: TabEnum) => {
        setActiveTab(activeTab);
    };

    return (
        <>
            <SectionTabs
                intersectionHandler={intersectionHandler}
                activeTab={activeTab}
                sections={[
                    { id: 'first', title: 'Bankacılık' },
                    { id: 'second', title: 'E-Ekstre' }
                ]}
            />

            <CommonPagesInfo
                title="DAHA HIZLI, DAHA GÜVENLİ, DAHA KAPSAMLI"
                description="Bankaların ekstre, DBS gibi açık entegrasyon servislerini kolayca bağlayın ve tüm bankaları tek yerde yönetin."
            />

            <CommonPagesSection
                order="1"
                title="Bankaların bakiyelerini tek panel üzerinden takip edin."
                description="Bankaların açık entegrasyon servislerinden gelen bakiye bilgisini döviz hesabından bağımsız olarak görün. Tüm banka bakiyelerinizi anlık olarak tek ekranda yönetin."
                src={FirstImage}
                id="first"
                info="BAKİYE YÖNETİMİ"
                intersectionHandler={intersectionHandler}
            />

            <DemoSection />

            <CommonPagesSection
                order="0"
                title="Banka hesaplarınızdaki hareketleri hızla işleyin."
                description="E-Ekstre hizmeti ile bankaların hesap hareketleri anlık olarak EnterERP sistemine kaydedilir. Hesap hareketlerini tahsilat ve ödeme kayıtları ile eşleştirerek açıkta kalan kayıtlar için yeni kayıtlar oluşturun."
                src={SecondImage}
                id="second"
                info="ONLİNE EKSTRE YÖNETİMİ"
                intersectionHandler={intersectionHandler}
            />
        </>
    );
};

export default ContentSection;
