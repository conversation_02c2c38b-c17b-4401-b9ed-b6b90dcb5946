import InfoIcon from '@public/images/shared/Info.svg';
import { InfoCard } from '@src/components/shared';

const SpecSection = () => {
    return (
        <section className="spacer container">
            <div className="grid place-items-start gap-12 md:grid-cols-2 lg:gap-36 xl:grid-cols-4">
                <InfoCard
                    title="Online DBS"
                    description="Faturaları otomatik yükleyin, bakiyelerinizi kontrol edin ve ödemeleri kontrol altına alın."
                    src={InfoIcon}
                />
                <InfoCard
                    title="Hesap Mutabakatı"
                    description="Hesap hareketlerini harici uzlaştırma sistemi ile ister manuel ister otomatik olarak anında yapın."
                    src={InfoIcon}
                />
                <InfoCard
                    title="Uzlaştırma Modeli"
                    description="Hesap hareketlerindeki kesintiler ve kazançlar için tek tıkla kayıtlar oluşturarak zaman tasarrufu sağlayın."
                    src={InfoIcon}
                />
                <InfoCard
                    title="Gerçek Zamanlı Finans"
                    description="Online bankacılık raporları sayesinde mali ve operasyon raporlarınızdan gerçek zamanlı olarak doğru veri alın."
                    src={InfoIcon}
                />
            </div>
        </section>
    );
};

export default SpecSection;
