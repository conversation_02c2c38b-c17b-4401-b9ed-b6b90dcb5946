import { useState } from 'react';
import FirstImage from '@public/images/pages/tools/configurator/First.png';
import SixthImage from '@public/images/pages/tools/configurator/Sixth.png';
import {
    CommonPagesFeatured,
    CommonPagesInfo,
    CommonPagesSection,
    DemoSection,
    SectionTabs
} from '@src/components/shared';
import { TabEnum } from '@src/interfaces';

const tabData = [
    {
        disclosureTitle: 'Referans Noktası',
        disclosureDescription:
            'Kişiselleştirmek istediğiniz ürün için ilk başta bir referans oluşturun ve o referansın üzerine ürünü inşa edin.',
        disclosureTabs: [
            {
                tabTitle: 'CRM',
                tabImage: require('@public/images/pages/tools/configurator/Second.png')
            }
        ]
    },
    {
        disclosureTitle: 'Ürün İçin Özellik Seçin',
        disclosureDescription:
            'Tasarlanan ürün için kullanılan malzeme, renk ve form verme gibi nitelik seti ve özellikleri kullanıcılar için belirleyin.',
        disclosureTabs: [
            {
                tabTitle: 'CRM',
                tabImage: require('@public/images/pages/tools/configurator/Third.png')
            }
        ]
    },
    {
        disclosureTitle: 'Tasarımı Ölçeklendirin',
        disclosureDescription:
            'Özel olarak tasarlanan ürün için üretim veya tedarik kapasitenize göre müşterilerinizin anlayabileceği ve yorumlayabileceği şekilde boyut seçenekleri sunun.',
        disclosureTabs: [
            {
                tabTitle: 'CRM',
                tabImage: require('@public/images/pages/tools/configurator/Fourth.png')
            }
        ]
    },
    {
        disclosureTitle: 'Onaydan Önce Ürünü Görün',
        disclosureDescription:
            'Tüm seçenekler işaretlendikten sonra müşterileriniz için oluşacak özet tablosunda görmek istediklerini belirleyin. Ve artık akıllı satışa hazırsın.',
        disclosureTabs: [
            {
                tabTitle: 'CRM',
                tabImage: require('@public/images/pages/tools/configurator/Fifth.png')
            }
        ]
    }
];

const ContentSection = () => {
    const [activeTab, setActiveTab] = useState<TabEnum>('first');

    const intersectionHandler = (activeTab: TabEnum) => {
        setActiveTab(activeTab);
    };

    return (
        <>
            <SectionTabs
                intersectionHandler={intersectionHandler}
                activeTab={activeTab}
                sections={[
                    { id: 'first', title: 'PCM Satış' },
                    { id: 'second', title: 'Özelleştirme' },
                    { id: 'third', title: 'Sonuç' }
                ]}
            />

            <CommonPagesInfo
                title="DAHA HIZLI, DAHA GÜVENLİ, DAHA KAPSAMLI"
                description="Özelleştirme çalışmalarınızı EnterPCM ile otomatik pilota alın, böylece önemli olana konsantre olabilir, mağazanızı büyütebilirsiniz!"
            />

            <CommonPagesSection
                order="0"
                title="Daha hızlı ve daha akıllı satış yapmak için PCM kullanın."
                description="Müşterilerinize kişiye özel üretim seçenekleri sunmak istiyorsanız EnterERP ’nin PCM(ürün konfigüratör yönetim) modülünü kullanarak sınırsız olarak özelleşebilen ve kişiselleştirilebilen ürün varyantları oluşturulmasını sağlayabilirsiniz."
                src={FirstImage}
                id="first"
                info="PCM SATIŞ YÖNETİMİ"
                intersectionHandler={intersectionHandler}
            />

            <div className="[&>section]:bg-primary-50">
                <CommonPagesFeatured
                    id="second"
                    title="EnterERP ile özel ürünler tasarlatın ve akıllı satış yapın."
                    tabData={tabData}
                    intersectionHandler={intersectionHandler}
                />
            </div>

            <CommonPagesSection
                order="0"
                title="Birden çok varyantı olan ürünlerinizi konfigüre ederek sorunsuz satış yapın."
                description="EnterERP dinamik ürün konfigüratorü sayesinde fiyatlamadan ürün teminine kadar tüm süreçlerinizi tek bir sistem üzerinden kolayca yönetir ve yüzlerce üründe politika geliştirmeye uğraşmazsınız. Müşterilerinize varyantlı ürünleriniz için hızlı teklif önerileri sunmak artık çok kolay."
                src={SixthImage}
                id="third"
                info="HIZLI TEKLİF SONUCU"
                intersectionHandler={intersectionHandler}
            />

            <DemoSection />
        </>
    );
};

export default ContentSection;
