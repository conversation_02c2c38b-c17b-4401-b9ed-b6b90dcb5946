import { useState } from 'react';
import FifthImage from '@public/images/pages/apps/finance/Fifth.png';
import FirstImage from '@public/images/pages/apps/finance/First.png';
import FourthImage from '@public/images/pages/apps/finance/Fourth.png';
import SecondImage from '@public/images/pages/apps/finance/Second.png';
import ThirdImage from '@public/images/pages/apps/finance/Third.png';
import {
    CommonPagesInfo,
    CommonPagesSection,
    DemoSection,
    SectionTabs
} from '@src/components/shared';
import { TabEnum } from '@src/interfaces';

const ContentSection = () => {
    const [activeTab, setActiveTab] = useState<TabEnum>('first');

    const intersectionHandler = (activeTab: TabEnum) => {
        setActiveTab(activeTab);
    };

    return (
        <>
            <SectionTabs
                intersectionHandler={intersectionHandler}
                activeTab={activeTab}
                sections={[
                    { id: 'first', title: 'Nakit' },
                    { id: 'second', title: 'Online Ekstre' },
                    { id: 'third', title: 'Ödeme Planı' },
                    { id: 'fourth', title: 'Sanal POS' },
                    { id: 'fifth', title: 'Finansman Araçları' }
                ]}
            />

            <CommonPagesInfo
                title="DAHA TUTARLI, DAHA DİNAMİK, DAHA İŞLEVSEL"
                description="Nakit akışınızı gerçek ve tahminlere dayalı olarak yönetin, temerrüte düşmeyin."
            />

            <CommonPagesSection
                order="1"
                title="Bir bakışta geliriniz, kârınız ve giderleriniz."
                description="Finansal raporlardan günlük nakit akışınızı kolayca takip edebilir, aylık tahsilat ve ödemelerinzi görüntüleyebilir ve yıl içindeki geçmiş ve gelecek finansal performansınızı görebilirsiniz. Giderleriniz ve gelirinizle ilgili önceden doldurulmuş verilerle ayrıntılı nakit akışı raporları oluşturun ve bilinçli harcama kararları vermeniz için gereken bilgileri edinin."
                src={FirstImage}
                id="first"
                info="NAKİT AKIŞ YÖNETİMİ"
                intersectionHandler={intersectionHandler}
            />

            <CommonPagesSection
                order="0"
                title="Banka ekstrelerinizi zahmetsiz uzlaştırın."
                description="Banka hareketlerinizi anında online olarak görerek takip edebilirsiniz. Ekstrelerinizi sistemdeki kayıtlarınız ile zahmetsiz olarak uzlaştırarak açıkta kalan işlemler için otomatik kayıt oluşturabilir veya sebeplerini araştırabilirsiniz."
                src={SecondImage}
                id="second"
                info="ONLİNE EKSTRE YÖNETİMİ"
                intersectionHandler={intersectionHandler}
            />

            <DemoSection />

            <CommonPagesSection
                order="1"
                title="Aynı anda birden çok ödeme yöntemi ile tahsilat yapın."
                description="Fatura veya müşteri ekstre ödemelerinin toplam tutarını aynı anda nakit, havale, çek, senet ve POS işlemi ile birden çok seçenekle alabilir vade farkını kolayca hesaplayarak tahsilatınıza anında ekleyebilirsiniz."
                src={ThirdImage}
                id="third"
                info="GELİŞMİŞ TAHSİLAT YÖNETİMİ"
                intersectionHandler={intersectionHandler}
            />

            <CommonPagesSection
                order="0"
                title="Kredi kartı tahsilatlarınızı sanal POS ile kolayca alın."
                description="İster özel Sanal POS çözümleri olsun ister banka Sanal POS çözümleri olsun, EnterERP ile hızlıca entegre olun ve tahsilatlarınızı kolayca alın. Sanal POS sistemlerinden aldığınız tahsilatları anında muhasebeleştirilir ve ayrıca iptal iade süreçlerinizi hiçbir yere gitmeden EnterERP üzerinden yönetebilirsiniz."
                src={FourthImage}
                id="fourth"
                info="SANAL POS YÖNETİMİ"
                intersectionHandler={intersectionHandler}
            />

            <CommonPagesSection
                order="1"
                title="Finansman araçlarını anlık olarak yönetin."
                description="Finansman kredinizin alınmasından takibine ve işletmenin bu finansmana ihtiyaç duymasına kadar olan tüm süreci sizin yerinize tasarlar. DBS alma veya DBS tanımlama süreçlerinizi kolayca yönetebileceğiniz mali araçları tek bir noktada birleştirerek yönetin."
                src={FifthImage}
                id="fifth"
                info="KREDİ, TEMİNAT VE DBS YÖNETİMİ"
                intersectionHandler={intersectionHandler}
            />
        </>
    );
};

export default ContentSection;
