import InfoIcon from '@public/images/shared/Info.svg';
import { InfoCard } from '@src/components/shared';

const SpecSection = () => {
    return (
        <section className="spacer container">
            <div className="grid place-items-start gap-12 md:grid-cols-2 lg:gap-36 xl:grid-cols-4">
                <InfoCard
                    title="Gelişmiş Ödeme Planı"
                    description="Faturalarınız veya cari alacak/borcunuz için birden fazla ödeme yöntemi ile ödeme planlayın."
                    src={InfoIcon}
                />
                <InfoCard
                    title="Kredi Yönetimi"
                    description="Finansal kuruluşlardan almış olduğunuz finansmanı doğrudan tanımlayarak takip edin."
                    src={InfoIcon}
                />
                <InfoCard
                    title="Teminat Yönetimi"
                    description="Sözleşmler ve anlaşmalar için aldığınız veya verdiğiniz teminatları detaylı yönetin."
                    src={InfoIcon}
                />
                <InfoCard
                    title="POS Yönetimi"
                    description="Kredi kartı POS tahsilatlarının iptali, dönem içi hesap tahsilatını ve komisyon ödemesini yönetin."
                    src={InfoIcon}
                />
            </div>
        </section>
    );
};

export default SpecSection;
