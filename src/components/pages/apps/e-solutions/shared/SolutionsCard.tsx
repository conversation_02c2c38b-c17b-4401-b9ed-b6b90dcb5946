import { StaticImageData } from 'next/image';
import { ExtendedImage, GhostButton } from '@src/components/shared';
import { useTrans } from '@src/hooks';

interface SolutionsCardProps {
    src: string | StaticImageData;
    title: string;
    description: string;
    href: string;
}

const SolutionsCard = ({
    title,
    description,
    src,
    href
}: SolutionsCardProps) => {
    const t = useTrans();

    return (
        <article className="card-shadow grid place-items-center gap-6 rounded-4xl border p-8 md:grid-cols-2 xl:gap-10 xl:p-20">
            <div className="grid gap-6">
                <p className="font-means text-3xl">{t(title)}</p>
                <p className="text-accent-200">{t(description)}</p>
                <div>
                    <GhostButton color="dark" href={href}>
                        {t('İnceleyin')}
                    </GhostButton>
                </div>
            </div>

            <ExtendedImage
                src={src}
                alt={t(title)}
                className="max-md:mx-auto"
                containerClassName="max-md:-order-1 max-w-md md:ml-auto"
            />
        </article>
    );
};

export default SolutionsCard;
