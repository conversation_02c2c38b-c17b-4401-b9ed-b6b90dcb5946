import { Disclosure } from '@headlessui/react';
import { useRef } from 'react';
import { useSectionIntersect, useTrans } from '@src/hooks';
import { MinusIcon, PlusIcon } from '@src/icons/solid';
import { TabEnum } from '@src/interfaces';
import { twMerge } from '@src/utils';

interface FaqDisclosureProps {
    disclosureData: {
        disclosureTitle: string;
        disclosureDescription: string;
    }[];
    id: TabEnum;
    intersectionHandler?: (activeTab: TabEnum) => void;
}

const FaqDisclosure = ({
    disclosureData,
    intersectionHandler,
    id
}: FaqDisclosureProps) => {
    const t = useTrans();

    const ref = useRef<HTMLDivElement>(null);

    useSectionIntersect({ ref, id, intersectionHandler });

    return (
        <div
            id={id}
            ref={ref}
            className="spacer mx-auto max-2xl:container 2xl:w-5/12"
        >
            <h2 className="mb-6 text-center font-means text-4xl">
                {t('<PERSON>ık<PERSON>rulan <PERSON>ru<PERSON>')}
            </h2>

            {disclosureData.map((disclosure, index) => (
                <Disclosure key={index}>
                    {({ open }) => (
                        <>
                            <Disclosure.Button className="flex w-full items-center justify-between border-b py-3 text-lg">
                                <p className="flex-1 text-left font-means">
                                    {t(disclosure.disclosureTitle)}
                                </p>
                                <div className="flex h-10 w-10 items-center justify-center">
                                    {open ? (
                                        <MinusIcon className="h-5 w-5 text-primary-200" />
                                    ) : (
                                        <PlusIcon className="h-5 w-5 text-primary-200" />
                                    )}
                                </div>
                            </Disclosure.Button>

                            <Disclosure.Panel
                                static
                                className={twMerge(
                                    'max-h-0 overflow-hidden pt-3 transition-[max-height] duration-300',
                                    open && 'max-h-96'
                                )}
                            >
                                {t(disclosure.disclosureDescription)}
                            </Disclosure.Panel>
                        </>
                    )}
                </Disclosure>
            ))}
        </div>
    );
};

export default FaqDisclosure;
