import { useState } from 'react';
import FirstImage from '@public/images/pages/apps/e-solutions/e-waybill/First.png';
import {
    CommonPagesInfo,
    CommonPagesSection,
    DemoSection,
    SectionTabs
} from '@src/components/shared';
import { TabEnum } from '@src/interfaces';
import FaqDisclosure from '../shared/FaqDisclosure';

const disclosureData = [
    {
        disclosureTitle: 'e-İrsaliye nedir?',
        disclosureDescription:
            'Fiziki ortamda kağıt olarak düzenlenen irsaliyelerin teknolojinin gelişmesiyle internet ortamında düzenlenerek mükellefler arasında iletilmesi ve saklanmasını sağlayan dijital servistir. VUK 397 nolu maddesi gereğince koşulları taşıyan mükellefler için e-İrsaliye düzenleme zorunluluğu vardır.'
    },
    {
        disclosureTitle: 'Kimler e-İrsaliye düzenleyebilir?',
        disclosureDescription:
            '<PERSON><PERSON><PERSON>ının her yıl belirlediği koşulları taşıyan veya gönüllü olarak GİB portala kayıt yaptıran tüm mükellefler e-İrsaliye düzenleyebilirler.'
    },
    {
        disclosureTitle:
            'Bireysel ve kurumsal tüm müşterilere e-İrsaliye düzenlenebilir mi?',
        disclosureDescription:
            'GİB e-Fatura sistemine kayıtlı mükelleflere, kayıtlı olmayan mükellef ve şahıslara e-İrsaliye düzenleyebilirsiniz.'
    },
    {
        disclosureTitle: 'e-Fatura sistemine nasıl kayıt yaptırabilirim?',
        disclosureDescription:
            'GİB portal üzerinden kaydınızı yaptırabilir veya bir e-Fatura entegratör firma ile e-İrsaliye kullanmaya başlayabilirsiniz.'
    },
    {
        disclosureTitle: 'e-İrsaliye için özel entegratör firma nedir?',
        disclosureDescription:
            'e-Fatura sistemi üzerinden entegratör başvurusu yaparak e-İrsaliye düzenleme ve saklama yetkisi alan firmalara entegratör firma denir.'
    },
    {
        disclosureTitle: 'e-İrsaliye ücretli bir servis mi?',
        disclosureDescription:
            'EnterERP e-İrsaliye modülü tamamen ücretsiz olarak sunulmaktadır.'
    },
    {
        disclosureTitle: 'e-İrsaliye kontör ücreti ne kadar?',
        disclosureDescription:
            'EnterERP e-İrsaliye kontör kullanımı için her bir fatura kontör tutarı her yıl için saklama hizmet karşılığı belirlenerek en uygun fiyat politikasıyla uygulanmaktadır.'
    },
    {
        disclosureTitle: 'e-İrsaliye nasıl düzenlenir?',
        disclosureDescription:
            'EnterERP kullanırken ayrıca e-İrsaliye oluşturmanıza gerek kalmadan sistemde oluşturulan transfer kaydı üzerinden e-İrsaliye gönder butonu ile anında zahmetsiz gönderim sağlayabilirsiniz.'
    }
];

const ContentSection = () => {
    const [activeTab, setActiveTab] = useState<TabEnum>('first');

    const intersectionHandler = (activeTab: TabEnum) => {
        setActiveTab(activeTab);
    };

    return (
        <>
            <SectionTabs
                intersectionHandler={intersectionHandler}
                activeTab={activeTab}
                sections={[
                    { id: 'first', title: 'E-İrsaliye' },
                    { id: 'second', title: 'SSS' }
                ]}
            />

            <CommonPagesInfo
                title="DAHA BASİT, DAHA KOLAY, DAHA HIZLI"
                description="Elektronik irsaliye ile muhasebe ve finansal süreçlerinizi hızlandırarak verimliliği artırın."
            />

            <CommonPagesSection
                order="1"
                title="e- İrsaliye ile dijital ortamda hızlıca irsaliye alın ve gönderin."
                description="Geleneksel kağıt irsaliye yerine irsaliyelerinizi dijital e-İrsaliyeye geçirerek hızlıca irsaliyelerinizi sistem üzerinden gönderin ve alın. Dijital ortamda anlık olarak e-İrsaliye göndererek hem ekstra maliyet yükünden kurtulun hem de zamandan tasarruf edin."
                src={FirstImage}
                id="first"
                info="DİJİTAL İRSALİYE"
                intersectionHandler={intersectionHandler}
            />

            <DemoSection />

            <FaqDisclosure
                id="second"
                disclosureData={disclosureData}
                intersectionHandler={intersectionHandler}
            />
        </>
    );
};

export default ContentSection;
