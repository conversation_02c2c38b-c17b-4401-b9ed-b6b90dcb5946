import { useState } from 'react';
import FirstImage from '@public/images/pages/apps/e-solutions/e-archive/First.png';
import {
    CommonPagesInfo,
    CommonPagesSection,
    DemoSection,
    SectionTabs
} from '@src/components/shared';
import { TabEnum } from '@src/interfaces';
import FaqDisclosure from '../shared/FaqDisclosure';

const disclosureData = [
    {
        disclosureTitle: 'e-Arşiv nedir?',
        disclosureDescription:
            'Fizi<PERSON> ortamda kağıt olarak kesilen faturaların teknolojinin gelişmesiyle internet ortamında düzenlenerek e-Fatura kaydı olmayan mükelleflere veya şahıslara gönderilmesi ve saklanmasını sağlayan dijital sistemdir. VUK 397 nolu maddesi gereğince koşulları taşıyan mükellefler için e-Arşiv düzenleme zorunluluğu vardır.'
    },
    {
        disclosureTitle: 'Kimler e-Arşiv düzenleyebilir?',
        disclosureDescription:
            '<PERSON><PERSON><PERSON><PERSON>şkanlığının her yıl belirlediği koşulları taşıyan veya gönüllü olarak GİB portala kayıt yaptıran tüm mükellefler e-Arşiv düzenleyebilirler. Özellikle elektronik ticaret yapan firmalar için zorunlu hale gelmiştir.'
    },
    {
        disclosureTitle: 'Kimlere e-Arşiv düzenlenebilir?',
        disclosureDescription:
            'GİB e-Fatura sistemine kayıtlı olmayan mükelleflere e-Arşiv düzenlenemezken, kayıtlı olmayan mükellef ve şahıslara e-Arşiv düzenlenebilir.'
    },
    {
        disclosureTitle: 'e-Arşiv sistemine nasıl kayıt yaptırabilirim?',
        disclosureDescription:
            'GİB portal üzerinden kaydınızı yaptırabilir veya bir e-Fatura entegratör firma ile e-Arşiv kullanmaya başlayabilirsiniz.'
    },
    {
        disclosureTitle: 'e-Arşiv için özel entegratör firma nedir?',
        disclosureDescription:
            'GİB sistemi üzerinden entegratör başvurusu yaparak e-Arşiv düzenleme ve saklama yetkisi alan firmalara özel entegratör firma denir.'
    },
    {
        disclosureTitle: 'e-Arşiv ücretli bir servis mi?',
        disclosureDescription:
            'EnterERP e-Arşiv hizmeti tamamen ücretsiz olarak sağlanmaktadır.'
    },
    {
        disclosureTitle: 'e-Arşiv kontör ücreti ne kadar?',
        disclosureDescription:
            'EnterERP e-Arşiv kontör kullanımı için her bir fatura kontör tutarı her yıl için saklama hizmet karşılığı belirlenerek en uygun fiyat politikasıyla uygulanmaktadır.'
    },
    {
        disclosureTitle: 'e-Arşiv nasıl düzenlenir?',
        disclosureDescription:
            'EnterERP kullanırken ayrıca e-Arşiv oluşturmanıza gerek kalmadan sistemde oluşturulan fatura üzerinden e-Arşiv gönder butonu ile anında zahmetsiz gönderin.'
    }
];

const ContentSection = () => {
    const [activeTab, setActiveTab] = useState<TabEnum>('first');

    const intersectionHandler = (activeTab: TabEnum) => {
        setActiveTab(activeTab);
    };

    return (
        <>
            <SectionTabs
                intersectionHandler={intersectionHandler}
                activeTab={activeTab}
                sections={[
                    { id: 'first', title: 'E-Arşiv' },
                    { id: 'second', title: 'SSS' }
                ]}
            />

            <CommonPagesInfo
                title="DAHA BASİT, DAHA KOLAY, DAHA HIZLI"
                description="Elektronik arşiv ile muhasebe ve finansal süreçlerinizi hızlandırarak verimliliği artırın."
            />

            <CommonPagesSection
                order="1"
                title="e- Arşiv ile dijital ortamda fatura gönderin."
                description="Geleneksel kağıt faturanın yerine çevre ve doğa dostu teknoloji ile faturalarınızı dijital e-Arşiv Faturaya geçirerek hızlıca faturalarınızı sistem üzerinden gönderin. Dijital ortamda anlık olarak e-Arşiv göndererek hem ekstra maliyet yükünden kurtulun hem de zamandan kazanın."
                src={FirstImage}
                id="first"
                info="DİJİTAL E-ARŞİV FATURA"
                intersectionHandler={intersectionHandler}
            />

            <DemoSection />

            <FaqDisclosure
                id="second"
                disclosureData={disclosureData}
                intersectionHandler={intersectionHandler}
            />
        </>
    );
};

export default ContentSection;
