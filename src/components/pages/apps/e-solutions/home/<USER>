import Hero from '@public/images/pages/apps/e-solutions/home/<USER>';
import {
    ProductCards,
    CommonPagesHero,
    DemoSection
} from '@src/components/shared';
import ContentSection from './ContentSection';

const ESolutions = () => {
    return (
        <div className="[&>section]:border-b-0">
            <CommonPagesHero
                title="e-Çözümler"
                description="Belgelerinizi EnterERP üzerinden e-Çözümler sistemi ile dijitale geçirerek zaman ve maliyet kaybını düşürün."
                info="E-ÇÖZÜMLER"
                image={Hero}
            />
            <ContentSection />
            <div className="bottom-spacer">
                <DemoSection variant="blue" />
            </div>
            <ProductCards />
        </div>
    );
};

export default ESolutions;
