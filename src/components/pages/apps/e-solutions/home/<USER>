import FirstImage from '@public/images/pages/apps/e-solutions/home/<USER>';
import FourthImage from '@public/images/pages/apps/e-solutions/home/<USER>';
import SecondImage from '@public/images/pages/apps/e-solutions/home/<USER>';
import ThirdImage from '@public/images/pages/apps/e-solutions/home/<USER>';
import SolutionsCard from '../shared/SolutionsCard';

const ContentSection = () => {
    return (
        <section className="bottom-spacer max-md:top-spacer container grid gap-8 xl:gap-12">
            <SolutionsCard
                title="e-Fatura"
                description="Faturalarınızı EnterERP üzerinden e-Fatura sistemi ile dijitale geçirerek zaman ve maliyet kaybını düşürün."
                src={FirstImage}
                href="e-solutions/e-invoice"
            />
            <SolutionsCard
                title="e-Arşiv"
                description="Faturalarınızı EnterERP üzerinden e-Arşiv sistemi ile dijitale geçirerek zaman ve maliyet kaybını düşürün."
                src={SecondImage}
                href="e-solutions/e-archive"
            />
            <SolutionsCard
                title="e-İrsaliye"
                description="Faturalarınızı EnterERP üzerinden e-İrsaliye sistemi ile dijitale geçirerek zaman ve maliyet kaybını düşürün."
                src={ThirdImage}
                href="e-solutions/e-waybill"
            />
            <SolutionsCard
                title="e-Defter"
                description="Faturalarınızı EnterERP üzerinden e-Defter sistemi ile dijitale geçirerek zaman ve maliyet kaybını düşürün."
                src={FourthImage}
                href="e-solutions/e-book"
            />
        </section>
    );
};

export default ContentSection;
