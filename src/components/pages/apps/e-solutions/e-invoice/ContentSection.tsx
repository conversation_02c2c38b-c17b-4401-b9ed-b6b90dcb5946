import { useState } from 'react';
import FirstImage from '@public/images/pages/apps/e-solutions/e-invoice/First.png';
import {
    CommonPagesInfo,
    CommonPagesSection,
    DemoSection,
    SectionTabs
} from '@src/components/shared';
import { TabEnum } from '@src/interfaces';
import FaqDisclosure from '../shared/FaqDisclosure';

const disclosureData = [
    {
        disclosureTitle: 'e-Fatura nedir?',
        disclosureDescription:
            'Fizi<PERSON> ortamda kağıt olarak kesilen faturaların teknolojinin gelişmesiyle internet ortamında düzenlenerek mükellefler arasında iletilmesi ve saklanmasını sağlayan dijital sistemdir. VUK 397 nolu maddesi gereğince koşulları taşıyan mükellefler için e-Fatura düzenleme zorunluluğu vardır.'
    },
    {
        disclosureTitle: 'Kimler e-Fatura düzenleyebilir?',
        disclosureDescription:
            '<PERSON><PERSON><PERSON>lığının her yıl belirlediği koşulları taşıyan veya gönüllü olarak GİB portala kayıt yaptıran tüm mükellefler e-Fatura düzenleyebilirler. '
    },
    {
        disclosureTitle:
            'Bireysel ve kurumsal tüm müşterilere e-Fatura düzenlenebilir mi?',
        disclosureDescription:
            'GİB e-Fatura sistemine kayıtlı mükelleflere e-Fatura düzenlenirken, kayıtlı olmayan mükellef ve şahıslara e-Arşiv Fatura düzenleyebilirsiniz.'
    },
    {
        disclosureTitle: 'e-Fatura için özel entegratör firma nedir?',
        disclosureDescription:
            'e-Fatura sistemi üzerinden entegratör başvurusu yaparak e-Fatura düzenleme ve saklama yetkisi alan firmalara özel entegratör firma denir.'
    },
    {
        disclosureTitle: 'e-Fatura ücretli bir servis mi?',
        disclosureDescription:
            'EnterERP e-Fatura sistemi tamamen ücretsiz olarak sunulmaktadır.'
    },
    {
        disclosureTitle: 'e-Fatura kontör ücreti ne kadar?',
        disclosureDescription:
            'EnterERP e-Fatura kontör kullanımı, her yıl için saklama hizmet karşılığı belirlenerek en uygun fiyat politikasıyla uygulanmaktadır.'
    },
    {
        disclosureTitle: 'e-Fatura nasıl düzenlenir?',
        disclosureDescription:
            'EnterERP kullanırken ayrıca e-Fatura oluşturmanıza gerek kalmadan sistemde oluşturulan fatura üzerinden e-Fatura gönder butonu ile anında zahmetsiz gönderim sağlayabilirsiniz.'
    }
];

const ContentSection = () => {
    const [activeTab, setActiveTab] = useState<TabEnum>('first');

    const intersectionHandler = (activeTab: TabEnum) => {
        setActiveTab(activeTab);
    };

    return (
        <>
            <SectionTabs
                intersectionHandler={intersectionHandler}
                activeTab={activeTab}
                sections={[
                    { id: 'first', title: 'E-Fatura' },
                    { id: 'second', title: 'SSS' }
                ]}
            />

            <CommonPagesInfo
                title="DAHA BASİT, DAHA KOLAY, DAHA HIZLI"
                description="Elektronik fatura ile muhasebe ve finansal süreçlerinizi hızlandırarak verimliliği artırın."
            />

            <CommonPagesSection
                order="1"
                title="e- Fatura ile dijital ortamda kolayca fatura alın ve gönderin."
                description="Geleneksel kağıt faturanın yerine çevre ve doğa dostu teknoloji ile faturalarınızı dijital e-Faturaya geçirerek hızlıca faturalarınızı sistem üzerinden gönderin ve alın. Dijital ortamda anlık olarak e-Fatura göndererek hem ekstra maliyet yükünden kurtulun hem de zamandan kazanın."
                src={FirstImage}
                id="first"
                info="DİJİTAL FATURA"
                intersectionHandler={intersectionHandler}
            />

            <DemoSection />

            <FaqDisclosure
                id="second"
                disclosureData={disclosureData}
                intersectionHandler={intersectionHandler}
            />
        </>
    );
};

export default ContentSection;
