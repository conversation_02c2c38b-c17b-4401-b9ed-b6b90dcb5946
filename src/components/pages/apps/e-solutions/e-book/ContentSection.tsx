import { useState } from 'react';
import FirstImage from '@public/images/pages/apps/accounting/First.png';
import {
    CommonPagesInfo,
    CommonPagesSection,
    DemoSection,
    SectionTabs
} from '@src/components/shared';
import { TabEnum } from '@src/interfaces';
import FaqDisclosure from '../shared/FaqDisclosure';

const disclosureData = [
    {
        disclosureTitle: 'e-Defter nedir?',
        disclosureDescription:
            'e-Defter, Vergi Usul Kanunu ve Türk Ticaret Kanunu hükümleri gereğince tutulması zorunlu olan defterlerin Elektronik Defter Genel Tebliğinde belirtilen format ve standartlara uygun biçimde elektronik dosya biçiminde hazırlanması, bastırılmaksızın kaydedilmesi, değişmezliğinin, bütünlüğünün ve kaynağının doğruluğunun garanti altına alınması ve ilgililer nezdinde ispat aracı olarak kullanılabilmesine imkan tanımayı hedefleyen hukuki ve teknik düzenlemeler bütünüdür.'
    },
    {
        disclosureTitle: 'Kimler e-Defter hazırlayabilir?',
        disclosureDescription:
            'Gelir İdaresi Başkanlığının her yıl belirlediği koşulları taşıyan veya gönüllü olarak GİB portala kayıt yaptıran tüm mükellefler defterlerini e-Defter olarak tasdik ederek saklayabilirler.'
    },
    {
        disclosureTitle:
            'e-Defter kapsamında tutulabilecek defterler nelerdir?',
        disclosureDescription:
            'Yevmiye defteri, büyük defter ve defter beyan sistemi kapsamında tutulan defterler e-Defter olarak tutulabilecektir.'
    },
    {
        disclosureTitle: 'e-Defter sistemine nasıl kayıt yaptırabilirim?',
        disclosureDescription:
            'e-Defter sistemi uygulamasına www.edefter.gov.tr üzerinden kayıt başvurusu yaparak dahil olabilirsiniz.'
    },
    {
        disclosureTitle: 'e-Defter özel entegratör firma nedir?',
        disclosureDescription:
            'e-Defter uygulaması üzerinden entegratör başvurusu yaparak e-Defter düzenleme ve saklama yetkisi alan firmalara özel entegratör firma denir.'
    }
];

const ContentSection = () => {
    const [activeTab, setActiveTab] = useState<TabEnum>('first');

    const intersectionHandler = (activeTab: TabEnum) => {
        setActiveTab(activeTab);
    };

    return (
        <>
            <SectionTabs
                intersectionHandler={intersectionHandler}
                activeTab={activeTab}
                sections={[
                    { id: 'first', title: 'E-Defter' },
                    { id: 'second', title: 'SSS' }
                ]}
            />

            <CommonPagesInfo
                title="DAHA BASİT, DAHA KOLAY, DAHA HIZLI"
                description="Elektronik defter uygulaması ile muhasebe ve finansal süreçlerinizi hızlandırarak verimliliği artırın."
            />

            <CommonPagesSection
                order="1"
                title="e-Defter ile dijital ortamda kolayca defterleriniz tasdik edin."
                description="Geleneksel kağıt defter kaydı saklamanın yerine dijital e-Defter uygulaması ile noter ve kağıt masraflarından kurtulun. Ayrıca defterleriniz online olarak zahmetsiz tasdik ettirin."
                src={FirstImage}
                id="first"
                info="E-DEFTER YÖNETİMİ"
                intersectionHandler={intersectionHandler}
            />

            <DemoSection />

            <FaqDisclosure
                id="second"
                disclosureData={disclosureData}
                intersectionHandler={intersectionHandler}
            />
        </>
    );
};

export default ContentSection;
