import { useState } from 'react';
import FifthImage from '@public/images/pages/apps/income-expense/Fifth.png';
import FirstImage from '@public/images/pages/apps/income-expense/First.png';
import FourthImage from '@public/images/pages/apps/income-expense/Fourth.png';
import SecondImage from '@public/images/pages/apps/income-expense/Second.png';
import ThirdImage from '@public/images/pages/apps/income-expense/Third.png';
import {
    CommonPagesInfo,
    CommonPagesSection,
    DemoSection,
    SectionTabs
} from '@src/components/shared';
import { TabEnum } from '@src/interfaces';

const ContentSection = () => {
    const [activeTab, setActiveTab] = useState<TabEnum>('first');

    const intersectionHandler = (activeTab: TabEnum) => {
        setActiveTab(activeTab);
    };

    return (
        <>
            <SectionTabs
                intersectionHandler={intersectionHandler}
                activeTab={activeTab}
                sections={[
                    { id: 'first', title: '<PERSON><PERSON><PERSON>' },
                    { id: 'second', title: 'G<PERSON>' },
                    { id: 'third', title: 'Seyahat Planlama' },
                    { id: 'fourth', title: '<PERSON><PERSON><PERSON><PERSON>' },
                    { id: 'fifth', title: '<PERSON>or<PERSON>' }
                ]}
            />

            <CommonPagesInfo
                title="DAHA KONTROLLÜ, DAHA TUTARLI, DAHA KAPSAMLI"
                description="İş sehayatlari için masrafları planlayın, çoklu organizasyonlarınız için kolayca toplu giderler oluşturun."
            />

            <CommonPagesSection
                order="1"
                title="Faaliyet dışı gelirlerinizi zahmetsiz oluşturun."
                description="Finansman, kamu ve faaliyet dışı diğer gelirleriniz için kategori bazında gelir oluşturabilir; bütçe, proje ve etiketlerinize göre gruplayarak hızlıca rapor hazırlayabilirsiniz."
                src={FirstImage}
                id="first"
                info="GELİR YÖNETİMİ"
                intersectionHandler={intersectionHandler}
            />

            <CommonPagesSection
                order="0"
                title="Masrafları planlayın ve kategorize edin."
                description="İş seyahati, kira, yemek, yakıt ve diğer tüm masraf faturalarınızı gider yönetiminde gider kategorilerine veya hesaplarına göre kolayca dağıtın. Toplu gider sistemi ile gider hesaplarını kolayca paylaştırın. Proje, bütçe, kategori ve etiketlere göre gruplayarak hemen raporlayın."
                src={SecondImage}
                id="second"
                info="GİDER YÖNETİMİ"
                intersectionHandler={intersectionHandler}
            />

            <DemoSection />

            <CommonPagesSection
                order="1"
                title="İş seyahatlerinizi önceden planlayın."
                description="Seyahatlerinizi önceden planlayarak iş takviminizi oluşturabilir ve yöneticinizin onayına sunabilirsiniz. Seyahat taleplerini planlamak, gelecek iş yükünüzü görmenize yardımcı olarak seyahat için yapılması gereken işlemlerin zamanında ve doğru yapılmasını sağlar."
                src={ThirdImage}
                id="third"
                info="SEYAHAT PLANLAMA"
                intersectionHandler={intersectionHandler}
            />

            <CommonPagesSection
                order="0"
                title="Masraflar için finanstan ödeme talep edin."
                description="Seyahatlariniz veya işletme içi genel ihtiyaçlarınız için masraf talebi oluşturarak finans biriminden ödeme talep edin ve alın."
                src={FourthImage}
                id="fourth"
                info="MASRAF TALEPLERİ"
                intersectionHandler={intersectionHandler}
            />

            <CommonPagesSection
                order="1"
                title="Giderler için yapılan ödemeleri takip edin."
                description="Konaklama, temsil ağırlama, araç kiralama, yemek ve market gibi genel giderleriniz için rapor hazırlayarak ödemeyi kimin yaptığını belirleyerek masraf ödemeniz varsa kalan bakiye için tahsilat alın, ödeme yapın veya ücret ekleyin. Giderlerinizi gider raporları ile tek ekranda raporlayın, yöneticinize ve finans birimine sunun."
                src={FifthImage}
                id="fifth"
                info="GİDER RAPORLARI"
                intersectionHandler={intersectionHandler}
            />
        </>
    );
};

export default ContentSection;
