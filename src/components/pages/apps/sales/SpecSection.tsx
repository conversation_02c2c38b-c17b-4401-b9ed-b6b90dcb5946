import InfoIcon from '@public/images/shared/Info.svg';
import { InfoCard } from '@src/components/shared';

const SpecSection = () => {
    return (
        <section className="spacer container">
            <div className="grid place-items-start gap-12 md:grid-cols-2 lg:gap-36 xl:grid-cols-4">
                <InfoCard
                    title="Fiyat Listeleri"
                    description="Dönem bazlı birden çok fiyat listesi hazırlayabilir ve aynı satış evrağında birden çok liste uygulayabilirsiniz."
                    src={InfoIcon}
                />
                <InfoCard
                    title="Organizasyon"
                    description="Satış bölgelerinizi ve müşteri gruplarınızı belirleyeceğiniz satış ekipleri ile organize edebilirsiniz."
                    src={InfoIcon}
                />
                <InfoCard
                    title="Fiyat Sihirbazı"
                    description="Tüm fiyat listelerinizi tek bir ekran üzerinden yöneterek istediğiniz ürünün fiyatını değiştirebilirsiniz."
                    src={InfoIcon}
                />
                <InfoCard
                    title="Kampanyalar"
                    description="Satış kampanyalarınızı ürün, sipariş, nakliye ve kupan şeklinde çeşitli yöntemlerle hazırlayabilirsiniz."
                    src={InfoIcon}
                />
            </div>
        </section>
    );
};

export default SpecSection;
