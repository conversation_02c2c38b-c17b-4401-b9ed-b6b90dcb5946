import { useState } from 'react';
import FifthImage from '@public/images/pages/apps/sales/Fifth.png';
import FirstImage from '@public/images/pages/apps/sales/First.png';
import FourthImage from '@public/images/pages/apps/sales/Fourth.png';
import SecondImage from '@public/images/pages/apps/sales/Second.png';
import ThirdImage from '@public/images/pages/apps/sales/Third.png';
import {
    CommonPagesFeatured,
    CommonPagesInfo,
    CommonPagesSection,
    DemoSection,
    SectionTabs
} from '@src/components/shared';
import { TabEnum } from '@src/interfaces';

const tabData = [
    {
        disclosureTitle: 'Ödeme Planı',
        disclosureDescription:
            'İstediğiniz ödeme türünde aynı anda ödeme alın. Vade farkını sizin için otomatik hesaplar.',
        disclosureTabs: [
            {
                tabTitle: 'CRM',
                tabImage: require('@public/images/pages/apps/sales/Sales.png')
            }
        ]
    },
    {
        disclosureTitle: 'Otomatik Ödeme Planı',
        disclosureDescription:
            'Ödeme planlarını önceden anlaştığınız yöntemle otomatik hazırlayın. Vade farklarına göre hesaplama yapmadan hesaplanmış değerleri otomatik görüntüleyin.',
        disclosureTabs: [
            {
                tabTitle: 'CRM',
                tabImage: require('@public/images/pages/apps/sales/Sales.png')
            }
        ]
    },
    {
        disclosureTitle: 'Brüt Kâr Hesabı',
        disclosureDescription:
            'Vade farkı ve teslimat koşullarına göre hazırlamış olduğunuz siparişleriniz üzerinden net kâr, brüt kâr, son satın alma kârı, taban maliyet kârı ve tahmini kârlılığınızı hesaplayın.',
        disclosureTabs: [
            {
                tabTitle: 'CRM',
                tabImage: require('@public/images/pages/apps/sales/Sales.png')
            }
        ]
    }
];

const ContentSection = () => {
    const [activeTab, setActiveTab] = useState<TabEnum>('first');

    const intersectionHandler = (activeTab: TabEnum) => {
        setActiveTab(activeTab);
    };

    return (
        <>
            <SectionTabs
                intersectionHandler={intersectionHandler}
                activeTab={activeTab}
                sections={[
                    { id: 'first', title: 'Teklif' },
                    { id: 'second', title: 'Satış' },
                    { id: 'third', title: 'Ödeme Planlama' },
                    { id: 'fourth', title: 'Konfigüratör' },
                    { id: 'fifth', title: 'Müşteri Doğrulama' },
                    { id: 'sixth', title: 'Limit' }
                ]}
            />

            <CommonPagesInfo
                title="DAHA ESNEK, DAHA FAZLA, DAHA YÜKSEK"
                description="Satışlarınızı EnterERP’nin işlevsel özellikleri sayasesinde %35 ‘e kadar arttırın."
            />

            <CommonPagesSection
                order="0"
                title="Teklif süreçlerinizdeki kurallarınızı önceden otomatik olarak belirleyin."
                description="İşletmeler satış stratejilerini belirlerken en önemli süreç olan teklif yönetimini kendi kuralları çerçevesinde özelleştirmek ve kontrol etmek ister. Fiyat listelerinden ödeme planlarına kadar her şeyin anlık yansıyarak stok durumu ve kârlılığınızı gösterdiği, aynı zamanda tüm revizyonların tutulduğu teklif süreçlerinizi kontrol altına alın."
                src={FirstImage}
                info="TEKLİF YÖNETİMİ"
                id="first"
                intersectionHandler={intersectionHandler}
            />

            <CommonPagesSection
                order="1"
                title="Daha hızlı ve daha akıllı satış yapmak için deneyimleyin."
                description="Pandemiden bu yana satış temsilcilerinin %58'i işlerinin sonsuza kadar değiştiğini düşünüyor. Dünyanın dört bir yanındaki liderler, temsilciler ve operasyon ekipleri dijital satış yönetiminin satışlarını arttırdığını ve satışta hızlı olanın kazandığını söylüyor. Siz de işletmenizde EnterERP satış yönetimi ile gerçek zamanlı olarak stok durumu, ödeme planı, vade farkı ve tahmini teslimat gibi süreçleri anlık hesaplayarak hızlı karar almanızı sağlayın."
                src={SecondImage}
                info="SATIŞ YÖNETİMİ"
                id="second"
                intersectionHandler={intersectionHandler}
            />

            <CommonPagesFeatured
                id="third"
                title="EnterERP ödeme planlama ile vade farkına göre kârınızı hesaplayın"
                tabData={tabData}
                intersectionHandler={intersectionHandler}
            />

            <CommonPagesSection
                order="0"
                title="Birden çok varyantı olan ürünlerinizi konfigüre ederek sorunsuz satış yapın."
                description="Müşterilerinize varyantlı ürünleriniz için hızlı çözüm önerileri sunmak artık çok kolay. EnterERP dinamik ürün konfigüratorü sayesinde fiyatlamadan ürün teminine kadar tüm süreçlerinizi tek bir sistem üzerinden kolayca yönetir ve yüzlerce üründe politika geliştirmeye uğraşmazsınız."
                src={ThirdImage}
                info="ÜRÜN KONFİGÜRATORÜ"
                id="fourth"
                intersectionHandler={intersectionHandler}
            />

            <DemoSection />

            <CommonPagesSection
                order="1"
                title="Bireysel ve kurumsal VKN sorgusu ile müşterilerinizi anında doğrulayın."
                description="Müşterilerinizi kimlik veya vergi numarası ile sorgulayıp ad, soyad, unvan, adres ve vergi dairesi gibi bilgilere sistem üzerinden ulaşarak müşterinizin gerçek bir vergi mükellefi olup olmadığını doğrulayabilir ve kullanıcı veri girişini en aza indirebilirsiniz."
                src={FourthImage}
                info="MÜŞTERİ DOĞRULAMA"
                id="fifth"
                intersectionHandler={intersectionHandler}
            />

            <CommonPagesSection
                order="0"
                title="Riskli müşterileriniz için uyarılar alın."
                description="Müşterileriniz işletmenizin sürdürülebilirliğinin kanıtıdır. Müşterilerimize hizmet veya ürün temin ederken onların davranışlarını takip etmek zorunda kalırız. Ticari ilişkimizin devamlılığı ve güvenilirliği için ödeme kurallarımızı uygulamamız gerekir. Limit yönetimi sayesinde müşterilerinize limitler tanımlayın, müsaitliklerini kontrol edin. Uyarı aldığınızda dikkatli olun ve ödeme dengesi olmayan müşterilerinizi kara listeye ekleyerek riskinizi yönetin."
                src={FifthImage}
                info="LİMİT SİSTEMİ"
                id="sixth"
                intersectionHandler={intersectionHandler}
            />
        </>
    );
};

export default ContentSection;
