import InfoIcon from '@public/images/shared/Info.svg';
import { InfoCard } from '@src/components/shared';

const SpecSection = () => {
    return (
        <section className="spacer container">
            <div className="grid place-items-start gap-12 md:grid-cols-2 lg:gap-36 xl:grid-cols-4">
                <InfoCard
                    title="Servis Olayları"
                    description="Olay tabanlı servis ile ekibinizi otomatik olarak doğrudan çözüme yönlendirin."
                    src={InfoIcon}
                />
                <InfoCard
                    title="Servis Organizasyonları"
                    description="Bölgesel planlama yaparak ekiplerinizin iş emirleri için yanıt ve çözüm sürelerini kısaltın."
                    src={InfoIcon}
                />
                <InfoCard
                    title="Kontrollü Servis"
                    description="Gerçek çözüme kavuşturulmuş bir iş emri için ön tanımlı kontroller fırlatarak işinizi sağlama alın."
                    src={InfoIcon}
                />
                <InfoCard
                    title="Hizmet Seviyeleri"
                    description="Müşterilerinize hizmet seviyelerine göre yanıt ve çözüm sunarak kârlılığınızı maksimuma çıkarın."
                    src={InfoIcon}
                />
            </div>
        </section>
    );
};

export default SpecSection;
