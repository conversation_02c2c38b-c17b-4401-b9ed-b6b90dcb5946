import { useState } from 'react';
import FirstImage from '@public/images/pages/apps/services/First.png';
import FourthImage from '@public/images/pages/apps/services/Fourth.png';
import SecondImage from '@public/images/pages/apps/services/Second.png';
import ThirdImage from '@public/images/pages/apps/services/Third.png';
import {
    CommonPagesInfo,
    CommonPagesSection,
    DemoSection,
    SectionTabs
} from '@src/components/shared';
import { TabEnum } from '@src/interfaces';

const ContentSection = () => {
    const [activeTab, setActiveTab] = useState<TabEnum>('first');

    const intersectionHandler = (activeTab: TabEnum) => {
        setActiveTab(activeTab);
    };

    return (
        <>
            <SectionTabs
                intersectionHandler={intersectionHandler}
                activeTab={activeTab}
                sections={[
                    { id: 'first', title: 'Talep' },
                    { id: 'second', title: 'İş Emri' },
                    { id: 'third', title: '<PERSON><PERSON><PERSON>' },
                    { id: 'fourth', title: 'Ekipmanlar' }
                ]}
            />

            <CommonPagesInfo
                title="DAHA HIZLI, DAHA GÜVENLİ, DAHA KAPSAMLI"
                description="Doğru kişileri doğru zamanda planlayarak sevkiyatı en üst düzeye çıkarın, geliri artırın ve sahada yapılan işler için gerçek zamanlı verileri görün."
            />

            <CommonPagesSection
                order="1"
                title="Servis taleplerini hızlıca karşılayın ve %35’ini iş  emri oluşmadan çözün."
                description="Müşterilerinizin servis taleplerini talep ekranından karşılayın, %35’e kadar isteği saha ekibine yönlendirmeden uzaktan çözün. Analizlerle birlikte birçok servis talebinin uzaktan çözüm olasılığını artırarak maliyetleri en alt seviyeye indirin."
                src={FirstImage}
                id="first"
                info="SERVİS TALEPLERİ"
                intersectionHandler={intersectionHandler}
            />

            <CommonPagesSection
                order="0"
                title="Saha veya merkez servis uygulama ekiplerinizin iş emirlerini planlayın."
                description="Alınan servis taleplerini, servis organizasyonlarınıza bölgesel planlama ile otomatik iletin. Saha servis mobil uygulaması ile vakit kaybetmeden probleme çözüm üretin. Olay tabanlı servis uygulaması ile organizasyonlarınızı en iyi şekilde yöneterek maliyetleri düşürün."
                src={SecondImage}
                id="second"
                info="İŞ EMRİ YÖNETİMİ"
                intersectionHandler={intersectionHandler}
            />

            <DemoSection />

            <CommonPagesSection
                order="1"
                title="Ürün ve hizmetleriniz için garanti takibini yönetin."
                description="Garanti sunduğunuz ürün veya hizmetler için otomatik sözleşmeler oluşturun, garanti başlatın ve takip edin."
                src={ThirdImage}
                id="third"
                info="SÖZLEŞME YÖNETİMİ"
                intersectionHandler={intersectionHandler}
            />

            <CommonPagesSection
                order="0"
                title="Bakım ve tamir için ekipman oluşturarak süreç geçmişini bilin."
                description="İster garanti kapsamında ürün satışı için isterseniz bakım sözleşmesi ile garanti hizmeti sağlıyor olun, tüm süreçlerinizi tek bir ekipman üzerinden takip edin. Sözleşme ve iş emirleri ile ekipmanların bakım ve tamir süreçlerini kolaylaştırarak izlenebilir ve raporlanabilir bir sistem kurun."
                src={FourthImage}
                id="fourth"
                info="EKİPMAN YÖNETİMİ"
                intersectionHandler={intersectionHandler}
            />
        </>
    );
};

export default ContentSection;
