import { useState } from 'react';
import FirstImage from '@public/images/pages/apps/help-desk/First.png';
import SecondImage from '@public/images/pages/apps/help-desk/Second.png';
import ThirdImage from '@public/images/pages/apps/help-desk/Third.png';
import {
    CommonPagesInfo,
    CommonPagesSection,
    DemoSection,
    SectionTabs
} from '@src/components/shared';
import { TabEnum } from '@src/interfaces';

const ContentSection = () => {
    const [activeTab, setActiveTab] = useState<TabEnum>('first');

    const intersectionHandler = (activeTab: TabEnum) => {
        setActiveTab(activeTab);
    };

    return (
        <>
            <SectionTabs
                intersectionHandler={intersectionHandler}
                activeTab={activeTab}
                sections={[
                    { id: 'first', title: 'Destek Talebi' },
                    { id: 'second', title: 'Sohbet' },
                    { id: 'third', title: '<PERSON><PERSON><PERSON>' }
                ]}
            />

            <CommonPagesInfo
                title="DAHA KOLAY, DA<PERSON> İŞLEVSEL, DAHA GERÇEK"
                description="Bir destek talebini gerçekçi yorumlar ve kanıtlar ile işlevsel analiz edilebilir hale getirin."
            />

            <CommonPagesSection
                order="1"
                title="Soru işareti doğuran her olay için ticket açın."
                description="Destek sisteminde EnterERP'deki tüm modülleri bağlayabilir, süreçleriniz hakkında konuşabilir ve son kararları alabilirsiniz. Kaybolan ve sürdürülebilir olamayan mail ve whatsapp konuşmaları yerine destek sistemi ile devamlı, gerçek zamanlı bir veri akışı sağlayan destek sistemini kullanarak çözülmeyen konu bırakmayın."
                src={FirstImage}
                id="first"
                info="DESTEK TALEPLERİ"
                intersectionHandler={intersectionHandler}
            />

            <CommonPagesSection
                order="0"
                title="Canlı sohbet sistemi ile daha esnek ticket."
                description="Ticket içerisindeki sohbet yönetimi ile belirlenen grup konuşmacışarı konu hakkında istedikleri yorumu yapabilir çözüm için belge ekleyebilir. Gerçek zamanlı sohbet ile hızlı bir çözüm merkezine kavuşun."
                src={SecondImage}
                id="second"
                info="SOHBET YÖNETİMİ"
                intersectionHandler={intersectionHandler}
            />

            <DemoSection />

            <CommonPagesSection
                order="1"
                title="Departmanlar arası merkezi ortak yardımcı"
                description="Satıştan, satın almaya finanstan muhasebeye tüm departmaların oluşturdukları veya takibini yaptıkları evrakları bağlayarak veriye dayalı bilgi akış ağı kurun."
                src={ThirdImage}
                id="third"
                info="EVRAK İŞLEMLERİ YÖNETİMİ"
                intersectionHandler={intersectionHandler}
            />
        </>
    );
};

export default ContentSection;
