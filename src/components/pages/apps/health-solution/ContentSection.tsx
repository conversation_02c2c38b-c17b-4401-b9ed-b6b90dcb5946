import { useState } from 'react';
import FifthImage from '@public/images/pages/apps/health-solution/Fifth.png';
import FirstImage from '@public/images/pages/apps/health-solution/First.png';
import FourthImage from '@public/images/pages/apps/health-solution/Fourth.png';
import SecondImage from '@public/images/pages/apps/health-solution/Second.png';
import ThirdImage from '@public/images/pages/apps/health-solution/Third.png';
import {
    CommonPagesInfo,
    CommonPagesSection,
    DemoSection,
    SectionTabs
} from '@src/components/shared';
import { TabEnum } from '@src/interfaces';

const ContentSection = () => {
    const [activeTab, setActiveTab] = useState<TabEnum>('first');

    const intersectionHandler = (activeTab: TabEnum) => {
        setActiveTab(activeTab);
    };

    return (
        <>
            <SectionTabs
                intersectionHandler={intersectionHandler}
                activeTab={activeTab}
                sections={[
                    { id: 'first', title: '<PERSON><PERSON><PERSON>' },
                    { id: 'second', title: 'İTS' },
                    { id: 'third', title: 'ÜTS' },
                    { id: 'fourth', title: '<PERSON>' },
                    { id: 'fifth', title: 'Raporlama' }
                ]}
            />

            <CommonPagesInfo
                title="DAHA SAĞLIKLI, DAHA GÜVENLİ, DAHA TEMİZ"
                description="İlaç ve ıtriyat ürünleriniz içim tüm resmi ve saha süreçlerinizi tek platform üzerinden kurgulayın."
            />

            <CommonPagesSection
                order="1"
                title="Müstahzar listenizi otomatik oluşturun."
                description="Ecza deponuz için gerekli olan müstahzar listeniz sizin için hazırlandı. Müstahzar listesindeki ilaç ve ıtriyat ürünlerinin fiyatlarını satış listenize çekerek otomatik fiyatlarınızı belirleyin. Listeleriniz için uygulamak istediğiniz fiyatlar arasından anında seçim yapın."
                src={FirstImage}
                id="first"
                info="İLAÇ YÖNETİMİ"
                intersectionHandler={intersectionHandler}
            />

            <CommonPagesSection
                order="0"
                title="Entegre İTS ile bildirimleri hızlıca yapın."
                description="İlaçlar için üretim, satış ve transfer bildirimlerini entegre çalışan İlaç Takip Sistemi ile zahmetsiz yapın. Arada kalan ilaçlar için özel sorgu yönetimi ile üzerinize almadığınız veya müşterilerinizin üzerine almadığı ilaçları kolayca tespit ederek gerekli işlemleri yapın. Ecza depoları için özel geliştirilen eczane programları entegrasyonları ile ilaç satışlarını anında eczane ekranlarına düşürün."
                src={SecondImage}
                id="second"
                info="İLAÇ TAKİP SİSTEMİ (İTS)"
                intersectionHandler={intersectionHandler}
            />

            <DemoSection />

            <CommonPagesSection
                order="1"
                title="Onaylı ürünleriniz için bildirimleri tek ekranda yönetin."
                description="Üretilen, satışı yapılan, ihraç edilen veya devri gerçekleştirilen ürünlerin bildirimlerini hızlıca yapın. ÜTS sistemi ile tam entegre Enter Sağlık Yönetimi ile operasyonel depo yönetiminizi kusursuz hale getirin."
                src={ThirdImage}
                id="third"
                info="ÜRÜN TAKİP SİSTEMİ (ÜTS)"
                intersectionHandler={intersectionHandler}
            />

            <CommonPagesSection
                order="0"
                title="Mal fazlası ve kampanyaları birlikte kullanın."
                description="İlaç ve tüm ürünleriniz için mal fazlası tanımlayın, satışlarınızı artırmak için ekstra kampanyalar hazırlayın. Enter WebDepo Çözümü ile müşterileriniz için kolayca satışa sunun."
                src={FourthImage}
                id="fourth"
                info="MAL FAZLASI"
                intersectionHandler={intersectionHandler}
            />

            <CommonPagesSection
                order="1"
                title="Gelişmiş raporlama ve analiz sisteminden faydalanın."
                description="İMS rapolarınızı istediğiniz formatta hızlıca hazırlayın ve periyotlar halinde otomatik gönderim sağlayın. Eğer bir ecza deposu iseniz imalatçı roparlanızı önceden size özel hazırlayarak istediğiniz zaman otomatik veya manuel olarak iletilmesini sağlayın."
                src={FifthImage}
                id="fifth"
                info="RAPORLAMA"
                intersectionHandler={intersectionHandler}
            />
        </>
    );
};

export default ContentSection;
