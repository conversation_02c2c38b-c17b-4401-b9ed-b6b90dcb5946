import InfoIcon from '@public/images/shared/Info.svg';
import { InfoCard } from '@src/components/shared';

const SpecSection = () => {
    return (
        <section className="spacer container">
            <div className="grid place-items-start gap-12 md:grid-cols-2 lg:gap-36 xl:grid-cols-4">
                <InfoCard
                    title="Web Depo"
                    description="Eczanelere veya müşterilerinize online olarak kapalı bir bayi portalı kurarak satışları arttırın."
                    src={InfoIcon}
                />
                <InfoCard
                    title="Online İlaç Sipariş"
                    description="Bayi portalı üzerinden müşterilerinizden kredi kartı veya havale ile online sipariş alın."
                    src={InfoIcon}
                />
                <InfoCard
                    title="Mobil İlaç Sipariş"
                    description="Saha satış personeliniz veya müşterileriniz ile mobil sipariş ekranlarından hızlıca sipariş alın."
                    src={InfoIcon}
                />
                <InfoCard
                    title="Tevzi Yönetimi"
                    description="İlaçlara veya ürünlerinize kota koyarak stoğunuzdaki ürünlerin müşterilerinize kısıtlı satılmasını sağlayın."
                    src={InfoIcon}
                />
            </div>
        </section>
    );
};

export default SpecSection;
