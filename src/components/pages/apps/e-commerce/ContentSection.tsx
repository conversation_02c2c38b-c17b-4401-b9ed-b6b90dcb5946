import { useState } from 'react';
import FifthImage from '@public/images/pages/apps/e-commerce/Fifth.png';
import FirstImage from '@public/images/pages/apps/e-commerce/First.png';
import FourthImage from '@public/images/pages/apps/e-commerce/Fourth.png';
import SecondImage from '@public/images/pages/apps/e-commerce/Second.png';
import SixthImage from '@public/images/pages/apps/e-commerce/Sixth.png';
import ThirdImage from '@public/images/pages/apps/e-commerce/Third.png';
import {
    CommonPagesInfo,
    CommonPagesSection,
    DemoSection,
    SectionTabs
} from '@src/components/shared';
import { TabEnum } from '@src/interfaces';

const ContentSection = () => {
    const [activeTab, setActiveTab] = useState<TabEnum>('first');

    const intersectionHandler = (activeTab: TabEnum) => {
        setActiveTab(activeTab);
    };

    return (
        <>
            <SectionTabs
                intersectionHandler={intersectionHandler}
                activeTab={activeTab}
                sections={[
                    { id: 'first', title: 'E-Ticaret' },
                    { id: 'second', title: 'Özg<PERSON><PERSON>' },
                    { id: 'third', title: 'E-İhracat' },
                    { id: 'fourth', title: 'Pazar Yeri' },
                    { id: 'fifth', title: 'XML Entegrasyon' },
                    { id: 'sixth', title: 'B2B' }
                ]}
            />

            <CommonPagesInfo
                title="DAHA HIZLI, DAHA GÜVENLİ, DAHA KAPSAMLI, DAHA ÇEVRİMİÇİ"
                description="EnterStore’u deneyerek işinizi kolaylaştırmak, yürütmek ve büyütmek için ihtiyacınız olan tüm araçları ve hizmetleri keşfedin."
            />

            <div className="relative overflow-hidden">
                <svg
                    width="1632"
                    height="1778"
                    viewBox="0 0 1632 1778"
                    xmlns="http://www.w3.org/2000/svg"
                    className="absolute left-1/2 top-0 -z-10 hidden -translate-x-1/2 fill-primary-50 lg:block"
                >
                    <path d="M1632 222.25v444.5c0 122.684-91.39 222.25-204 222.25s-204 99.566-204 222.25c0 122.68-91.39 222.25-204 222.25-112.609 0-204 99.57-204 222.25S724.608 1778 612 1778H204c-112.608 0-204-99.57-204-222.25v-444.5C0 988.566 91.392 889 204 889h204c225.216 0 408-199.136 408-444.5V222.25C816 99.568 907.391 0 1020 0h408c112.61 0 204 99.568 204 222.25Z" />
                </svg>

                <CommonPagesSection
                    order="1"
                    title="Yıldırım hızında, yüksek performanslı E-Ticaret uygulamaları oluşturun."
                    description="Ön uç teknolojileri sayesinde ışık hızında yükleme süreleri ve dinamik içerik. EnterStore’da yıldırım hızında ürün, alışveriş sepeti ve ödeme deneyimleri kurun, kimlik doğrulaması yapın ve e-ticaret mağazanızı oluşturmaya başlayın. Ürün verilerini depolamak, alışveriş sepetleri oluşturmak, ödemeler oluşturmak, siparişleri yönetmek ve müşterileri işlemek için şimdi bir e-Ticaret platformuna sahip olun."
                    src={FirstImage}
                    id="first"
                    info="ENTER E-COMMERCE"
                    intersectionHandler={intersectionHandler}
                />

                <CommonPagesSection
                    order="0"
                    title="İyi bir marka olmak için özgün tasarımınızı uygulayın."
                    description="“E-Ticaret tarafında iyi satış yapmanın en önemli etkenlerinden biri özgün tasarımdır.” Fiziksel mağazanız için hayal ettiğiniz özgün tasarımı sanal mağazanız içinde uygulayın, marka olma yolunda ilk adımı atın. Ödeme işlemleri dahil olmak üzere e-Ticaretinizin her yönünü özelleştirin, yineleyin ve optimize edin. Kişiselleştirilmiş deneyimlerle müşterileri dönüştürün ve geliri artırın."
                    src={SecondImage}
                    id="second"
                    info="ÖZGÜN TASARIM"
                    intersectionHandler={intersectionHandler}
                />

                <CommonPagesSection
                    order="1"
                    title="Çevrim içi satış yapmak için ihtiyacınız olan her şey"
                    description="Enter Store size internet üzerinden ve dünya çapında her yerde, herkese kolayca satış
                yapma gücü verir. Envanter, sipariş yönetimi ve fiyatlandırma ile bütünleşik ERP ile her şeyi tek bir platformdan kontrol edin. EnterStore ile ister doğrudan tüketiciye ulaşmaya hazır bir B2C olun, ister farklı dilde birkaç markayı yöneten bir B2B olun,  birden çok kanalı ve markayı yönetmeye ve tek bir platformdan yeni ülkelere açılmaya başlayın."
                    src={ThirdImage}
                    id="third"
                    info="E-İHRACAT YÖNETİMİ"
                    intersectionHandler={intersectionHandler}
                />
            </div>

            <DemoSection />

            <CommonPagesSection
                order="0"
                title="Tek ekranda pazaryerlerinin yönetimini sağlayın."
                description="Tek platformda çok kanallı satış için mevcut arka uç sistemleriyle kolayca entegre edin, her mağazaya özel kataloglar ve fiyat listeleri atayarak hızlı ve uygun kampanyalı satış seçenekleriyle satışları artırın. Fiyat listelerini siparişlerle aynı kolaylıkla yönetin, aynı marka ve ürünü birden çok veriyle uğraşmadan tüm sitelerinizde kullanın."
                src={FourthImage}
                id="fourth"
                info="PAZARYERİ YÖNETİMİ"
                intersectionHandler={intersectionHandler}
            />

            <CommonPagesSection
                order="1"
                title="Tedarikçi ürünlerini mağazada hızlıca doğrudan yayınlayın."
                description="Altyapı fark etmeksizin üçüncü taraf yazılımlardan XML formatında gelen ürün, stok, fiyat, marka ve kategori gibi birçok özelliği anında eşleyerek entegre edin. Fiziki mağaza, B2C, B2B ve pazar yerlerinde farklı fiyat, indirim ve kampanya seçenekleri ile ürünlerin satışını yapın."
                src={FifthImage}
                id="fifth"
                info="XML ENTEGRASYON YÖNETİMİ"
                intersectionHandler={intersectionHandler}
            />

            <CommonPagesSection
                order="0"
                title="B2B E-Ticaret çözümümüzle çevrim içi satın almayı kolaylaştırın."
                description="EnterStore B2B, müşterilerinize talep ettikleri tüm B2B işlevleriyle sorunsuz, self servis çevrim içi alışveriş deneyimi sunar. B2B kapalı kanal E-ticaret platformu ile bayi ve müşterilerinize ödeme, ürün kataloğu, teslimat gibi gelişmiş alışveriş seçeneklerini kullandırarak zahmetsiz ve sorunsuz çalışın. Satış ekiplerinizi, üreticileri dijitale geçerken destekleyen sektöre özel çözümlerle güçlendirin ve talep üzerine yeni iş modelleri oluşturarak değişen pazarlara ve müşterilere daha hızlı uyum sağlayın."
                src={SixthImage}
                id="sixth"
                info="B2B YÖNETİMİ"
                intersectionHandler={intersectionHandler}
            />
        </>
    );
};

export default ContentSection;
