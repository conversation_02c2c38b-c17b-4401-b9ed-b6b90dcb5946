import InfoIcon from '@public/images/shared/Info.svg';
import { InfoCard } from '@src/components/shared';

const SpecSection = () => {
    return (
        <section className="spacer container">
            <div className="grid place-items-start gap-12 md:grid-cols-2 lg:gap-36 xl:grid-cols-4">
                <InfoCard
                    title="POS Entegrasyonları"
                    description="İster kredi kartı, ister kapıda ödeme, ister banka havalesi, ister çek. Dilerseniz hepsiyle ödeme alın."
                    src={InfoIcon}
                />
                <InfoCard
                    title="Kargo Entegrasyonları"
                    description="Entegre 150 kargo ile dünyanın her yerinden anlaştığınız firmaya siparişlerinizi gönderebilirsiniz."
                    src={InfoIcon}
                />
                <InfoCard
                    title="Çoklu Mağaza"
                    description="Tek bir yönetici ara yüzünden birden çok mağazayı yönetin. Ürünleri farklı mağazalarda görünecek şekilde ayarlayın."
                    src={InfoIcon}
                />
                <InfoCard
                    title="Özelleştir ve Genişlet"
                    description="Sıfırdan karmaşık bir mağaza tasarlayın ve genişletin. Sizin mağazanız, sizin yolunuz."
                    src={InfoIcon}
                />
                <InfoCard
                    title="Çoklu Parabirimi"
                    description="100'den fazla para biriminde ödeme kabul ederek, seçtiğiniz ağ geçidi aracılığıyla yerel ödeme yöntemleri sunarak küresel müşterilerinizin ihtiyaçlarını karşılayın."
                    src={InfoIcon}
                />
                <InfoCard
                    title="Çoklu Dil"
                    description="Hemen istediğiniz dilde E-Ticaret veya E-İhracat yapmaya başlayın ve satışlarınızı artırın."
                    src={InfoIcon}
                />
                <InfoCard
                    title="Gelişmiş Arama"
                    description="Dünyanın en gelişmiş arama yöntemlerini EnterStore ile deneyimleyerek hızlı alış verişin keyfini çıkarın."
                    src={InfoIcon}
                />
                <InfoCard
                    title="Kampanyalar"
                    description="Dinamik E-Ticaret kampanya sistemi ile ürün, kategori ve teslimat koşullarında anında kampanya oluşturun."
                    src={InfoIcon}
                />
            </div>
        </section>
    );
};

export default SpecSection;
