import InfoIcon from '@public/images/shared/Info.svg';
import { InfoCard } from '@src/components/shared';

const SpecSection = () => {
    return (
        <section className="spacer container">
            <div className="grid place-items-start gap-12 md:grid-cols-2 lg:gap-36 xl:grid-cols-4">
                <InfoCard
                    title="Liste Fiyatları"
                    description="Tedarikçilerin belirlemiş oldukları indirimli birim fiyatları liste fiyatı olarak tanımlayın."
                    src={InfoIcon}
                />
                <InfoCard
                    title="İndirim Sözleşmesi"
                    description="Tedarikçilerin satış hedeflerini belirlemek için verdiği primleri önceden tahmin edin."
                    src={InfoIcon}
                />
                <InfoCard
                    title="Tedarik Katoloğu"
                    description="Ürünlerinizi ve hizmetlerinizi tedarikçiler ile eşleştirerek hızlı fiyatlama ve tedarik sağlayın."
                    src={InfoIcon}
                />
                <InfoCard
                    title="Talep Analizi"
                    description="Tüm tedarik taleplerini görün ve taleplerin zaman içerisinde karşılanma durumunu izleyin."
                    src={InfoIcon}
                />
            </div>
        </section>
    );
};

export default SpecSection;
