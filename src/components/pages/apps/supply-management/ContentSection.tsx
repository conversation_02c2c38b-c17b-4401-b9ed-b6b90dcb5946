import { useState } from 'react';
import FifthImage from '@public/images/pages/apps/supply-management/Fifth.png';
import FirstImage from '@public/images/pages/apps/supply-management/First.png';
import FourthImage from '@public/images/pages/apps/supply-management/Fourth.png';
import SecondImage from '@public/images/pages/apps/supply-management/Second.png';
import ThirdImage from '@public/images/pages/apps/supply-management/Third.png';
import {
    CommonPagesInfo,
    CommonPagesSection,
    DemoSection,
    SectionTabs
} from '@src/components/shared';
import { TabEnum } from '@src/interfaces';

const ContentSection = () => {
    const [activeTab, setActiveTab] = useState<TabEnum>('first');

    const intersectionHandler = (activeTab: TabEnum) => {
        setActiveTab(activeTab);
    };

    return (
        <>
            <SectionTabs
                intersectionHandler={intersectionHandler}
                activeTab={activeTab}
                sections={[
                    { id: 'first', title: '<PERSON><PERSON>' },
                    { id: 'second', title: '<PERSON>ö<PERSON><PERSON><PERSON>' },
                    { id: 'third', title: '<PERSON><PERSON><PERSON><PERSON>' },
                    { id: 'fourth', title: '<PERSON><PERSON><PERSON><PERSON>' },
                    { id: 'fifth', title: 'Transfer' },
                    { id: 'sixth', title: 'Lojistik' }
                ]}
            />

            <CommonPagesInfo
                title="DAHA PLANLI, DAHA SAĞLAM, DAHA DOĞRU, DAHA UYGUN"
                description="Tedarikten ödemeye kadar basitleştirin ve harcamaları her fırsatta kontrol edin. "
            />

            <CommonPagesSection
                order="1"
                title="Tedarik taleplerini zaman ve maliyet tabanlı yönetin."
                description="Tamamen EnterERP üzerine inşa edilen satın alma yazılımımızla, bir talep verildiği anda harcama görünürlüğünü elde edersiniz. Harcama politikaları, bir satın alma siparişi oluşturulmadan önce otomatik olarak uygulanır. Bütçeleri uyumlu ve marjları pozitif tutun."
                src={FirstImage}
                id="first"
                info="TALEP YÖNETİMİ"
                intersectionHandler={intersectionHandler}
            />

            <CommonPagesSection
                order="0"
                title="Satın alma sözleşme hataları ile vedalaşın."
                description="Günlük ihtiyaç duyduğunuz verilere, maliyetleri en aza indirmek ve sıkı pazarlıklar yapmak için ihtiyaç duyduğunuz verilere kolayca erişin. Dönemsel indirim ve primlerinizi esnek takip ederek otomatik faturalaştırın. Çok yıllı sözleşmeleri kolaylıkla yönetin."
                src={SecondImage}
                id="second"
                info="TEDARİKÇİ SÖZLEŞMESİ"
                intersectionHandler={intersectionHandler}
            />

            <DemoSection />

            <CommonPagesSection
                order="1"
                title="Hızlı teklif toplama ile kaynak bulmayı basitleştirin."
                description="Gereksinimleri aynı anda birden fazla tedarikçiye gönderin. Fiyatı, teslimatı ve tüm özel koşulları karşılaştırmak için tek bir havuzda teklifleri yakalayın. Sizin için en doğru sonuçları tespit ederek siparişlerinizi oluşturun."
                src={ThirdImage}
                id="third"
                info="TEKLİF TOPLAMA"
                intersectionHandler={intersectionHandler}
            />

            <CommonPagesSection
                order="0"
                title="Talepleri karşılamak için satın alma emri oluşturun."
                description="Kuruluşunuz genelinde mal ve hizmet tedarikini görün, yönetin ve analiz edin. Bu tedarik yazılımıyla, tedarikten ödemeye kadar tüm süreç boyunca izlenebilirlik ve doğruluk oluşturursunuz. Satın alma siparişlerinin ayrıca satışlarınız için özel rezervasyonlar yaptığını izleyerek ürünün ayak izini takip edebilirsiniz."
                src={FourthImage}
                id="fourth"
                info="SATIN ALMA SİPARİŞİ"
                intersectionHandler={intersectionHandler}
            />

            <CommonPagesSection
                order="1"
                title="Malzemeleri istenilen konuma kolayca yönledirin."
                description="Transfer aşamasındaki tedarik siparişlerinizi lojistik için yönledirin. Tedarik için üretmek, depolar arası nakletmek veya son konuma göndermek için transfer sisteminden yararlanın."
                src={FifthImage}
                id="fifth"
                info="TEDARİK TRANSFERLERİ"
                intersectionHandler={intersectionHandler}
            />

            <CommonPagesSection
                order="0"
                title="Transferleri konumlarına zamanında taşıyın."
                description="Teslim edilmek üzere iki konum arasında, yani yolda olan ürünlerin takibini ve ürünlerin lojistik planlamasını hızlıca yapın. Hareket halindeki gönderiyi takip etmek için kargo firmaları ile entegre olun."
                src={FifthImage}
                id="sixth"
                info="LOJİSTİK"
                intersectionHandler={intersectionHandler}
            />
        </>
    );
};

export default ContentSection;
