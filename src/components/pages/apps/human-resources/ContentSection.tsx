import { useState } from 'react';
import FifthImage from '@public/images/pages/apps/human-resources/Fifth.png';
import FirstImage from '@public/images/pages/apps/human-resources/First.png';
import FourthImage from '@public/images/pages/apps/human-resources/Fourth.png';
import SecondImage from '@public/images/pages/apps/human-resources/Second.png';
import SixthImage from '@public/images/pages/apps/human-resources/Sixth.png';
import {
    CommonPagesFeatured,
    CommonPagesInfo,
    CommonPagesSection,
    DemoSection,
    SectionTabs
} from '@src/components/shared';
import { TabEnum } from '@src/interfaces';

const tabData = [
    {
        disclosureTitle: 'Devamlılık takibi',
        disclosureDescription:
            'Personellerin işe olan devamlılıklarının birçok yöntemle işlenmesini sağlayın.',
        disclosureTabs: [
            {
                tabTitle: 'CRM',
                tabImage: require('@public/images/pages/apps/human-resources/Third.png')
            }
        ]
    },
    {
        disclosureTitle: 'Resmi tatiller',
        disclosureDescription:
            'Resmi tatilleri önceden belirleyin ve planlamalarınızın doğruluğunu arttırın.',
        disclosureTabs: [
            {
                tabTitle: 'CRM',
                tabImage: require('@public/images/pages/apps/human-resources/Third.png')
            }
        ]
    },
    {
        disclosureTitle: 'Puantaj',
        disclosureDescription:
            'İzinler, resmi tatiller, fazla mesailer ve devamlılık verilerinizi tek bir ekranda takip ederek çalışma takvimini tek bir ekranda yönetin.',
        disclosureTabs: [
            {
                tabTitle: 'CRM',
                tabImage: require('@public/images/pages/apps/human-resources/Third.png')
            }
        ]
    }
];

const ContentSection = () => {
    const [activeTab, setActiveTab] = useState<TabEnum>('first');

    const intersectionHandler = (activeTab: TabEnum) => {
        setActiveTab(activeTab);
    };

    return (
        <>
            <SectionTabs
                intersectionHandler={intersectionHandler}
                activeTab={activeTab}
                sections={[
                    { id: 'first', title: 'Özlük' },
                    { id: 'second', title: 'Sözleşme' },
                    { id: 'third', title: 'Puantaj' },
                    { id: 'fourth', title: 'Bordro' },
                    { id: 'fifth', title: 'İzinler' },
                    { id: 'sixth', title: 'Avanslar' }
                ]}
            />

            <CommonPagesInfo
                title="DAHA ESNEK, DAHA AVANTAJLI, DAHA KAPSAMLI, DAHA HIZLI"
                description="Doğru insan kaynakları yönetimi yazılımının çalışanlarınızı üretken, memnun ve verimli tutmanıza nasıl yardımcı olabileceğini keşfedin."
            />

            <CommonPagesSection
                order="1"
                title="Personel bilgi yönetimi ile verilerinizi dijitale taşıyın."
                description="Dijital olarak toplanan çalışan bilgileriyle veri hatalarını ve kayıp evrakları azaltmaya yardımcı olur. Oryantasyon belgelerini, deneyimlerini, yeteneklerini, sertifikalarını, sağlık bilgilerini ve çalışan referans bilgilerini güvenli bir veritabanında saklayın. Süreli belge ve sertifikalar için uyarılar koyun. Sizin için hazırlanmış olan özlük bilgilerini ve raporlama sisteminden ihitiyacınız olan bilgileri raporlayarak analizler çıkarın."
                src={FirstImage}
                id="first"
                info="ÖZLÜK YÖNETİMİ"
                intersectionHandler={intersectionHandler}
            />

            <CommonPagesSection
                order="0"
                title="İş sözleşmelerinizi gerçek bir fonksiyon kazandırın."
                description="Çalışanlarınız ile yapmış olduğunuz iş sözleşmelerinde ücret politikası, ücret tutarı, izin hakkı, devamsızlık takip şekli, çalışma saatleri gibi ve daha fazlası için ihtiyaç duyduğunuz her şeye anında uzaktan erişim sağlayın."
                src={SecondImage}
                id="second"
                info="SÖZLEŞME YÖNETİMİ"
                intersectionHandler={intersectionHandler}
            />

            <CommonPagesFeatured
                id="third"
                title="Personel takip yönetimini en etkin şekilde kullanın"
                tabData={tabData}
                intersectionHandler={intersectionHandler}
            />

            <CommonPagesSection
                order="1"
                title="Çalışan ödemeleri için ay sonunda otomatik bordro oluşturun."
                description="Bordro ihtiyaçlarınız ister basit ister çok karmaşık olsun, Enter Bordro, çalışanlarınıza ödeme yapmayı ve vergi beyannamelerini yönetmeyi kolaylaştıracaktır. Bordro kurulumunuzu, işlemenizi ve raporlamanızı kolaylaştırın ve veri girişini azaltın. Puantaj verilerini otomatik olarak bordro sistemine çekerek verilerinizin doğruluğunu artırın. EnterİK ile harici bir bordro sistemi kullanmanın maliyetini ve karmaşıklığını ortadan kaldırabilir ve finans, muhasebe, iş zekası, dağıtım, üretim, inşaat ve saha hizmetleriyle sorunsuz çalıştırabilirsiniz."
                src={FourthImage}
                id="fourth"
                info="MAAŞ BORDROSU YÖNETİMİ"
                intersectionHandler={intersectionHandler}
            />

            <DemoSection />

            <CommonPagesSection
                order="0"
                title="İş gücü kaynağınızın zamanını doğru ve verimli planlayın."
                description="Çalışanlarınızın zaman çizelgelerine ve iş takvimlerine göre izinlerini planlayabilir, izin taleplerini karşılayabilir veya planlayarak onaylayabilirsiniz. Şirketiniz ve çalışan için en uygun zamanı belirleyerek süreci kolayca yönetebilirsiniz."
                src={FifthImage}
                id="fifth"
                info="İZİN YÖNETİMİ"
                intersectionHandler={intersectionHandler}
            />

            <CommonPagesSection
                order="1"
                title="Avans taleplerini karşılayın ve takip edin."
                description="Avans taleplerini karşılayarak onay süreçlerini yönetin. Avanslar için geri ödeme planı oluşturarak detayları ile birlikte kolayca takip edin."
                src={SixthImage}
                id="sixth"
                info="AVANS YÖNETİMİ"
                intersectionHandler={intersectionHandler}
            />
        </>
    );
};

export default ContentSection;
