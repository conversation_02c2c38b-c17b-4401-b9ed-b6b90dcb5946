import InfoIcon from '@public/images/shared/Info.svg';
import { InfoCard } from '@src/components/shared';

const SpecSection = () => {
    return (
        <section className="spacer container">
            <div className="grid place-items-start gap-12 md:grid-cols-2 lg:gap-36 xl:grid-cols-4">
                <InfoCard
                    title="Ek Ödemeler"
                    description="Çalışanlarınıza vermek istediğiniz ayni, maddi yardımları ve primleri hızlıca oluşturun."
                    src={InfoIcon}
                />
                <InfoCard
                    title="Kesintiler"
                    description="Avans, icra ve diğer kesintilerinizi tek tek veya topluca planlayın bordro döneminde rahat edin."
                    src={InfoIcon}
                />
                <InfoCard
                    title="Fazla Mesailer"
                    description="Gece, hafta sonu ve resmi tatil fazla mesaileri planlayarak ücret hesaplamasına kolayca dahil edin."
                    src={InfoIcon}
                />
                <InfoCard
                    title="BES Kesintisi"
                    description="Yasal zorunlu veya isteğe bağlı BES kesintilerini otomatikleştirerek zahmetsiz yönetin."
                    src={InfoIcon}
                />
            </div>
        </section>
    );
};

export default SpecSection;
