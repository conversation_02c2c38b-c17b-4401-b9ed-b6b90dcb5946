import InfoIcon from '@public/images/shared/Info.svg';
import { InfoCard } from '@src/components/shared';

const SpecSection = () => {
    return (
        <section className="spacer container">
            <div className="grid place-items-start gap-12 md:grid-cols-2 lg:gap-36 xl:grid-cols-4">
                <InfoCard
                    title="Keşif ve Gözlem"
                    description="Satış öncesi yapılan keşif ve gözlemlerin gelecekteki satışlarınıza etkisini görün."
                    src={InfoIcon}
                />
                <InfoCard
                    title="Rakipler"
                    description="<PERSON><PERSON><PERSON><PERSON><PERSON> her satış evrağına ekleyerek rakiplerinizin güçlü ve zayıf yönlerini tespit edin."
                    src={InfoIcon}
                />
                <InfoCard
                    title="Aktivite Analizi"
                    description="Süreçlerinizin tamamında aktivite oluşturmayı unutmayarak aktivitenin süreçlerine etkisini hissedin."
                    src={InfoIcon}
                />
                <InfoCard
                    title="Dönüşüm Analizi"
                    description="Satış evraklarınızın dönüşüm analizlerini izleyerek zayıf olduğunuz durumu öğrenin."
                    src={InfoIcon}
                />
            </div>
        </section>
    );
};

export default SpecSection;
