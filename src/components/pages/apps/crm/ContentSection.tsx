import { useState } from 'react';
import FifthImage from '@public/images/pages/apps/crm/Fifth.png';
import FirstImage from '@public/images/pages/apps/crm/First.png';
import FourthImage from '@public/images/pages/apps/crm/Fourth.png';
import SecondImage from '@public/images/pages/apps/crm/Second.png';
import ThirdImage from '@public/images/pages/apps/crm/Third.png';
import {
    CommonPagesInfo,
    CommonPagesSection,
    DemoSection,
    SectionTabs
} from '@src/components/shared';
import { TabEnum } from '@src/interfaces';

const ContentSection = () => {
    const [activeTab, setActiveTab] = useState<TabEnum>('first');

    const intersectionHandler = (activeTab: TabEnum) => {
        setActiveTab(activeTab);
    };

    return (
        <>
            <SectionTabs
                intersectionHandler={intersectionHandler}
                activeTab={activeTab}
                sections={[
                    { id: 'first', title: '<PERSON>üşteri Adayı' },
                    { id: 'second', title: 'Fırsat' },
                    { id: 'third', title: 'Teklif' },
                    { id: 'fourth', title: 'Aktivite' },
                    { id: 'fifth', title: 'Takvim' }
                ]}
            />

            <CommonPagesInfo
                title="DAHA BASİT, DAHA KOLAY, DAHA HIZLI"
                description="Ekiplerinizi birleştirin, Müşteri 360 ve EnterERP CRM ile müşterilerinizi şaşırtın."
            />

            <CommonPagesSection
                order="1"
                title="Müşterinize odaklanın, işinizi büyütün."
                description="Müşteri adaylarını kayıt edin, onları arada bir rahatsız edin ve onları potansiyel müşterilere dönüştürürek satış yapın."
                src={FirstImage}
                id="first"
                info="MÜŞTERİ ADAYI YÖNETİMİ"
                intersectionHandler={intersectionHandler}
            />

            <CommonPagesSection
                order="0"
                title="Tek platform, yüksek fırsat, sonsuz olasılık."
                description="Müşteri ve müşteri adaylarınızın ürün satın alma olasılıklarını yönetin, onlar için yeni fırsatlar açın ve o fırsatları mükemmel satışlara dönüştürün. Her satış bir olasılıkla başlar. Siz de bu olasılık senaryolarınızı fırsatlarda detaylıca yöneterek satışlarınızı arttırın."
                src={SecondImage}
                id="second"
                info="FIRSAT YÖNETİMİ"
                intersectionHandler={intersectionHandler}
            />

            <DemoSection />

            <CommonPagesSection
                order="1"
                title="Teklif süreçlerinizdeki kurallarınızı önceden otomatik olarak belirleyin."
                description="İşletmeler satış stratejilerini belirlerken en önemli süreç olan teklif yönetimini kendi kuralları çerçevesinde özelleştirmek ve kontrol etmek ister. Fiyat listelerinden ödeme planlarına kadar her şeyin anlık olarak yansıyarak stok durumu ve kârlılığınızı gösterdiği, aynı zamanda tüm revizyonların tutulduğu teklif süreçlerinizi kontrol altına alın."
                src={ThirdImage}
                id="third"
                info="TEKLİF YÖNETİMİ"
                intersectionHandler={intersectionHandler}
            />

            <CommonPagesSection
                order="0"
                title="Müşterileriniz ile teması artırmak satışlarınızı  %35’e kadar arttırır."
                description="Satış evraklarınız üzerinden görev, toplantı, randevu, telefon çağrısı ve etkinlik planlayarak ekibinizi ve müşterilerinizi katılımcı olarak dahil edebilirsiniz."
                src={FourthImage}
                id="fourth"
                info="AKTİVİTE"
                intersectionHandler={intersectionHandler}
            />

            <CommonPagesSection
                order="1"
                title="Takviminize bağlı kalarak zamanınızı doğru yönetin."
                description="İşletme içindeki veya dışındaki tüm aktivitelerinizi takviminize ekleyerek zamanı en iyi şekilde yönetin."
                src={FifthImage}
                id="fifth"
                info="AKTİVİTE TAKVİMİ"
                intersectionHandler={intersectionHandler}
            />
        </>
    );
};

export default ContentSection;
