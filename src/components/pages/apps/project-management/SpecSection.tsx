import InfoIcon from '@public/images/shared/Info.svg';
import { InfoCard } from '@src/components/shared';

const SpecSection = () => {
    return (
        <section className="spacer container">
            <div className="grid place-items-start gap-12 md:grid-cols-2 lg:gap-36 xl:grid-cols-4">
                <InfoCard
                    title="Saha Mal Çıkışları"
                    description="Proje alanına montaj için gönderilen ürünlerin depodan transferini kontrollü sağlayın."
                    src={InfoIcon}
                />
                <InfoCard
                    title="Saha Mal Kabulleri"
                    description="Proje sahasında kullanılmayan ürünlerin tekrar depoya dönüşünü kontrol edin."
                    src={InfoIcon}
                />
                <InfoCard
                    title="Kaynak Planlama"
                    description="İş gücü, ekipman ve araç kaynaklarınızın zaman çizelgesini verimli planlayın."
                    src={InfoIcon}
                />
                <InfoCard
                    title="Proje Riks Analizi"
                    description="Yürütmeye alınan projeleriniz için detaylı zaman ve maliyet raporlarını analiz ederek riskleri en aza indirin."
                    src={InfoIcon}
                />
                <InfoCard
                    title="İnşaat Proje Yönetimi"
                    description="İnşaat firmanızdaki her projelerinizi yönetmek için EnterERP‘nin dinamikliğini kullanın."
                    src={InfoIcon}
                />
                <InfoCard
                    title="Mekanik Proje Yönetimi"
                    description="Mekanik taahhüt firmanızdaki her projelerinizi yönetmek için EnterERP‘nin dinamikliğini kullanın."
                    src={InfoIcon}
                />
                <InfoCard
                    title="Elektrik Proje Yönetimi"
                    description="Elektrik taahhüt firmanızdaki her projelerinizi yönetmek için EnterERP‘nin dinamikliğini kullanın."
                    src={InfoIcon}
                />
                <InfoCard
                    title="Proje Yönetimi"
                    description="Uçtan uca proje yönetim çözümü EnterERP ile karmaşık ve riskli süreçlerinizi kontrol altına alın."
                    src={InfoIcon}
                />
            </div>
        </section>
    );
};

export default SpecSection;
