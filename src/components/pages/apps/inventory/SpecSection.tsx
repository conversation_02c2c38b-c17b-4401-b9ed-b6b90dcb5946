import InfoIcon from '@public/images/shared/Info.svg';
import { InfoCard } from '@src/components/shared';

const SpecSection = () => {
    return (
        <section className="spacer container">
            <div className="grid place-items-start gap-12 md:grid-cols-2 lg:gap-36 xl:grid-cols-4">
                <InfoCard
                    title="Kit Ürün"
                    description="Farklı ürünleri kit altında farklı miktar ve fiyatlarda bir araya getirerek satış yapabilirsiniz."
                    src={InfoIcon}
                />
                <InfoCard
                    title="Küme Ürün"
                    description="Aynı anda birden fazla ürünü birlikte hızlıca seçeneklendirerek evraklarınıza eklemek için kullanabilirsiniz."
                    src={InfoIcon}
                />
                <InfoCard
                    title="Alternatif Ürün"
                    description="Birbirleri yerine kullanılabilen ürünleri eşleştirerek müşterilerinize farklı alternatifler sunabilirsiniz."
                    src={InfoIcon}
                />
                <InfoCard
                    title="Dinamik Özellikler"
                    description="Ürünlerinize dinamik ürün özellik setleri tanımlayabilir, evraklarınızda bu değerlere göre işlem yapabilirsiniz."
                    src={InfoIcon}
                />
            </div>
        </section>
    );
};

export default SpecSection;
