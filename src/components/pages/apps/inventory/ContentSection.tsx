import { useState } from 'react';
import FifthImage from '@public/images/pages/apps/inventory/Fifth.png';
import FirstImage from '@public/images/pages/apps/inventory/First.png';
import FourthImage from '@public/images/pages/apps/inventory/Fourth.png';
import SecondImage from '@public/images/pages/apps/inventory/Second.png';
import ThirdImage from '@public/images/pages/apps/inventory/Third.png';
import {
    CommonPagesInfo,
    CommonPagesSection,
    DemoSection,
    SectionTabs
} from '@src/components/shared';
import { TabEnum } from '@src/interfaces';

const ContentSection = () => {
    const [activeTab, setActiveTab] = useState<TabEnum>('first');

    const intersectionHandler = (activeTab: TabEnum) => {
        setActiveTab(activeTab);
    };

    return (
        <>
            <SectionTabs
                intersectionHandler={intersectionHandler}
                activeTab={activeTab}
                sections={[
                    { id: 'first', title: 'Ürün' },
                    { id: 'second', title: 'Varyant' },
                    { id: 'third', title: 'Depo ve Konum' },
                    { id: 'fourth', title: 'Seri ve Lot' },
                    { id: 'fifth', title: 'Stok' }
                ]}
            />

            <CommonPagesInfo
                title="DAHA HIZLI, DAHA KONTROLLÜ, DAHA HAREKETLİ, DAHA İZLENEBİLİR"
                description="EnterERP Envanter Yönetimi, arz ve talebi dengelemek için esnek ürün yönetimi, kaliteli izlenebilirlik ile envanter süreçlerini basitleştirmenize yardımcı olur."
            />

            <CommonPagesSection
                order="1"
                title="Envanteri optimize edin, karmaşık öğeleri yönetin."
                description="Güvenlik stoğu, minimum/maksimum sipariş miktarları ve ambar transferleri ile envanter dönüşlerini artırmak için ikmal ve otomasyonu kullanın. Barkodlama ve fiziksel envanter döngüsü sayımları ile envanter doğruluğunu iyileştirin. Öğe öznitelik varyasyonlarına dayalı ürün aileleri oluşturmak için matris öğelerinden yararlanın, parti ve seri takip edilen öğeleri kolayca yönetin. Anında özel ölçü birimleri ve dönüşümler tanımlayın."
                src={FirstImage}
                id="first"
                info="ÜRÜN YÖNETİMİ"
                intersectionHandler={intersectionHandler}
            />

            <CommonPagesSection
                order="0"
                title="Ürünlerinizi konfigüre ederek daha kolay yönetin."
                description="Renk ve beden gibi niteliklere sahip ürünlerinizi nitelik setleri ile aynı anda onlarca ürün varyantını oluşturabilir, görsel ekleyebilir ve özelliklerini seçebilirsiniz. E-ticaret, mağaza ve saha satış ekiplerinizle farklı listelerle paylaşarak hızlıca satışını yapmaya başlayın."
                src={SecondImage}
                id="second"
                info="VARYANT ÜRÜN YÖNETİMİ"
                intersectionHandler={intersectionHandler}
            />

            <DemoSection />

            <CommonPagesSection
                order="1"
                title="Karmaşık depoları düzenleyin, ürünleri zahmetsizce bulun."
                description="Şirketlerin coğrafi olarak dağınık konumlarda sistem çapında envanteri kontrol etmelerini
                depo bazında bilgi akışını sağlamalarını sağlayan sınırsız varyasyonlu depo oluşturma. Depoları parçalara ayırarak ürünlerin gerçek fiili yerlerinin tespiti için konum tabanlı ürün yerleştirme ve kaldırma stratejilerini manuel veya otomatik olarak sunuyor."
                src={ThirdImage}
                id="third"
                info="GELİŞMİŞ DEPO VE KONUM YÖNETİMİ"
                intersectionHandler={intersectionHandler}
            />

            <CommonPagesSection
                order="0"
                title="Ürün izlemeyi rahatlatan takip numaraları."
                description="Ürünlerin stoğu kadar envanter ve transfer süreçlerindeki hareketler de bir o kadar önemlidir. Ürünlerin tüm hareketlerini daha izlenebilir kılmak ve kolay yönetmek için bazı numaralandırma standartları olan seri, lot ve barkod uygulamalarını kullanmak kaçınılmazdır. Envanter Yönetimi Yazılımı, satılan mal maliyetleri ve envanter taşıma maliyetleri için kesin, gerçek zamanlı izleme ile sürprizlerden kaçınmanıza yardımcı olur. Spesifik (fiili) envanter maliyetlerinin izlenmesi için lot ve seri takibi kullanın."
                src={FourthImage}
                id="fourth"
                info="SERİ,LOT VE BARKOD UYGULAMARI"
                intersectionHandler={intersectionHandler}
            />

            <CommonPagesSection
                order="1"
                title="Müşteri memnuniyetini artırın, belgeleri yerine getirmeyi iyileştirin."
                description="Stok rezervasyonları ve gecikmiş siparişleri, iadeleri, iptalleri ve değişimleri yönetmeye yönelik araçlarla konumlardaki envanter seviyelerine gerçek zamanlı erişimle söz verildiği gibi satışları ve transferleri yerine getirin. Güvenli stok, minimum/maksimum sipariş miktarları ve iyileştirilmiş karşılama oranları için yapılandırılabilir talep tahmini formülleri ile stok tükenmesini önlemek için envanteri etkili bir şekilde yönetin."
                src={FifthImage}
                id="fifth"
                info="STOK YÖNETİMİ"
                intersectionHandler={intersectionHandler}
            />
        </>
    );
};

export default ContentSection;
