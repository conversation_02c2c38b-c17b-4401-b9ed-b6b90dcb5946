import InfoIcon from '@public/images/shared/Info.svg';
import { InfoCard } from '@src/components/shared';

const SpecSection = () => {
    return (
        <section className="spacer container">
            <div className="grid place-items-start gap-12 md:grid-cols-2 lg:gap-36 xl:grid-cols-4">
                <InfoCard
                    title="Bilanço"
                    description="Finansal durumunuzu bir bakışta izleyerek karar alma öngörüleri çıkartın."
                    src={InfoIcon}
                />
                <InfoCard
                    title="Gelir Tablosu"
                    description="Anlık olarak kârlılığınızı takip ederek satış stratejinizi doğrulayın ya da yeni stratejiler belirleyin."
                    src={InfoIcon}
                />
                <InfoCard
                    title="Hesap Planı"
                    description="Ülkenin en iyi hazırlanmış hesap yönetimini deneyimleyerek rahat bir işleyiş sağlayın."
                    src={InfoIcon}
                />
                <InfoCard
                    title="Defter Kayıtları"
                    description="Defter kayıt sistemini ve defter kayıtlarınızı gerçek zamanlı olarak hızlıca alın."
                    src={InfoIcon}
                />
            </div>
        </section>
    );
};

export default SpecSection;
