import { StaticImageData } from 'next/image';
import { ExtendedImage } from '@src/components/shared';
import { useTrans } from '@src/hooks';

interface DescCardProps {
    title: string;
    description: string;
    image: StaticImageData | string;
}

const DescCard = ({ title, description, image }: DescCardProps) => {
    const t = useTrans();

    return (
        <article className="grid gap-6 rounded-2xl border-2 border-primary-500 p-6 text-left text-white">
            <div>
                <ExtendedImage src={image} alt={t(title)} />
            </div>
            <h3 className="font-means text-2xl">{t(title)}</h3>
            <p className="text-sm text-accent-100">{t(description)}</p>
        </article>
    );
};

export default DescCard;
