import InfoIcon from '@public/images/shared/Info.svg';
import { GhostButton, InfoCard, SolidButton } from '@src/components/shared';
import { useTrans } from '@src/hooks';

const SpecSection = () => {
    const t = useTrans();

    return (
        <section className="spacer container">
            <h2 className="text-center font-means text-4xl">
                {t('Entegre veri hizmetleri paketiyle daha hızlı oluşturun')}
            </h2>
            <div className="mt-8 flex flex-col items-center justify-center gap-6 md:flex-row lg:mt-16">
                <SolidButton href="/signup">{t('Ücretsiz Dene')}</SolidButton>
                <GhostButton
                    className="max-md:ml-5"
                    href="/appointment"
                    color="dark"
                >
                    {t('Randevu Talep Et')}
                </GhostButton>
            </div>
            <div className="grid place-items-start gap-12 pt-12 md:grid-cols-2 lg:gap-36 lg:pt-28 xl:grid-cols-4">
                <InfoCard
                    title="Bulut Tabanlı"
                    description="İster imalatta, ister inşaatta, toptan satışta veya herhangi bir işte olun, bulut yazılımımız işinize göre uyarlanabilir."
                    src={InfoIcon}
                />
                <InfoCard
                    title="Online Finans Yönetimi"
                    description="EnterERP, daha fazla banka entegrasyonları ile işletmenizin nakit akışının güncel ve doğru bir görünümü elde etmesine yardımcı olur."
                    src={InfoIcon}
                />
                <InfoCard
                    title="Dinamik Yapı"
                    description="Müşterileriniz değerli uzmanlığınıza güveniyor. Peki ya onlara daha hızlı, daha derin ve daha zengin önerilerde bulunabilseydiniz?"
                    src={InfoIcon}
                />
                <InfoCard
                    title="Bütünleşik Entegrasyon"
                    description="EnterERP, 50'den fazla uygulamayla entegre olarak satış noktasından e-posta pazarlamasına kadar her şeyi yönetmenize yardımcı olur."
                    src={InfoIcon}
                />
            </div>
        </section>
    );
};

export default SpecSection;
