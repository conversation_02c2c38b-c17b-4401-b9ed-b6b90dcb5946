import { useEffect, useMemo, useRef, useState } from 'react';
import ChartIcon from '@public/images/pages/company/all-in-one/Chart.svg';
import EnterPowerFour from '@public/images/pages/company/all-in-one/EnterPowerFour.svg';
import EnterPowerOne from '@public/images/pages/company/all-in-one/EnterPowerOne.svg';
import EnterPowerThree from '@public/images/pages/company/all-in-one/EnterPowerThree.svg';
import EnterPowerTwo from '@public/images/pages/company/all-in-one/EnterPowerTwo.svg';
import SearchIcon from '@public/images/pages/company/all-in-one/Search.svg';
import {
    CommonPagesInfo,
    ExtendedImage,
    SectionTabs
} from '@src/components/shared';
import { useIntersectionObserver, useTrans } from '@src/hooks';
import { TabEnum } from '@src/interfaces';
import { twMerge } from '@src/utils';
import DescCard from '../../DescCard';

const DesktopSection = () => {
    const [image, setImage] = useState(EnterPowerOne);
    const [loading, setLoading] = useState(false);

    const [activeTab, setActiveTab] = useState<TabEnum>('first');

    const intersectionHandler = (activeTab: TabEnum) => {
        setActiveTab(activeTab);
    };

    const t = useTrans();

    const firstRef = useRef<HTMLDivElement>(null);
    const secondRef = useRef<HTMLDivElement>(null);
    const thirdRef = useRef<HTMLDivElement>(null);
    const fourthRef = useRef<HTMLDivElement>(null);

    const accordionData = useMemo(
        () => [
            {
                id: 'first',
                ref: firstRef,
                title: 'Her şey tek bir yerde',
                description:
                    'Tüm iş süreçleri ve ekiplerin işbirliği için tek ve eksiksiz bir platform edinin.',
                cards: [
                    {
                        id: 11,
                        title: 'Ölçeklenir',
                        description:
                            'Şirketiniz büyüdükçe ve platforma daha fazla ekip katıldıkça EnterERP’yi kolayca ölçekleyebilirsiniz. Tüm ihtiyaçlarınızı karşılamak için özelleştirilebilir ve genişletilebilir.',
                        image: SearchIcon
                    },
                    {
                        id: 12,
                        title: 'Genişletilebilir',
                        description:
                            'Tek bir platform üzerinde entegrasyonlar oluşturun ve zengin modül içeriklerini kullanarak EnterERP ile iş süreçlerini otomatikleştirin.',
                        image: ChartIcon
                    }
                ]
            },
            {
                id: 'second',
                ref: secondRef,
                title: 'EnterERP Geleceğe Hazır Modüler Bulut ERP Platformu',
                description:
                    'Büyüyen işletmelere, önemli sermaye yatırımı ve personel artışına gerek kalmadan BT altyapılarını ve iş uygulamalarını dağıtmak ve desteklemek için en uygun maliyetli, esnek ve verimli yolu sağlamak üzere tasarlanmış çok yönlü bir platform.',
                cards: [
                    {
                        id: 21,
                        title: 'Tedarik',
                        description:
                            'EnterERP Tedarik çözümleri, toplam sahip olma maliyetini düşürmenize, ölçeklenebilirliği etkinleştirmenize, güvenliği ve çalışma süresini iyileştirmenize yardımcı olmak için geliştirildi.',
                        image: SearchIcon
                    },
                    {
                        id: 22,
                        title: 'Finans',
                        description:
                            'Nakit varlıklarınızın tam olarak ne seviyede olduğunu, nereye gittiğini görün ve işletme  için finansman ihtiyacınız olup olmadığını  önceden tespit edin.',
                        image: ChartIcon
                    },
                    {
                        id: 23,
                        title: 'Satış ve CRM',
                        description:
                            'Müşterileriniz nerede olursa olsun; çevrim içi, yüz yüze ve her zaman her yerde satış yapmanızı sağlayan tek platform. Daha hızlı ve daha akıllı bir satış için ihtiyacınız olan araçları edinin.',
                        image: SearchIcon
                    },
                    {
                        id: 24,
                        title: 'İnsan Kaynakları',
                        description:
                            'İnsan Kaynakları ihtiyaçlarınız ister basit ister çok karmaşık olsun; EnterERP, çalışanlarınıza ödeme yapmayı ve vergi beyannamelerini yönetmeyi kolaylaştıracaktır.',
                        image: ChartIcon
                    }
                ]
            },
            {
                id: 'third',
                ref: thirdRef,
                title: 'Dünyanın ilk gerçek zamanlı ERP’si ile çalışın',
                description:
                    'Ofiste değil misiniz? Problem değil. Herhangi bir tarayıcıdan veya cihazdan fatura gönderin, nakit akışını yönetin, giderleri takip edin ve iş birliği yapın. Esnek ve erişilebilir olun.',
                cards: [
                    {
                        id: 31,
                        title: 'Tek Platform',
                        description:
                            'Tek platform üzerinden ekiplerinizle anlık birlikte çalışmanın konforunu  test edin. Derlenen sonuçları sizin için EnterERP kolayca değerlendirsin.',
                        image: SearchIcon
                    },
                    {
                        id: 32,
                        title: 'Güncel',
                        description:
                            'Verimli çalışmak için güncel veriler ile karar almanız gerekir. Birbiri ile iletişimsiz disiplinleri unutun, öngörüleri önceden sunan bir EnterERP var.',
                        image: ChartIcon
                    },
                    {
                        id: 33,
                        title: 'Kontrollü',
                        description:
                            'Ekiplere işletmenin büyüme eğrilerinde enerji verimliliklerini düşürmeden çalışma fırsatı verin. Kontrolü alt yapının anahtar çözüm uygulamalarına bırakın.',
                        image: SearchIcon
                    },
                    {
                        id: 34,
                        title: 'Erişilebilir',
                        description:
                            'Sonuç almak için esnekliği elden bırakmadan her yerde her an her şeye ulaşın. Fırsatlara karşı her zaman hazır olun.',
                        image: ChartIcon
                    }
                ]
            },
            {
                id: 'fourth',
                ref: fourthRef,
                title: 'İş yüklerinizi güvenli ve kendinden emin bir şekilde ölçeklendirin',
                description:
                    "Güvenilir bir temel, sunduğumuz her şeyin merkezinde yer alır. Yerleşik veri güvenliği ve kurtarma varsayılanlarıyla başlayın, ardından ihtiyacınız olan kontrolleri ekleyin. Atlas'ın küresel ve çoklu bulut erişimiyle dünyanın her yerinde uygulamaları çalıştırın.",
                cards: [
                    {
                        id: 41,
                        title: 'Uçtan Uca Güvenlik',
                        description:
                            'MongoDB, tüm verileriniz için yerleşik güvenlik kontrolleri sunar. Mevcut güvenlik protokolleriniz ve uyumluluk standartlarınızla bütünleşmek için kurumsal düzeyde özellikleri sağlar.',
                        image: SearchIcon
                    },
                    {
                        id: 42,
                        title: 'Çoklu Bulut',
                        description:
                            'MongoDB, küresel olarak dağıtılan tek çoklu bulut veritabanıdır.',
                        image: ChartIcon
                    }
                ]
            }
        ],
        []
    );

    const firstEntry = useIntersectionObserver(firstRef, {
        threshold: 0,
        rootMargin: '0px 0px -85%'
    });
    const secondEntry = useIntersectionObserver(secondRef, {
        threshold: 0,
        rootMargin: '0px 0px -85%'
    });
    const thirdEntry = useIntersectionObserver(thirdRef, {
        threshold: 0,
        rootMargin: '0px 0px -85%'
    });
    const fourthEntry = useIntersectionObserver(fourthRef, {
        threshold: 0,
        rootMargin: '0px 0px -85%'
    });

    useEffect(() => {
        if (firstEntry?.isIntersecting) {
            setImage(EnterPowerOne);
            if (image == EnterPowerOne) return;
            setLoading(true);
            setTimeout(() => {
                setLoading(false);
            }, 300);
        }
        if (secondEntry?.isIntersecting) {
            setImage(EnterPowerTwo);
            if (image == EnterPowerTwo) return;
            setLoading(true);
            setTimeout(() => {
                setLoading(false);
            }, 300);
        }
        if (thirdEntry?.isIntersecting) {
            setImage(EnterPowerThree);
            if (image == EnterPowerThree) return;
            setLoading(true);
            setTimeout(() => {
                setLoading(false);
            }, 300);
        }
        if (fourthEntry?.isIntersecting) {
            setImage(EnterPowerFour);
            if (image == EnterPowerFour) return;
            setLoading(true);
            setTimeout(() => {
                setLoading(false);
            }, 300);
        }
    }, [
        firstEntry?.isIntersecting,
        secondEntry?.isIntersecting,
        thirdEntry?.isIntersecting,
        fourthEntry?.isIntersecting,
        image
    ]);

    return (
        <section className="hidden lg:block">
            <SectionTabs
                intersectionHandler={intersectionHandler}
                activeTab={activeTab}
                sections={[
                    { id: 'first', title: 'EnterERP' },
                    { id: 'second', title: 'Uygulamalar' },
                    { id: 'third', title: 'Enter Runtime' },
                    { id: 'fourth', title: 'Runtime' }
                ]}
            />

            <CommonPagesInfo
                title="DAHA HIZLI, DAHA GÜVENLİ, DAHA KAPSAMLI"
                description="Geleceğin teknolojisine dokunun. Her ihtiyacınız için bir uygulama..."
            />

            <div className="relative min-h-screen bg-primary-900">
                <div className="absolute -top-[1px] left-0 hidden 2xl:block">
                    <svg
                        width="696"
                        height="686"
                        viewBox="0 0 696 686"
                        fill="none"
                        xmlns="http://www.w3.org/2000/svg"
                    >
                        <path
                            d="M695.239 0H-10L-9.99976 685.342C-10.1619 559.182 94.612 456.894 223.838 456.894H227.239C356.465 456.894 461.239 354.607 461.239 228.448C461.239 102.288 566.013 0 695.239 0Z"
                            fill="white"
                        />
                    </svg>
                </div>

                <div className="container flex gap-20">
                    <div className="min-h-screen w-1/2">
                        <ExtendedImage
                            className={twMerge(
                                'spacer sticky top-24 w-full transition-opacity duration-300',
                                loading ? 'opacity-50' : 'opacity-100'
                            )}
                            src={image}
                            alt={t('Katmanlı yapı')}
                        />
                    </div>

                    <div className="spacer w-1/2">
                        {accordionData.map((item) => (
                            <div
                                key={item.id}
                                ref={item.ref}
                                id={item.id}
                                className="mb-60 flex flex-col gap-12 pt-10 text-center"
                            >
                                <div className="grid gap-4 text-left">
                                    <h2 className="font-means text-4xl text-white">
                                        {t(item.title)}
                                    </h2>
                                    <p className="text-lg text-accent-100">
                                        {t(item.description)}
                                    </p>
                                </div>
                                <div className="grid gap-12 px-16 2xl:grid-cols-2">
                                    {item.cards.map((card) => (
                                        <DescCard
                                            key={card.id}
                                            title={card.title}
                                            description={card.description}
                                            image={card.image}
                                        />
                                    ))}
                                </div>
                            </div>
                        ))}
                    </div>
                </div>
            </div>
        </section>
    );
};

export default DesktopSection;
