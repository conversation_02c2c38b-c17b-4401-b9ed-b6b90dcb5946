import { useState } from 'react';
import {
    Accordion,
    AccordionItem,
    AccordionItemButton,
    AccordionItemHeading,
    AccordionItemPanel
} from 'react-accessible-accordion';
import { useSwipeable } from 'react-swipeable';
import ChartIcon from '@public/images/pages/company/all-in-one/Chart.svg';
import EnterPowerFour from '@public/images/pages/company/all-in-one/EnterPowerFour.svg';
import EnterPowerOne from '@public/images/pages/company/all-in-one/EnterPowerOne.svg';
import EnterPowerThree from '@public/images/pages/company/all-in-one/EnterPowerThree.svg';
import EnterPowerTwo from '@public/images/pages/company/all-in-one/EnterPowerTwo.svg';
import SearchIcon from '@public/images/pages/company/all-in-one/Search.svg';
import { CommonPagesInfo, ExtendedImage } from '@src/components/shared';
import { useTrans } from '@src/hooks';
import { twMerge } from '@src/utils';
import DescCard from '../../DescCard';

const images = [EnterPowerOne, EnterPowerTwo, EnterPowerThree, EnterPowerFour];

const accordionData = [
    {
        id: 1,
        title: 'Her şey tek bir yerde',
        description:
            'Tüm iş süreçleri ve ekiplerin işbirliği için tek ve eksiksiz bir platform edinin.',
        cards: [
            {
                id: 11,
                title: 'Ölçeklenir',
                description:
                    'Şirketiniz büyüdükçe ve platforma daha fazla ekip katıldıkça EnterERP’yi kolayca ölçekleyebilirsiniz. Tüm ihtiyaçlarınızı karşılamak için özelleştirilebilir ve genişletilebilir.',
                image: SearchIcon
            },
            {
                id: 12,
                title: 'Genişletilebilir',
                description:
                    'Tek bir platform üzerinde entegrasyonlar oluşturun ve zengin modül içeriklerini kullanarak EnterERP ile iş süreçlerini otomatikleştirin.',
                image: ChartIcon
            }
        ]
    },
    {
        id: 2,
        title: 'EnterERP Geleceğe Hazır Modüler Bulut ERP Platformu',
        description:
            'Büyüyen işletmelere, önemli sermaye yatırımı ve personel artışına gerek kalmadan BT altyapılarını ve iş uygulamalarını dağıtmak ve desteklemek için en uygun maliyetli, esnek ve verimli yolu sağlamak üzere tasarlanmış çok yönlü bir platform.',
        cards: [
            {
                id: 21,
                title: 'Tedarik',
                description:
                    'EnterERP Tedarik çözümleri, toplam sahip olma maliyetini düşürmenize, ölçeklenebilirliği etkinleştirmenize, güvenliği ve çalışma süresini iyileştirmenize yardımcı olmak için geliştirildi.',
                image: SearchIcon
            },
            {
                id: 22,
                title: 'Finans',
                description:
                    'Nakit varlıklarınızın tam olarak ne seviyede olduğunu, nereye gittiğini görün ve işletme  için finansman ihtiyacınız olup olmadığını  önceden tespit edin.',
                image: ChartIcon
            },
            {
                id: 23,
                title: 'Satış ve CRM',
                description:
                    'Müşterileriniz nerede olursa olsun; çevrim içi, yüz yüze ve her zaman her yerde satış yapmanızı sağlayan tek platform. Daha hızlı ve daha akıllı bir satış için ihtiyacınız olan araçları edinin.',
                image: SearchIcon
            },
            {
                id: 24,
                title: 'İnsan Kaynakları',
                description:
                    'İnsan Kaynakları ihtiyaçlarınız ister basit ister çok karmaşık olsun; EnterERP, çalışanlarınıza ödeme yapmayı ve vergi beyannamelerini yönetmeyi kolaylaştıracaktır.',
                image: ChartIcon
            }
        ]
    },
    {
        id: 3,
        title: 'Dünyanın ilk gerçek zamanlı ERP’si ile çalışın',
        description:
            'Ofiste değil misiniz? Problem değil. Herhangi bir tarayıcıdan veya cihazdan fatura gönderin, nakit akışını yönetin, giderleri takip edin ve iş birliği yapın. Esnek ve erişilebilir olun.',
        cards: [
            {
                id: 31,
                title: 'Tek Platform',
                description:
                    'Tek platform üzerinden ekiplerinizle anlık birlikte çalışmanın konforunu  test edin. Derlenen sonuçları sizin için EnterERP kolayca değerlendirsin.',
                image: SearchIcon
            },
            {
                id: 32,
                title: 'Güncel',
                description:
                    'Verimli çalışmak için güncel veriler ile karar almanız gerekir. Birbiri ile iletişimsiz disiplinleri unutun, öngörüleri önceden sunan bir EnterERP var.',
                image: ChartIcon
            },
            {
                id: 33,
                title: 'Kontrollü',
                description:
                    'Ekiplere işletmenin büyüme eğrilerinde enerji verimliliklerini düşürmeden çalışma fırsatı verin. Kontrolü alt yapının anahtar çözüm uygulamalarına bırakın.',
                image: SearchIcon
            },
            {
                id: 34,
                title: 'Erişilebilir',
                description:
                    'Sonuç almak için esnekliği elden bırakmadan her yerde her an her şeye ulaşın. Fırsatlara karşı her zaman hazır olun.',
                image: ChartIcon
            }
        ]
    },
    {
        id: 4,
        title: 'İş yüklerinizi güvenli ve kendinden emin bir şekilde ölçeklendirin',
        description:
            "Güvenilir bir temel, sunduğumuz her şeyin merkezinde yer alır. Yerleşik veri güvenliği ve kurtarma varsayılanlarıyla başlayın, ardından ihtiyacınız olan kontrolleri ekleyin. Atlas'ın küresel ve çoklu bulut erişimiyle dünyanın her yerinde uygulamaları çalıştırın.",
        cards: [
            {
                id: 41,
                title: 'Uçtan Uca Güvenlik',
                description:
                    'MongoDB, tüm verileriniz için yerleşik güvenlik kontrolleri sunar. Mevcut güvenlik protokolleriniz ve uyumluluk standartlarınızla bütünleşmek için kurumsal düzeyde özellikleri sağlar.',
                image: SearchIcon
            },
            {
                id: 42,
                title: 'Çoklu Bulut',
                description:
                    'MongoDB, küresel olarak dağıtılan tek çoklu bulut veritabanıdır.',
                image: ChartIcon
            }
        ]
    }
];

const MobileSection = () => {
    const [activeLayer, setActiveLayer] = useState(0);

    const handlers = useSwipeable({
        onSwipedLeft: () => {
            if (activeLayer >= images.length - 1) return;
            setActiveLayer((prev) => prev + 1);
        },
        onSwipedRight: () => {
            if (activeLayer === 0) return;
            setActiveLayer((prev) => prev - 1);
        }
    });

    const t = useTrans();

    return (
        <section className="lg:hidden">
            <CommonPagesInfo
                title="DAHA HIZLI, DAHA GÜVENLİ, DAHA KAPSAMLI"
                description="Geleceğin teknolojisine dokunun. Her ihtiyacınız için bir uygulama..."
            />

            <div className="overflow-hidden bg-primary-900">
                <div
                    {...handlers}
                    className="top-spacer container relative mb-6 aspect-1"
                >
                    {images.map((image, index) => (
                        <div
                            key={index}
                            className={twMerge(
                                'absolute top-6 transition-transform duration-200',
                                activeLayer === index
                                    ? 'translate-x-0'
                                    : 'translate-x-[125%]'
                            )}
                        >
                            <ExtendedImage
                                src={image}
                                alt={t('Katmanlı yapı')}
                                priority
                            />
                        </div>
                    ))}
                </div>

                <div className="container flex items-center justify-center gap-3">
                    {[...new Array(images.length)].map((_, index) => (
                        <span
                            key={index}
                            onClick={() => setActiveLayer(index)}
                            className={twMerge(
                                'inline-block h-6 w-6 rounded-[125%]',
                                index === activeLayer
                                    ? 'bg-primary-400'
                                    : 'bg-accent-200'
                            )}
                        ></span>
                    ))}
                </div>

                <div className="pb-4 pt-6">
                    <Accordion preExpanded={[activeLayer]}>
                        <AccordionItem
                            uuid={activeLayer}
                            className="border-b border-t border-gray-800"
                            dangerouslySetExpanded
                        >
                            <AccordionItemHeading className="container">
                                <AccordionItemButton>
                                    <h2 className="w-10/12 font-means font-light">
                                        {t(accordionData[activeLayer].title)}
                                    </h2>
                                </AccordionItemButton>
                            </AccordionItemHeading>
                            <AccordionItemPanel className="container pb-4">
                                <p className="mb-8 text-lg text-accent-100">
                                    {t(accordionData[activeLayer].description)}
                                </p>

                                <div className="grid gap-8">
                                    {accordionData[activeLayer].cards.map(
                                        (card) => (
                                            <DescCard
                                                key={card.id}
                                                title={card.title}
                                                description={card.description}
                                                image={card.image}
                                            />
                                        )
                                    )}
                                </div>
                            </AccordionItemPanel>
                        </AccordionItem>
                    </Accordion>
                </div>
            </div>
        </section>
    );
};

export default MobileSection;
