import { Tab } from '@headlessui/react';
import { Fragment } from 'react';
import { useTrans } from '@src/hooks';
import { twMerge } from '@src/utils';
import { reviewTabItems } from '../shared/ReviewTabData';
import ReviewCard from './ReviewCard';

const TabSection = () => {
    const t = useTrans();

    return (
        <section className="relative mb-24 bg-primary-50 py-20 lg:py-32 2xl:py-60">
            <div className="container">
                <h2 className="mx-auto mb-8 text-center font-means text-2xl md:text-4xl lg:w-7/12">
                    {t(
                        '<PERSON><PERSON> giriş sürecinin her aşamasında memnun EnterERP müşterilerinden haber alın'
                    )}
                </h2>
                <Tab.Group>
                    <Tab.List className="card-shadow mx-auto flex w-full flex-col flex-wrap items-center gap-4 rounded-4xl border bg-white p-3 md:w-fit md:flex-row">
                        {Object.keys(reviewTabItems).map((tabCategory) => (
                            <Tab as={Fragment} key={tabCategory}>
                                {({ selected }) => (
                                    <button
                                        className={twMerge(
                                            'whitespace-nowrap rounded-full border border-transparent px-8 py-2 transition duration-200 hover:border-primary-200',
                                            selected &&
                                                'bg-primary-200 text-white'
                                        )}
                                    >
                                        {tabCategory}
                                    </button>
                                )}
                            </Tab>
                        ))}
                    </Tab.List>
                    <Tab.Panels as={Fragment}>
                        {Object.values(reviewTabItems).map(
                            (categoryItems, index) => (
                                <Tab.Panel
                                    key={index}
                                    className="mt-4 grid w-full gap-4 lg:grid-cols-2 2xl:grid-cols-3"
                                >
                                    {categoryItems.map((categoryItem) => (
                                        <ReviewCard
                                            key={categoryItem.title}
                                            title={categoryItem.title}
                                            description={
                                                categoryItem.description
                                            }
                                            name={categoryItem.name}
                                            role={categoryItem.role}
                                            size={categoryItem.size}
                                        />
                                    ))}
                                </Tab.Panel>
                            )
                        )}
                    </Tab.Panels>
                </Tab.Group>
            </div>

            <svg
                className="absolute left-0 right-0 top-0 w-full"
                viewBox="0 0 2160 167"
                xmlns="http://www.w3.org/2000/svg"
            >
                <path
                    d="M2159.5 0H-5v148c422.5 68 608.374-77.91 1105-81.499 583.18-4.214 607 159.999 1059.5 49.999V0Z"
                    fill="#fff"
                />
            </svg>
            <svg
                className="absolute -bottom-[1px] left-0 right-0 w-full"
                viewBox="0 0 2160 167"
                xmlns="http://www.w3.org/2000/svg"
            >
                <path
                    d="M-5 166.038h2164.5v-148c-422.5-68-608.37 77.91-1105 81.499C471.318 103.75 447.5-60.462-5 49.538v116.5Z"
                    fill="#fff"
                />
            </svg>
        </section>
    );
};

export default TabSection;
