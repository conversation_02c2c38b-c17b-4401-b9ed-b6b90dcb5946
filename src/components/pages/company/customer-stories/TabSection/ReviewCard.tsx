import { useMemo } from 'react';
import FiveStarIcon from '@public/images/pages/company/customer-stories/icons/FiveStar.svg';
import QuoteIcon from '@public/images/pages/company/customer-stories/icons/Quote.svg';
import { ExtendedImage } from '@src/components/shared';
import { useTrans } from '@src/hooks';

interface ReviewCardProps {
    title: string;
    description: string;
    name: string;
    role: string;
    size: string;
}

const ReviewCard = ({
    title,
    description,
    name,
    role,
    size
}: ReviewCardProps) => {
    const t = useTrans();

    const avatarName = useMemo(() => {
        return name
            .split(/\s/)
            .reduce((response, word) => (response += word.slice(0, 1)), '');
        // eslint-disable-next-line
    }, []);

    return (
        <article className="card-shadow flex w-full flex-col gap-6 rounded-4xl border bg-white p-8 text-center xl:p-12">
            <ExtendedImage
                src={QuoteIcon}
                alt={t('<PERSON><PERSON><PERSON><PERSON> İşareti')}
                containerClassName="h-auto"
            />

            <h3 className="font-means text-2xl">{t(title)}</h3>
            <p className="text-accent-200">{t(description)}</p>

            <div className="mt-auto grid gap-2.5">
                <ExtendedImage
                    src={FiveStarIcon}
                    alt={t('Beş Yıldız')}
                    className="mx-auto"
                />
                <div className="mx-auto flex h-16 w-16 items-center justify-center rounded-full bg-primary-200 text-xl text-white">
                    {avatarName}
                </div>
                <p className="text-lg font-medium">{name}</p>
                <p className="text-sm text-accent-200">{t(role)}</p>
                <p className="text-sm italic text-accent-200">{t(size)}</p>
            </div>
        </article>
    );
};

export default ReviewCard;
