export const reviewTabItems = {
    Kurucular: [
        {
            title: '<PERSON><PERSON><PERSON><PERSON> bü<PERSON><PERSON> güç katıyor',
            description:
                'EnterERP, ihtiyacım olan bilgiyi nerede bulacağımı merak etmek yerine işe odaklanmamı sağlıyor. B<PERSON><PERSON> güçtür ve bilgiye ne kadar çabuk ulaşırsanız, o kadar iyi durumdasınız.',
            name: '<PERSON><PERSON><PERSON>',
            role: '<PERSON><PERSON><PERSON> Or<PERSON>',
            size: '<PERSON><PERSON>yük İşletme (1000 veya daha fazla çalışan)'
        },
        {
            title: 'Rekabet için avantaj sağlıyor',
            description:
                'EnterERP’yi kimseye tavsiye eder miyim? Kesinlikle bizim sektörümüzde tavsiye etmem. Kimsenin bizim şirketin yaptıkları üzerinde rekabet avantajına sahip olmasını istemem.',
            name: '<PERSON><PERSON> Alperen <PERSON>',
            role: '<PERSON><PERSON><PERSON>',
            size: '<PERSON><PERSON><PERSON><PERSON><PERSON> İşletme (50 veya daha az çalışan)'
        },
        {
            title: '<PERSON><PERSON><PERSON><PERSON> büyümesine yardımcı oluyor',
            description:
                '<PERSON><PERSON><PERSON> programımız sık sık çöküyordu ve sunucuyu her iki saatte bir yeniden başlatmamız gerekiyordu. Program zorlanıyordu ve istediğimizi sağlayamıyordu. EnterERP ’de bu artık söz konusu bile olmuyor.',
            name: 'Semih Varlı',
            role: 'Kurucu Ortak',
            size: 'Küçük İşletme (50 veya daha az çalışan)'
        }
    ],
    Yöneticiler: [
        {
            title: 'Anlık olarak analiz yapabiliyorum',
            description:
                'Bu pazarda hayatta kalmanın ve başarılı olmanın tek yolu, operasyonu yönlendirmek, kârlılığı sürdürmek, müşteri memnuniyetini ve kaliteyi artırmak için raporlarda görünürlüğe sahip olmak gerekir. Bunu EnterERP olmadan yapamazdık.',
            name: 'Muhammet Güngören',
            role: 'Genel Müdür',
            size: 'Büyük İşletme (1000 veya daha fazla çalışan)'
        },
        {
            title: 'İhtiyacımız olan her şey onda var',
            description:
                'EnterERP’yi gördüğümüzde tamamen vites değiştirdik. EnterERP ’yi kullanım kolaylığı nedeniyle seçtik. Maliyet başka bir faktördü. EnterERP iş modelimiz için çok daha mantıklıydı çünkü kullanıcılarımızın çoğu diğer çözümleri anlayamadı ve isteklerimizi karşılamıyordu.',
            name: 'Buğra Hoş',
            role: 'Satış Müdürü',
            size: 'Orta İşletme (50 veya daha fazla çalışan)'
        },
        {
            title: 'Projelerimiz EnterERP’ye emanet',
            description:
                'Ülke çapında daha büyük projelerle büyüdükçe EnterERP, süreci daha verimli yönetmemizi sağlıyor. EnterERP ve sağladığımız bilgiler sayesinde, müşteriler bizimle yüksek düzeyde rahatlık yaşıyor.',
            name: 'Erkan Elmastaş',
            role: 'Finans Müdürü',
            size: 'Orta İşletme (50 veya daha fazla çalışan)'
        }
    ],
    Personeller: [
        {
            title: 'Süreçleri hızla çözümleyebiliyorum',
            description:
                'Çoklu şube yönetimi kullandığımız için operasyon anlamında muhasebe ekibini kontrol etmek ve oluşturulan verilerin doğruluğunu analiz etmek çok zor zor bir süreçti. EnterERP ile artık sürecin doğruluğunu bir akış içerisinde yönetebiliyorum.',
            name: 'Hasan Tatlı',
            role: 'Muhasebe Şefi',
            size: 'Büyük İşletme (1000 veya daha fazla çalışan)'
        },
        {
            title: 'Saha satışı hiç bu kadar kolay olmadı',
            description:
                'Saha satış ve servis operasyonları için oluşturduğum organizasyonları kolaylıkla yönetebiliyor ve görevlendirmelerini iş takvimlerine göre planlayabiliyorum. Maliyetlerimizi düşürerek verimliliği en üst seviyeye taşıdık.',
            name: 'Ahmet Tekinoğlu',
            role: 'Bölge Satış Sorumlusu',
            size: 'Orta İşletme (50 veya daha fazla çalışan)'
        },
        {
            title: 'Tedarik yönetimini hızlandırdık',
            description:
                'Yurt içi ve yurt dışı ürün tedarikinin yoğun olduğu bir süreç yürütmeye çalışıyoruz. Bu süreci yürütürken en çok problem yaşadığımız durum gelecek olan ürün stokunu rezerve edememekti. EnterERP gelişmiş rezervasyon sistemi ile tedarik için ön yapılandırma yapmanız yeterli.',
            name: 'Aslıhan Sert',
            role: 'Tedarik Yönetimi Sorumlusu',
            size: 'Küçük İşletme (50 veya daha az çalışan)'
        }
    ]
};
