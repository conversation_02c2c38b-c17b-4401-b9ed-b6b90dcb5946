import { useTrans } from '@src/hooks';
import CustomerStoryCard from './CustomerStoryCard';
import { customerStoriesData } from './shared/CustomerStoriesData';
import TabSection from './TabSection';

const ContentSection = () => {
    const t = useTrans();

    return (
        <>
            {customerStoriesData.map((customerStory) => (
                <div
                    key={customerStory.title}
                    className="container relative mb-24"
                >
                    <CustomerStoryCard
                        name={customerStory.name}
                        description={customerStory.description}
                        location={customerStory.location}
                        role={customerStory.role}
                        title={customerStory.title}
                        src={customerStory.src}
                        tags={customerStory.tags}
                    />
                </div>
            ))}

            <h2 className="container mb-12 text-center font-means text-3xl sm:text-5xl">
                {t('+200 Mutlu Müşteri')}
            </h2>

            <TabSection />
        </>
    );
};

export default ContentSection;
