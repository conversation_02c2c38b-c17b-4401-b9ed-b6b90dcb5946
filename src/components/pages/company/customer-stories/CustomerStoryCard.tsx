import { StaticImageData } from 'next/image';
import { ExtendedImage } from '@src/components/shared';
import { useTrans } from '@src/hooks';

interface CustomerStoryCardProps {
    title: string;
    description: string;
    src: string | StaticImageData;
    role: string;
    name: string;
    tags: string[];
    location: string;
}

const CustomerStoryCard = ({
    name,
    description,
    location,
    role,
    src,
    tags,
    title
}: CustomerStoryCardProps) => {
    const t = useTrans();

    return (
        <article className="card-shadow ml-auto rounded-4xl border bg-white p-6 max-xl:mt-36 lg:w-10/12 lg:p-10">
            <div className="mt-32 grid gap-6 lg:!mt-0 lg:ml-20 lg:gap-10">
                <h2 className="font-means text-3xl">{t(title)}</h2>
                <p className="text-lg text-accent-200 lg:text-xl">
                    &ldquo;{t(description)}&rdquo;
                </p>
                <div className="grid gap-1">
                    <p className="text-sm lg:text-lg">{t(name)}</p>
                    <p className="text-sm text-primary-200 lg:text-lg">
                        {t(role)}
                    </p>
                </div>
                <div className="flex flex-wrap items-center justify-end gap-2">
                    {tags.map((tag) => (
                        <p
                            className="w-fit whitespace-nowrap rounded-full border px-4 py-2.5 text-sm font-medium leading-6"
                            key={tag}
                        >
                            <span className="text-primary-200">#</span>
                            {t(tag)}
                        </p>
                    ))}
                    <div className="flex items-center gap-2 rounded-full bg-primary-200 px-6 py-2.5 text-white">
                        <svg
                            viewBox="0 0 15 20"
                            xmlns="http://www.w3.org/2000/svg"
                            className="h-5 w-5 fill-white"
                        >
                            <path d="M5.70337 9.18318C5.62453 9.0652 5.55791 8.94032 5.50409 8.81049C5.396 8.54974 5.33957 8.269 5.33957 7.984H7.49485L7.49486 7.98526L7.49486 7.98604V7.98604L7.49486 7.98857V7.98857L7.49487 7.99136L7.49795 10.1423C7.21593 10.1423 6.93523 10.0871 6.67213 9.97812C6.54103 9.92382 6.41625 9.85701 6.29937 9.77897C6.06462 9.62222 5.86173 9.42016 5.70337 9.18318ZM7.49869 10.1423L7.49791 7.984L9.65625 7.984C9.65625 8.12454 9.64253 8.26541 9.61487 8.40459C9.53168 8.82333 9.32618 9.20798 9.02436 9.5099C8.72255 9.81182 8.33797 10.0175 7.91927 10.1008C7.7801 10.1285 7.63923 10.1423 7.49869 10.1423ZM9.65625 7.98326L7.50527 7.98095L9.49173 7.1575C9.60078 7.42055 9.65615 7.70124 9.65625 7.98326ZM9.49173 7.1575L7.50527 7.98095L7.50248 7.98094H7.50248L7.49996 7.98094L8.69646 6.18903C8.93348 6.34729 9.13562 6.55012 9.29245 6.78481C9.37054 6.90166 9.4374 7.02641 9.49173 7.1575ZM8.69646 6.18903L7.49995 7.98094L7.49918 7.98094L7.49791 7.98094L7.49714 5.82566C7.78215 5.82556 8.0629 5.88188 8.32369 5.98988C8.45354 6.04365 8.57844 6.11023 8.69646 6.18903ZM7.49483 7.96852L7.49485 7.98094L7.48244 7.98092L5.33958 7.97862C5.33973 7.83815 5.35359 7.69912 5.38042 7.5633C5.46259 7.1473 5.66642 6.76146 5.97057 6.4572C6.27472 6.15294 6.66048 5.94898 7.07645 5.86665C7.21226 5.83978 7.35129 5.82587 7.49176 5.82566L7.49483 7.96852Z" />
                            <path d="M7.50064 19.4724C7.55121 19.4724 7.60129 19.4624 7.64797 19.4429C7.69465 19.4235 7.73702 19.3949 7.77262 19.359C8.06785 19.0615 15 12.0259 15 7.98395C14.9884 6.00241 14.1932 4.10597 12.7879 2.70887C11.3826 1.31178 9.48158 0.527588 7.5 0.527588C5.51842 0.527588 3.61737 1.31178 2.2121 2.70887C0.806837 4.10597 0.011563 6.00241 0 7.98395C0 12.0259 6.93292 19.0615 7.22738 19.359C7.26313 19.3951 7.30571 19.4237 7.35262 19.4432C7.39953 19.4626 7.44985 19.4726 7.50064 19.4724ZM7.50064 1.25023C9.28567 1.25273 10.9969 1.96301 12.259 3.22532C13.5211 4.48762 14.2311 6.19892 14.2333 7.98395C14.2333 11.2863 8.79135 17.1849 7.49962 18.5382C6.20788 17.1847 0.765643 11.2853 0.765643 7.98395C0.767873 6.19852 1.47821 4.48688 2.74082 3.2245C4.00343 1.96213 5.71521 1.25212 7.50064 1.25023Z" />
                        </svg>

                        <p>{t(location)}</p>
                    </div>
                </div>
                <ExtendedImage
                    src={src}
                    alt={t(title)}
                    containerClassName="absolute left-6 -top-24 w-7/12 max-w-[240px] rounded-4xl border card-shadow lg:left-0 lg:w-3/12 lg:max-w-xs h-auto aspect-1 flex items-center bg-white px-12 lg:-top-16"
                />
            </div>
        </article>
    );
};

export default CustomerStoryCard;
