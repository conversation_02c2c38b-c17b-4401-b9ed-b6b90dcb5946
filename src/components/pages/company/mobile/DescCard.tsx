import { StaticImageData } from 'next/image';
import { ExtendedImage } from '@src/components/shared';
import { useTrans } from '@src/hooks';

interface DescCardProps {
    title: string;
    description: string;
    image: StaticImageData | string;
}

const DescCard = ({ title, description, image }: DescCardProps) => {
    const t = useTrans();

    return (
        <article className="grid gap-6 rounded-2xl bg-white p-8">
            <div>
                <ExtendedImage src={image} alt={t(title)} />
            </div>
            <h3 className="font-means text-lg">{t(title)}</h3>
            <p className="text-sm">{t(description)}</p>
        </article>
    );
};

export default DescCard;
