import BuildIcon from '@public/images/icons/Build.svg';
import ScaleIcon from '@public/images/icons/Scale.svg';
import SecurityIcon from '@public/images/icons/Security.svg';
import { InfoCard } from '@src/components/shared';

const SpecSection = () => {
    return (
        <>
            <section className="spacer container">
                <div className="grid place-items-center gap-12 md:grid-cols-2 lg:gap-36 xl:grid-cols-4">
                    <InfoCard
                        title="Olağanüstü Kullanılabilirlik"
                        description="Kullanıcının mümkün olduğu kadar çok daha fazla eylemi daha kolay, hızlı ve işlevsel yapmasını sağlayan kullanılabilirliği keşfedin."
                        src={BuildIcon}
                    />
                    <InfoCard
                        title="Modern Tasarım"
                        description="Güncel tasarım trendlerini yenilikçi yazılım teknolojileri ile en iyi şekilde birleştiren EnterERP’nin mobil uygulamalarını deneyimleyin."
                        src={ScaleIcon}
                    />
                    <InfoCard
                        title="Özelleştirilebilir Kullanım"
                        description="Her kullanıcının isteğine göre şekillenen modüler yapısı ile mobil kullanımı kendinize özel olarak tasarlayın."
                        src={SecurityIcon}
                    />
                    <InfoCard
                        title="Hızlı Verimlilik"
                        description="Hızlı, kolay, doğru. Ekibinizin tahminlerini gerçek zamanlı olarak görün ve karar alma süreçlerinde onlara anında yardımcı olun."
                        src={SecurityIcon}
                    />
                </div>
            </section>
        </>
    );
};

export default SpecSection;
