import Phone from '@public/images/pages/company/mobile/Phone.png';
import Search from '@public/images/pages/company/mobile/Search.svg';
import { CommonPagesInfo, ExtendedImage } from '@src/components/shared';
import { useTrans } from '@src/hooks';
import DescCard from './DescCard';

const ContentSection = () => {
    const t = useTrans();

    return (
        <>
            <CommonPagesInfo
                title="DAHA AKILLI, DAHA GELİŞMİŞ, DAHA YENİLİKÇİ, DAHA ÇEVİK"
                description="EnterERP verimliliği artırır ve süreç yönetimini basitleştirir. Günlük yaşam yolculuğu sırasında modern ve sezgisel mobil uygulama tasarımlarının, kullanıcıların zamanlarından ve enerjilerinden nasıl tasarruf etmelerini sağladığını keşfedin."
            />

            <section className="relative bg-primary-50">
                <svg
                    width="696"
                    height="686"
                    viewBox="0 0 696 686"
                    xmlns="http://www.w3.org/2000/svg"
                    className="absolute left-0 top-0 hidden lg:block"
                >
                    <path
                        d="M695.239 0H-10L-9.99976 685.342C-10.1619 559.182 94.612 456.894 223.838 456.894H227.239C356.465 456.894 461.239 354.607 461.239 228.448C461.239 102.288 566.013 0 695.239 0Z"
                        fill="white"
                    />
                </svg>

                <div className="spacer container flex flex-col items-center justify-between gap-12 lg:flex-row xl:gap-20">
                    <div className="w-full xl:w-1/3">
                        <ExtendedImage
                            src={Phone}
                            alt={t('EnterERP mobil uygulama')}
                            className="mx-auto"
                            quality={100}
                        />
                    </div>
                    <div className="grid gap-12 xl:w-8/12">
                        <h2 className="text-center font-means text-5xl">
                            {t('Mobil Yönetim')}
                        </h2>
                        <p className="text-center text-xl text-accent-200">
                            {t(
                                'Akıllı telefonlar teknolojideki sürekli gelişmelerle birlikte işletmelerin işlerini oluşturmalarına, yönetmelerine ve kontrol etmelerine yardımcı oluyor. EnterApp, mobil cihazınızı taşınabilir bir ofise dönüştürür. Nerede olursanız olun aramaları kaydedebilir, sıcak müşteri adaylarına ve iş fırsatlarına yanıt verebilir veya panoları kontrol edebilirsiniz.'
                            )}
                        </p>
                        <div className="grid gap-8 md:grid-cols-2 xl:grid-cols-3">
                            <DescCard
                                title="Satış ve CRM"
                                description="Daha hızlı ve daha akıllı satış yapmak, müşteri ilişkilerinizi en iyi şekilde yönetmek için ihtiyacınız olan uygulama araçlarını EnterApp mobil uygulaması ile tamamen ücretsiz olarak edinin."
                                image={Search}
                            />
                            <DescCard
                                title="Saha Servis"
                                description="Enter Service mobil uygulaması sayesinde sahadaki ekiplerinizi en verimli şekilde planlayarak kusursuz yönetin. Müşteri ihtiyaçlarına hızlı dönüşler sağlayarak memnuniyeti en üst seviyede tutun."
                                image={Search}
                            />
                            <DescCard
                                title="Depo ve Stok"
                                description="Tedarik planlamanın en sorunlu bölümü uygulama birimi ile olan iletişimdir. Enter Inventory ile depo ve lojistik birimlerinizle olan iletişimi güçlendirerek sorunsuz bir tedarik süreç yönetimi deneyimi yaşayın."
                                image={Search}
                            />
                        </div>
                    </div>
                </div>
            </section>
        </>
    );
};

export default ContentSection;
