import { useState } from 'react';
import Performance from '@public/images/pages/company/why-entererp/Performance.svg';
import PhoneChart from '@public/images/pages/company/why-entererp/PhoneChart.svg';
import Security from '@public/images/pages/company/why-entererp/Security.svg';
import SpeedChart from '@public/images/pages/company/why-entererp/SpeedChart.svg';
import { CommonPagesSection, SectionTabs } from '@src/components/shared';
import { TabEnum } from '@src/interfaces';

const ContentSection = () => {
    const [activeTab, setActiveTab] = useState<TabEnum>('first');

    const intersectionHandler = (activeTab: TabEnum) => {
        setActiveTab(activeTab);
    };

    return (
        <>
            <SectionTabs
                intersectionHandler={intersectionHandler}
                activeTab={activeTab}
                sections={[
                    { id: 'first', title: 'Ultra Hızlı' },
                    { id: 'second', title: '<PERSON><PERSON><PERSON><PERSON>aman' },
                    { id: 'third', title: '<PERSON><PERSON><PERSON><PERSON>' },
                    { id: 'fourth', title: 'Düşük Maliyet' }
                ]}
            />

            <section className="why-entererp-bg">
                <CommonPagesSection
                    order="0"
                    title="Bulutların üstünde hız kesmeyen bir teknoloji"
                    description="EnterERP, sınırlarını zorlamak isteyen profesyoneller için özel olarak tasarlandı. Olağanüstü performans, yapılandırma ve genişletme seçenekleriyle tasarlanmış yepyeni bir sistem. “Dünyadaki en yakın rakibine göre 10 kat daha hızlı “."
                    src={SpeedChart}
                    id="first"
                    info=""
                    intersectionHandler={intersectionHandler}
                />

                <CommonPagesSection
                    order="1"
                    title="Gerçek zamanlı veri senkronizasyonu"
                    description="En yeni teknolojileri kullanan EnterERP, gerçek zamanlı altyapısı sayesinde istenilen sonuçları sizlere anlık olarak sunar. Tek bir gösterge panosundan envanteri yönetin, ödemeleri izleyin ve gerçek zamanlı iş öngörülerini görüntüleyin. İhtiyacınız olan tüm araçları oluşturduk, artık yalnızca işinizi büyütmeye odaklanabilirsiniz."
                    src={PhoneChart}
                    id="second"
                    info=""
                    intersectionHandler={intersectionHandler}
                />

                <CommonPagesSection
                    order="0"
                    title="Üst düzey veri güvenliği"
                    description="Geliştirdiğimiz yazılımlarda ileri seviye güvenlik metotları ile önlem alıyoruz. Lokal çözümlere kıyasla üst düzey güvenliğiyle sizin için özel olarak korunan bir kale."
                    src={Security}
                    id="third"
                    info=""
                    intersectionHandler={intersectionHandler}
                />

                <CommonPagesSection
                    order="1"
                    title="Yüksek performans düşük maliyet"
                    description="Güvenlik, soğutma, mesai ve bakım maliyetlerini ortadan kaldırıp sadece işinizi büyütmeye odaklanın. EnterERP tüm rakiplerine göre minimum 5 kata kadar daha düşük özellikteki donanımlarda çalışabilir. Zorlu koşullarda büyük işler başarmak isteyenler için tasarlandı."
                    src={Performance}
                    id="fourth"
                    info=""
                    intersectionHandler={intersectionHandler}
                />
            </section>
        </>
    );
};

export default ContentSection;
