import InfoIcon from '@public/images/shared/Info.svg';
import { InfoCard } from '@src/components/shared';

const SpecSection = () => {
    return (
        <section className="spacer container">
            <div className="grid place-items-start gap-12 md:grid-cols-2 lg:gap-36 xl:grid-cols-4">
                <InfoCard
                    title="Kullanıcı Dostu"
                    description="İşletmeniz için en iyi kullanıcı deneyimini sağlıyoruz."
                    src={InfoIcon}
                />
                <InfoCard
                    title="Modüler"
                    description="İhtiyaçlarınıza uygun modüler çözümler."
                    src={InfoIcon}
                />
                <InfoCard
                    title="En Moderni"
                    description="Tasarım mimarisi ile yeniliği açık bir yaklaşımı görün."
                    src={InfoIcon}
                />
                <InfoCard
                    title="Tam Entegre"
                    description="Üçüncü taraf ç<PERSON>le tam entegrasyon sağlar."
                    src={InfoIcon}
                />
                <InfoCard
                    title="Mobil"
                    description="Tüm süreçlerinizi EnterAPP mobil ile cebinizden takip edin."
                    src={InfoIcon}
                />
                <InfoCard
                    title="Esnek"
                    description="Sektörünüze uyarlanabilen esnek yapısı ile işinizi büyütün."
                    src={InfoIcon}
                />
                <InfoCard
                    title="Duyarlı"
                    description="EnterERP ekibi sizin gibi düşünürek işiniz için en iyi desteği sunar."
                    src={InfoIcon}
                />
                <InfoCard
                    title="Güncel"
                    description="Değişime hızlı uyum sağlamanız için her zaman güncel kalıyoruz."
                    src={InfoIcon}
                />
            </div>
        </section>
    );
};

export default SpecSection;
