import CareerBgImage from '@public/images/pages/corporate/career/CareerBg.png';
import CircleCheckIcon from '@public/images/pages/corporate/career/icons/CircleCheck.svg';
import CommunityInfoIcon from '@public/images/pages/corporate/career/icons/CommunityInfo.svg';
import CommunityWorldIcon from '@public/images/pages/corporate/career/icons/CommunityWorld.svg';
import ComputerLearnIcon from '@public/images/pages/corporate/career/icons/ComputerLearn.svg';
import EyeIcon from '@public/images/pages/corporate/career/icons/Eye.svg';
import FaqIcon from '@public/images/pages/corporate/career/icons/Faq.svg';
import { ExtendedImage, InfoCard } from '@src/components/shared';
import { useTrans } from '@src/hooks';
import DescriptionSection from '../shared/DescriptionSection';

const ContentSection = () => {
    const t = useTrans();

    return (
        <div className="bottom-spacer">
            <DescriptionSection
                info="ENTERERP DEĞERLER"
                title="Değerler"
                description="Şirket değerlerimiz, EnterERP'nin bir ekip olarak birlikte çalışmasına yardımcı olan yol gösterici ilkeler ve temel inançlar kümesidir. Bizim savunduğumuz değerler işleri nasıl ve neden yaptığımızı yönlendiriyor. Bunlar, kendimizde ve meslektaşlarımızda aradığımız temel ilkelerdir."
                id="first"
            >
                <div className="grid gap-10 md:grid-cols-2 2xl:grid-cols-3">
                    <InfoCard
                        title="Müşteriye Takıntılı Olun"
                        description="Müşterilerimizi nasıl başarılı kılacağımız konusunda takıntılıyız. Verdiğimiz her karar müşterilerimizin başarısına hizmet etmelidir çünkü onların başarısı bizimkiyle el ele gelir."
                        src={FaqIcon}
                        isCard
                    />
                    <InfoCard
                        title="Cesurca Konuşun ve Hareket Edin"
                        description="Başarısız olmaktan değil, denememekten daha çok korkuyoruz. Hesaplanmış riskler alan ve deney yapan ekip üyelerine hayranız. Statükoya meydan okumaktan çekinmiyoruz."
                        src={CommunityInfoIcon}
                        isCard
                    />
                    <InfoCard
                        title="Sorumluluğu Sahipleniyoruz"
                        description="Bunun BİZİM şirketimiz olduğunu kabul ediyoruz. Hiçbir sorunu asla 'başkasının sorunu' olarak görmüyoruz. Bir boşluk görürsek, onu ele almak için proaktif davranıyoruz. Mükemmelliği savunuyoruz. Birbirimize ve kendimize karşı sorumluyuz."
                        src={CircleCheckIcon}
                        isCard
                    />
                    <InfoCard
                        title="Açgözlülükle Öğrenin"
                        description="Her zaman meraklıyız, öğrenmek ve kendimizi geliştirmek için fırsatlar arıyoruz. Büyüme zihniyetini benimsiyoruz ve kendimizde ve işimizde gördüğümüz boşluklardan motive oluyoruz."
                        src={ComputerLearnIcon}
                        isCard
                    />
                    <InfoCard
                        title="Odaklanma ve Aciliyetle Hareket Edin"
                        description="En büyük etkiye sahip olacak birkaç şeyi belirliyor ve bunları başarmak için hızla ilerliyoruz."
                        src={EyeIcon}
                        isCard
                    />
                    <InfoCard
                        title="Hepimiz biriz"
                        description="Tek takım olarak kazanır veya kaybederiz. İnsanların etrafında toplanır ve büyüme yolunda birbirimize yatırım yaparız: ister galibiyetleri kutlayın, ister yardım edin veya yapıcı geribildirim verin."
                        src={CommunityWorldIcon}
                        isCard
                    />
                </div>
            </DescriptionSection>

            <article className="card-shadow container grid place-items-center gap-6 rounded-4xl bg-primary-200 text-white max-lg:py-12 lg:grid-cols-2">
                <div className="grid gap-6 max-lg:px-6 lg:pl-12">
                    <h2 className="font-means text-3xl">
                        {t(
                            'Bize katılın ve yeni nesil iş dünyasına güç katın.'
                        )}
                    </h2>
                    <p className="text-lg">
                        {t(
                            'Başvuru için CV’nizi <EMAIL> adresine mail gönderin.'
                        )}
                    </p>
                </div>

                <ExtendedImage
                    src={CareerBgImage}
                    alt={t('Kariyer arka plan')}
                    className="mx-auto"
                />
            </article>
        </div>
    );
};

export default ContentSection;
