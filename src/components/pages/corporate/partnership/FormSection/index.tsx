import Form from './Form';
import FormPartial from './FormPartial';

const FormSection = () => {
    return (
        <section className="relative bg-primary-50">
            <svg
                width="605"
                height="750"
                viewBox="0 0 605 948"
                xmlns="http://www.w3.org/2000/svg"
                className="absolute bottom-0 left-0 hidden xl:block"
            >
                <path
                    d="M605 947.987H-345V0C-344.892 104.823 -259.685 189.694 -154.837 189.302H-153.393C-48.4756 188.908 36.7695 273.892 36.7695 378.809V379.465C36.7695 484.121 121.621 568.973 226.277 568.973C330.932 568.973 415.785 653.826 415.785 758.481C415.785 863.038 500.478 947.834 605 947.987Z"
                    fill="white"
                />
            </svg>

            <div className="spacer container my-10 grid gap-10 xl:grid-cols-2">
                <FormPartial />
                <Form />
            </div>
        </section>
    );
};

export default FormSection;
