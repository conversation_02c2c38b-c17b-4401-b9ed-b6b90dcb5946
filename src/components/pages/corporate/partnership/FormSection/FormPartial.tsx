import { useTrans } from '@src/hooks';

const ContentItem = ({ title }: { title: string }) => {
    const t = useTrans();

    return (
        <li className="flex items-center gap-6">
            <span className="inline-flex h-16 w-16 items-center justify-center rounded-full bg-white">
                <svg
                    width="32"
                    height="32"
                    viewBox="0 0 32 26"
                    xmlns="http://www.w3.org/2000/svg"
                >
                    <path
                        d="M30.4553 6.57369L14.7088 22.1069L12.0226 24.7567C11.9299 24.8481 11.8373 24.9394 11.7447 25.0308C11.6521 25.1222 11.5594 25.1222 11.4668 25.2135C11.3742 25.2135 11.3742 25.2135 11.2816 25.3049C10.6332 25.4877 9.89219 25.3049 9.42906 24.8481L6.65022 22.1069L1.55575 17.0815C0.81475 16.3505 0.81475 15.1627 1.55575 14.523L4.14934 11.9646C4.89034 11.2336 6.09447 11.2336 6.74284 11.9646L10.4479 15.6195C10.5406 15.7109 10.8184 15.7109 10.9111 15.6195L25.1756 1.54823C25.9166 0.817258 27.1207 0.817258 27.7691 1.54823L30.3627 4.10666C31.1963 4.74623 31.1963 5.84273 30.4553 6.57369Z"
                        fill="#0048FF"
                        stroke="#001E2B"
                        strokeMiterlimit="10"
                    />
                </svg>
            </span>
            <p className="flex-1 text-lg">{t(title)}</p>
        </li>
    );
};

const FormPartial = () => {
    const t = useTrans();

    return (
        <div className="grid place-content-start gap-8">
            <h2 className="font-means text-2xl lg:text-5xl">
                {t('Nasıl İş Ortağı olunur?')}
            </h2>
            <ul className="grid gap-8">
                <ContentItem title="Uygun olan  bir iş ortağı planını Belirleyin" />
                <ContentItem title="İş Ortağı başvuru formunu doldurun" />
                <ContentItem title="Ekiplerimiz sizinle iletişime geçsin" />
                <ContentItem title="Hemen kazanmaya başlayın!" />
            </ul>
        </div>
    );
};

export default FormPartial;
