import dynamic from 'next/dynamic';
import { FormProvider, useForm } from 'react-hook-form';
import {
    Input,
    Phone,
    PrivacyManifest,
    Select,
    SubmitPartial,
    TermsManifest,
    Textarea
} from '@src/components/shared/Form';
import {
    countries,
    departments,
    sectors
} from '@src/components/shared/Form/Static';
import { useSubmit, useTrans } from '@src/hooks';
import { FormValues } from '@src/interfaces';

const Toast = dynamic(() => import('@src/components/shared/Form/Toast'));

const Form = () => {
    const t = useTrans();

    const methods = useForm<FormValues>({ mode: 'onChange' });

    const { isSubmitting, status, showToast, onSubmit } = useSubmit({
        methods
    });

    return (
        <div className="card-shadow flex w-fit flex-col items-start gap-8 rounded-4xl border bg-white p-6 md:p-10">
            <div className="grid gap-8">
                <h2 className="text-center font-means text-2xl lg:text-5xl">
                    {t('İş Ortağı Başvuru Formu')}
                </h2>
                <p className="text-center text-lg text-accent-200">
                    {t(
                        'EnterERP hakkında daha fazla bilgi almak istiyorsanız formu doldurabilir veya bu numaradan bizi arayabilirsiniz. 0 850 582 00 35'
                    )}
                </p>
            </div>

            <Toast
                showToast={showToast}
                status={status}
                successMessage="Kaydınız başarıyla gerçekleşti."
                errorMessage="Bir şeyler ters gitti. Lütfen daha sonra tekrar deneyin."
            />

            <FormProvider {...methods}>
                <form
                    className="mx-auto flex flex-col justify-between gap-7"
                    onSubmit={methods.handleSubmit(onSubmit)}
                >
                    <fieldset
                        disabled={methods.formState.isSubmitting}
                        className="grid w-full gap-7 lg:grid-cols-2"
                    >
                        <Input name="name" label="Ad *" required />
                        <Input name="surname" label="Soyad *" required />
                        <Select
                            name="country"
                            label="Ülke/Bölge *"
                            defaultValue="Turkey"
                        >
                            {countries.map((country) => (
                                <option
                                    value={country.country}
                                    key={country.iso}
                                >
                                    {country.country}
                                </option>
                            ))}
                        </Select>
                        <Phone />
                        <Input name="mail" label="E-Posta (Opsiyonel)" />
                        <Input name="company" label="İşletme Adı *" required />
                        <Select name="sector" label="Sektör">
                            {sectors.map((sector) => (
                                <option value={sector} key={sector}>
                                    {sector}
                                </option>
                            ))}
                        </Select>
                        <Select name="department" label="Departman">
                            {departments.map((department) => (
                                <option value={department} key={department}>
                                    {department}
                                </option>
                            ))}
                        </Select>
                    </fieldset>

                    <Textarea
                        name="description"
                        rows={5}
                        placeholder={t(
                            'Size nasıl yardımcı olabileceğimiz hakkında daha detaylı bilgi verin.'
                        )}
                    />

                    <PrivacyManifest />

                    <TermsManifest />

                    <SubmitPartial
                        isSubmitting={isSubmitting}
                        title="Gönder"
                        type="submit"
                    />
                </form>
            </FormProvider>
        </div>
    );
};

export default Form;
