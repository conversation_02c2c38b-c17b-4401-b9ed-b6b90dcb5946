import { useState } from 'react';
import FirstImage from '@public/images/pages/corporate/partnership/First.png';
import LayerOneIcon from '@public/images/pages/corporate/partnership/icons/LayerOne.svg';
import LayerThreeIcon from '@public/images/pages/corporate/partnership/icons/LayerThree.svg';
import LayerTwoIcon from '@public/images/pages/corporate/partnership/icons/LayerTwo.svg';
import SecondImage from '@public/images/pages/corporate/partnership/Second.png';
import ThirdImage from '@public/images/pages/corporate/partnership/Third.png';
import {
    CommonPagesSection,
    InfoCard,
    SectionTabs
} from '@src/components/shared';
import { TabEnum } from '@src/interfaces';
import DescriptionSection from '../shared/DescriptionSection';
import FormSection from './FormSection';

const ContentSection = () => {
    const [activeTab, setActiveTab] = useState<TabEnum>('first');

    const intersectionHandler = (activeTab: TabEnum) => {
        setActiveTab(activeTab);
    };

    return (
        <>
            <SectionTabs
                intersectionHandler={intersectionHandler}
                activeTab={activeTab}
                sections={[
                    { id: 'first', title: 'Çözüm Ortağı' },
                    { id: 'second', title: 'Teknoloji Ortağı' },
                    { id: 'third', title: 'Satış Ortağı' },
                    { id: 'fourth', title: 'Başvuru Formu' }
                ]}
            />

            <DescriptionSection
                info="İŞ ORTAKLIĞI"
                title="EnterERP İş Ortaklığı"
                description="İşinizi daha hızlı büyütmek istiyorsanız EnterERP İş Ortaklığı programlarımızı keşfedin, sizin için doğru olanı bulun ve birlikte daha iyi büyümeye başlayalım."
                id="first"
                intersectionHandler={intersectionHandler}
            >
                <div className="grid gap-10 md:grid-cols-2 2xl:grid-cols-3">
                    <InfoCard
                        title="Çözüm Ortağı Programı"
                        description="EnterERP'yi satmak ve danışmanlık, uygulama ve geçiş hizmetleri sunmak isteyen satıcılar, danışmanlık şirketleri ve pazar yeri siteleri için"
                        src={LayerOneIcon}
                        isCard
                    />
                    <InfoCard
                        title="Teknoloji Ortağı Programı"
                        description="EnterERP’nin entegrasyon ekosistemine bir platform, araç veya yazılım ile katılmak isteyen işletmeler"
                        src={LayerTwoIcon}
                        isCard
                    />
                    <InfoCard
                        title="Satış Ortaklığı Programı"
                        description="Hedef kitlesini doğrudan EnterERP satın almaya yönlendirerek komisyon kazanmak isteyen web sitesi sahipleri, fenomenler, risk sermayedarları, kurumlar veya diğer işletmeler için"
                        src={LayerThreeIcon}
                        isCard
                    />
                </div>
            </DescriptionSection>

            <CommonPagesSection
                order="1"
                title="Çözüm Sağlayıcılar Ortağı"
                description="Çözüm ortaklığı programımız satış danışmanlığı, işe alım, uygulama ve kişiselleştirme gibi profesyonel hizmetler ile birlikte EnterERP’yi pazarlayıp satmak isteyen çözüm ortaklarına yöneliktir. Çözüm ortakları, ayrıca satış, faturalama (yalnızca Elite kademesi), teknik destek ve müşteri yönetimi sağlayarak müşteri ilişkilerini kontrol eder. Programa katılmak için sağlanması gereken belli teknik onaylar ve taahhütler vardır. İki ortaklık kademesi sunuyoruz: Elite ve Premier. Programa başvurduğunuzda ekip üyelerimizden birisi sizinle görüşerek işinize en uygun kademeyi belirler."
                src={FirstImage}
                id="second"
                info="HIZLANIN VE BÜYÜYÜN"
                intersectionHandler={intersectionHandler}
            />

            <CommonPagesSection
                order="0"
                title="Teknoloji iş ortakları"
                description="Bu iş ortaklığı, EnterERP ile entegre etmek istedikleri platform, araç veya yazılım sunan işletmeler içindir. Kuruluşlara, EnterERP ’yi teknoloji araçlarına ekleyerek satış sürecini nasıl geliştirebileceklerini, verimli adımları nasıl kolaylaştıracaklarını ve gelirlerini nasıl artıracaklarını gösterin. İşle ilgili sonuçlar ortaya koymak için ortak çözümler oluşturun!"
                src={SecondImage}
                id="third"
                info="TİCARİ SONUÇLAR ELDE EDİN"
                intersectionHandler={intersectionHandler}
            />

            <CommonPagesSection
                order="1"
                title="Satış ortaklığı"
                description="Bu program web sitesi sahipleri, mentorlar, bağımsız danışmanlar, risk sermayedarları ve kuluçka merkezleri, ticari gruplar ve dernekler veya hedef kitlesine doğrudan EnterERP planı satın almaya yönlendirerek komisyon kazanmak isteyen kurumlar için hazırlanmıştır. %30 ‘a varan kazanç!"
                src={ThirdImage}
                id="fourth"
                info="BÜYÜYÜN VE GELİŞİN"
                intersectionHandler={intersectionHandler}
            />

            <FormSection />
        </>
    );
};

export default ContentSection;
