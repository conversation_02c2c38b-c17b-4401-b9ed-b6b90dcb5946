import { ReactNode, useRef } from 'react';
import { useSectionIntersect, useTrans } from '@src/hooks';
import { TabEnum } from '@src/interfaces';

interface DescriptionSectionProps {
    title: string;
    description: string;
    id: TabEnum;
    info: string | number;
    intersectionHandler?: (activeTab: TabEnum) => void;
    children: ReactNode;
}

const DescriptionSection = ({
    title,
    description,
    id,
    info,
    intersectionHandler,
    children
}: DescriptionSectionProps) => {
    const t = useTrans();
    const ref = useRef<HTMLElement>(null);

    useSectionIntersect({ ref, id, intersectionHandler });

    return (
        <section
            id={id}
            ref={ref}
            className="container my-8 flex flex-col gap-24 py-16 max-lg:py-8"
        >
            <div className="grid gap-4 text-left lg:gap-6 lg:text-center">
                <span className="text-sm tracking-widest text-primary-400">
                    {t(info)}
                </span>
                <h2 className="font-means text-3xl font-light lg:text-5xl lg:leading-tight">
                    {t(title)}
                </h2>
                <p className="text-lg text-accent-200">{t(description)}</p>
            </div>
            {children}
        </section>
    );
};

export default DescriptionSection;
