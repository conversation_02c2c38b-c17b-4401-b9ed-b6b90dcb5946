import { useState } from 'react';
import FirstImage from '@public/images/pages/corporate/about-us/First.png';
import AchievementIcon from '@public/images/pages/corporate/about-us/icons/Achievement.svg';
import BuildIcon from '@public/images/pages/corporate/about-us/icons/Build.svg';
import CircleCheckIcon from '@public/images/pages/corporate/about-us/icons/CircleCheck.svg';
import CloudIcon from '@public/images/pages/corporate/about-us/icons/Cloud.svg';
import GeometricShapesIcon from '@public/images/pages/corporate/about-us/icons/GeometricShapes.svg';
import ScaleIcon from '@public/images/pages/corporate/about-us/icons/Scale.svg';
import SnakeLineIcon from '@public/images/pages/corporate/about-us/icons/SnakeLine.svg';
import UserShieldIcon from '@public/images/pages/corporate/about-us/icons/UserShield.svg';
import SecondImage from '@public/images/pages/corporate/about-us/Second.png';
import {
    CommonPagesSection,
    DemoSection,
    InfoCard,
    SectionTabs
} from '@src/components/shared';
import { TabEnum } from '@src/interfaces';
import DescriptionSection from '../shared/DescriptionSection';

const ContentSection = () => {
    const [activeTab, setActiveTab] = useState<TabEnum>('first');

    const intersectionHandler = (activeTab: TabEnum) => {
        setActiveTab(activeTab);
    };

    return (
        <>
            <SectionTabs
                intersectionHandler={intersectionHandler}
                activeTab={activeTab}
                sections={[
                    { id: 'first', title: 'Hakkımızda' },
                    { id: 'second', title: 'Değerler' },
                    { id: 'third', title: 'Görevimiz' }
                ]}
            />

            <CommonPagesSection
                order="1"
                title="Değişimi bir adım ötede yönetmek için çalışıyoruz."
                description="EnterERP ekibi, yazılım mühendisliği prensiplerinden faydalanarak dijital dönüşüm alanında geliştirdiği yazılımlarla müşterilerine en kaliteli ve en ekonomik yazılım çözümlerini sunmayı hedefliyor. Kalıplaşmış ve eski çözümler yerine, müşterilerin istekleri çerçevesinde geliştirilen yazılımları ile müşteri memnuniyetini en üst seviyede tutuyor. Tasarlanan ve geliştirilen gerçek zamanlı ve senkronize çalışan kolay kullanılabilir, yüksek performanslı ve güvenlikli yeni nesil otomasyon ve mobil uygulamalar ile dünyada bilişim ve yazılım alanında yeni bir dijital dönüşüm başlatmayı amaçlıyor."
                src={FirstImage}
                id="first"
                info="HAKKIMIZDA"
                intersectionHandler={intersectionHandler}
            />

            <div className="relative">
                <svg
                    width="1800"
                    height="2000"
                    viewBox="0 0 1804 2041"
                    xmlns="http://www.w3.org/2000/svg"
                    className="absolute -top-0 right-0 -z-10 hidden fill-primary-50 lg:block"
                >
                    <path d="M1210.59 510.761C1076.94 510.761 968.468 623.128 968.468 764.099V768.184C968.468 907.111 860 1021.52 728.288 1021.52H724.415C590.766 1021.52 484.234 1135.93 484.234 1274.86V1276.91C484.234 1417.88 377.702 1530.24 244.054 1530.24H240.18C108.468 1530.24 0 1644.65 0 1783.58V1787.66C0 1926.59 108.468 2041 240.18 2041H1448.83C1718.07 2041 1935 1810.13 1935 1528.2V253.337C1935 114.41 1826.53 0 1694.82 0H1690.95C1557.3 0 1450.76 114.41 1450.76 255.38C1452.7 396.351 1344.24 510.761 1210.59 510.761Z" />
                </svg>

                <DescriptionSection
                    info="DEĞERLER"
                    title="Neye Değer Veriyoruz"
                    description="Daha iyi kararlar almak için verilere dayanarak hareket etmeye ve en önemlisi insanları güçlendirmeye inanıyoruz. Çözüm arayan ve mükemmelliği savunan kapsayıcı ve işbirlikçi bir kültür yaratarak değerlerimizi her gün yaşıyoruz."
                    id="second"
                    intersectionHandler={intersectionHandler}
                >
                    <div className="grid gap-10 md:grid-cols-2 2xl:grid-cols-3">
                        <InfoCard
                            title="İnsanlarımıza Sahip Çıkıyoruz"
                            description="EnterERP'de harika insanları işe alıyor ve onları harika şeyler yapmaya davet ediyoruz. Beceri geliştirme ve kendi kariyer yolunuzu belirleme fırsatları sunuyoruz. Kişisel ve profesyonel olarak büyümek, konfor alanlarımızdan çıkmak için sürekli olarak birbirimize meydan okuyoruz."
                            src={UserShieldIcon}
                            isCard
                        />
                        <InfoCard
                            title="Müşteri Her Şeydir"
                            description="Müşterilerimiz olmadan, bir işimiz yok. Ve bunu asla unutmayız. Aldığımız her kararın merkezine müşterilerimizi koyuyoruz. EnterERP'yi kuranlar biz olmamıza rağmen, bunun tamamen bizimle ilgili olmadığını, aslında müşterilerimize sunabileceğimiz değerle ilgili olduğunu anlıyoruz."
                            src={AchievementIcon}
                            isCard
                        />
                        <InfoCard
                            title="Uçtan Uca Sahiplik"
                            description="EnterERP'de uçtan uca sahiplik değerini benimsedik. Her projenin tek bir sahibi vardır, bir kişi bunu gerçekleştirmekle görevlidir."
                            src={SnakeLineIcon}
                            isCard
                        />
                        <InfoCard
                            title="Her Sorunun Bir Çözümü Vardır"
                            description="Çözülemeyecek sorun olmadığına inanıyoruz. Bir çözüme varmak sadece zaman ve ekip çalışması gerektirir. Problem çözme metodolojimiz oldukça basittir. Önce müşterilerimizi ve sorunun - ve olası çözümlerin - onları nasıl etkileyebileceğini düşünürüz."
                            src={CircleCheckIcon}
                            isCard
                        />
                        <InfoCard
                            title="Bugün 'Çok İyi', Yarın 'Mükemmel'den Daha İyi"
                            description="Ekiplerimizi akıllı ve gerçekçi riskler almaya teşvik ediyoruz. Bu projeler sonuç vermezse sorun değil, bundan ders alır ve ilerleriz. Ekibimiz kendilerini yüksek standartlarda tuttuğumuz, her bir işini tutkuyla yapan üyelerden oluşmaktadır."
                            src={GeometricShapesIcon}
                            isCard
                        />
                        <InfoCard
                            title="Katılmıyorum ve Kabul Ediyorum"
                            description="Bir soruna yaklaşmanın ve sorunu çözmenin birçok yolu olduğunun farkındayız ve söylemi teşvik ediyoruz, ancak nihai amaç bir çözüm yolu bulmaktır."
                            src={ScaleIcon}
                            isCard
                        />
                        <InfoCard
                            title="Radikal Şeffaflık"
                            description="İlişkilere ve olaylara yaklaşımımız ben yada sen merkezli değil tamamen korkusuz ve gerçekçidir. İş birliklerimizi şeffaflık üzerine inşa ederek sadece satış ve kâr odaklı bir birliktelik istemiyoruz. İnsanlık için güven odaklı değer oluşturmayı hedefliyoruz."
                            src={BuildIcon}
                            isCard
                        />
                        <InfoCard
                            title="Veri Odaklıyız"
                            description="Bizler müşteri, tedarikçi ve iş arkadaşlarımızı iş ortaklarımız olarak görüyor ve kazanımlarımızı her zaman paylaşıyoruz. Kurmuş olduğumuz ekosistem üzerinde üretilen verinin gücü iş ortaklarımızın bu paydaya olan bağlılığından geliyor."
                            src={CloudIcon}
                            isCard
                        />
                        <InfoCard
                            title="Ekosistem Bağlılığını Destekliyoruz"
                            description="EnterERP sistemine dokunan herkesi iş ortağımız olarak görüyor ve bu ekosistemin bir parçası olarak kazanım sağlaması için destekliyoruz."
                            src={SnakeLineIcon}
                            isCard
                        />
                    </div>
                </DescriptionSection>
            </div>

            <CommonPagesSection
                order="0"
                title="Başarıya giden kanıtlanmış yolda işletmenize rehberlik etmek"
                description="EnterErp Kurumsal Kaynak Planlama, ekiplerin doğru kişileri doğru hesaplara çekerek karmaşık kurumsal iş döngülerini hızlandırmasına yardımcı olur. Bunu, ekiplerin her bir hesapta kiminle etkileşim kuracaklarını ve en yüksek getirili anlaşmaları yapmak için tam olarak ne yapmaları gerektiğini net bir şekilde görmelerine ve her üç ayda bir sayılarını artırmalarına yardımcı olarak yapıyoruz."
                src={SecondImage}
                id="third"
                info="GÖREVİMİZ"
                intersectionHandler={intersectionHandler}
            />

            <div className="bottom-spacer">
                <DemoSection />
            </div>
        </>
    );
};

export default ContentSection;
