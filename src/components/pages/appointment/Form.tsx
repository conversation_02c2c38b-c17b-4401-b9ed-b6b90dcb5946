import dynamic from 'next/dynamic';
import { useEffect, useState } from 'react';
import { FormProvider, SubmitHandler, useForm } from 'react-hook-form';
import {
    Input,
    Phone,
    PrivacyManifest,
    SubmitPartial,
    TermsManifest,
    Textarea
} from '@src/components/shared/Form';
import { useTrans } from '@src/hooks';
import { FormValues } from '@src/interfaces';

const DateTimePicker = dynamic(() => import('./DateTimePicker'));
const Toast = dynamic(() => import('@src/components/shared/Form/Toast'));

const Form = () => {
    const [currentStep, setCurrentStep] = useState<'register' | 'appointment'>(
        'register'
    );
    const [isSubmitting, setIsSubmitting] = useState(false);
    const [showToast, setShowToast] = useState(false);
    const [status, setStatus] = useState<'success' | 'error' | null>(null);
    const [formData, setFormData] = useState({});

    const t = useTrans();

    const methods = useForm<FormValues>({ mode: 'onChange' });

    const onNextStep: SubmitHandler<FormValues> = async (data) => {
        setCurrentStep('appointment');
        setFormData(data);

        const hasMessage = data?.description?.length > 0;

        try {
            await fetch('/api/create-ticket', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    name: `${data.name} ${data.surname}`,
                    email: data.mail,
                    phone: data.phone,
                    message: hasMessage ? data.description : 'Mesaj Yok!',
                    shouldSendSms: false,
                    extra: [
                        {
                            label: 'İşletme Adı',
                            value: data.company
                        }
                    ]
                })
            });
        } catch (err) {
            console.error(err);
        }
    };

    const onSubmit: SubmitHandler<FormValues> = async (data) => {
        if (
            methods.getValues('time') === undefined ||
            methods.getValues('time') === ''
        ) {
            methods.setError('time', { type: 'required' });
            return;
        }

        const mergedData: FormValues = { ...formData, ...data };

        const hasMessage = mergedData?.description?.length > 0;

        setIsSubmitting(true);
        try {
            const response = await fetch('/api/create-ticket', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    name: `${mergedData.name} ${mergedData.surname}`,
                    email: mergedData.mail,
                    phone: mergedData.phone,
                    message: hasMessage ? mergedData.description : 'Mesaj Yok!',
                    shouldSendSms: true,
                    extra: [
                        {
                            label: 'Randevu Tarihi',
                            value: mergedData.date
                        },
                        {
                            label: 'Randevu Saati',
                            value: mergedData.time
                        },
                        {
                            label: 'İşletme Adı',
                            value: mergedData.company
                        }
                    ]
                })
            });
            if (response.ok === true) setStatus('success');
        } catch (err) {
            setStatus('error');
        } finally {
            setIsSubmitting(false);
            setCurrentStep('register');
        }
    };

    useEffect(() => {
        if (methods.formState.isSubmitSuccessful) {
            methods.reset();
            methods.clearErrors();
            setShowToast(true);
        }
        const timer = setTimeout(() => {
            setShowToast(false);
        }, 3000);

        return () => clearTimeout(timer);
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [methods.formState.isSubmitSuccessful]);

    return (
        <div className="flex flex-col items-start justify-center gap-8 bg-white py-12 lg:pl-12">
            {currentStep === 'register' && (
                <div className="mx-auto grid w-11/12 gap-8 lg:mx-0 2xl:w-8/12">
                    <p className="text-center font-means text-5xl">
                        {t('Kaydol')}
                    </p>
                    <p className="mx-auto text-center text-lg text-accent-200 lg:w-8/12">
                        {t(
                            'EnterERP hakkında daha fazla bilgi almak istiyorsanız formu doldurabilir veya bu numaradan bizi arayabilirsiniz. 0 850 582 00 35'
                        )}
                    </p>
                </div>
            )}

            {currentStep === 'appointment' && (
                <div className="mx-auto grid w-11/12 gap-4 lg:mx-0 2xl:w-8/12">
                    <p className="text-center font-means text-3xl">
                        {t('Zaman Planla')}
                    </p>
                    <p className="mx-auto text-center text-lg text-accent-200 lg:w-8/12">
                        {t(
                            'Teşekkürler! Kısa bir telefon görüşmesi için uygun tarih nedir?'
                        )}
                    </p>
                </div>
            )}

            <Toast
                showToast={showToast}
                status={status}
                successMessage="Kaydınız başarıyla gerçekleşti."
                errorMessage="Bir şeyler yanlış gitti. Lütfen daha sonra tekrar deneyin."
            />

            <FormProvider {...methods}>
                {currentStep === 'register' && (
                    <form
                        className="appointment-form mx-auto flex w-11/12 flex-col justify-between gap-7 lg:mx-0 2xl:w-8/12"
                        onSubmit={methods.handleSubmit(onNextStep)}
                    >
                        <fieldset
                            disabled={methods.formState.isSubmitting}
                            className="grid w-full gap-7 lg:grid-cols-2"
                        >
                            <Input name="name" label="Ad *" required />
                            <Input name="surname" label="Soyad *" required />
                            <Phone />
                            <Input name="mail" label="E-Posta (Opsiyonel)" />
                            <div className="lg:col-span-2">
                                <Input
                                    name="company"
                                    label="İşletme Adı *"
                                    required
                                />
                            </div>
                        </fieldset>

                        <Textarea
                            name="description"
                            rows={5}
                            placeholder={t(
                                'Size nasıl yardımcı olabileceğimiz hakkında daha detaylı bilgi verin.'
                            )}
                        />

                        <PrivacyManifest />

                        <TermsManifest />

                        <SubmitPartial
                            isSubmitting={isSubmitting}
                            title="Sonraki"
                        />
                    </form>
                )}

                {currentStep === 'appointment' && (
                    <form
                        className="time-plan-form mx-auto flex w-11/12 flex-col justify-between gap-7 lg:mx-0 2xl:w-8/12"
                        onSubmit={methods.handleSubmit(onSubmit)}
                    >
                        <DateTimePicker />

                        <SubmitPartial
                            isSubmitting={isSubmitting}
                            title="Gönder"
                        />
                    </form>
                )}
            </FormProvider>
        </div>
    );
};

export default Form;
