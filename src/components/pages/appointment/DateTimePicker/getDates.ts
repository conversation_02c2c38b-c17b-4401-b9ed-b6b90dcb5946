export const getDates = {
    currentHour: new Date().getHours(),
    currentDay: new Date().getDate(),
    currentMonth: new Date().getMonth(),
    currentYear: new Date().getFullYear(),
    currentMonthName: new Date().toLocaleDateString('tr-TR', {
        month: 'short'
    }),
    currentWeekdayName: new Date().toLocaleDateString('tr-TR', {
        weekday: 'short'
    })
};

const getDaysInMonth = (month: number, year: number) =>
    new Array(31)
        .fill('')
        .map((_, i) => new Date(year, month - 1, i + 1))
        .filter((v) => v.getMonth() === month - 1);

interface AppointmentDatesArgs {
    day: string;
    weekday: string;
    month: string;
}

export const appointmentDates: AppointmentDatesArgs[] = [];

if (getDates.currentMonth === 12) getDates.currentMonth = 0;

const howManyMonthsToShow = 2;

for (let i = 1; i <= howManyMonthsToShow; i++) {
    getDaysInMonth(getDates.currentMonth + i, getDates.currentYear).forEach(
        (item) => {
            const appointmentDate = item.toLocaleDateString('tr-TR', {
                day: '2-digit',
                weekday: 'short',
                month: 'short'
            });
            appointmentDates.push({
                day: appointmentDate.slice(0, 2),
                weekday: appointmentDate.slice(-3),
                month: appointmentDate
                    .slice(3, appointmentDate.length - 4)
                    .slice(0, 3)
            });
        }
    );
}
