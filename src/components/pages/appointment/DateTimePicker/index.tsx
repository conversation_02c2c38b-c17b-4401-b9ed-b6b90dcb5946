import { useState, useEffect } from 'react';
import { useFormContext } from 'react-hook-form';
import { ChevronLeftIcon, ChevronRightIcon } from '@src/icons/solid';
import { FormValues } from '@src/interfaces';
import { twMerge } from '@src/utils';
import { getDates, appointmentDates } from './getDates';
import Time from './Time';

const DateTimePicker = () => {
    const [currentIndex, setCurrentIndex] = useState(getDates.currentDay);
    const [appointmentTime, setAppointmentTime] = useState('');
    const [appointmentDate, setAppointmentDate] = useState({
        day: getDates.currentDay.toString(),
        month: getDates.currentMonthName,
        weekday: getDates.currentWeekdayName
    });

    const { setValue, clearErrors } = useFormContext<FormValues>();

    useEffect(() => {
        setValue(
            'date',
            `${getDates.currentDay.toString()} ${getDates.currentMonthName} ${
                getDates.currentWeekdayName
            }`
        );
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, []);

    const prevSlideHandler = () => {
        setCurrentIndex((prev) => {
            if (prev === 0) return 1;
            return prev - 1;
        });
    };

    const nextSlideHandler = () => {
        setCurrentIndex((prev) => {
            if (prev === appointmentDates.length - 6) return prev;
            return prev + 1;
        });
    };

    const appointmentTimeHandler = (time: string) => {
        clearErrors('time');
        setAppointmentTime(time);
        setValue('time', time);
    };

    const appointmentDateHandler = (date: typeof appointmentDate) => {
        setAppointmentDate({
            day: date.day,
            month: date.month,
            weekday: date.weekday
        });
        setAppointmentTime('');
        setValue('time', '');
        setValue('date', `${date.day} ${date.month} ${date.weekday}`);
    };

    return (
        <>
            <section className="flex select-none items-center justify-center gap-3">
                <button
                    type="button"
                    title="previous"
                    className="flex h-10 items-center justify-center rounded-md border px-2 transition-opacity hover:opacity-60"
                    onClick={prevSlideHandler}
                >
                    <ChevronLeftIcon className="h-4 w-4 text-accent-200" />
                </button>
                <div className="flex max-w-[250px] flex-nowrap overflow-hidden rounded-lg border sm:max-w-[350px] md:max-w-[450px] lg:max-w-2xl xl:max-w-none">
                    {appointmentDates.map((date, index) => {
                        const isDateDisabled =
                            (Number(date.day) < getDates.currentDay &&
                                date.month === getDates.currentMonthName) ||
                            date.weekday === 'Paz';

                        return (
                            <div
                                style={{
                                    transform: `translateX(-${
                                        (currentIndex - 1) * 100
                                    }%)`
                                }}
                                className="flex w-[calc(100%/3)] min-w-[calc(100%/3)] flex-col text-center xs:w-[calc(100%/4)] xs:min-w-[calc(100%/4)] lg:w-[calc(100%/7)] lg:min-w-[calc(100%/7)]"
                                key={index}
                            >
                                <p className="border-b py-3">{date.weekday}</p>
                                <button
                                    type="button"
                                    disabled={isDateDisabled}
                                    onClick={() => appointmentDateHandler(date)}
                                    className={twMerge(
                                        'cursor-pointer p-4 text-sm font-medium',
                                        date.day === appointmentDate.day
                                            ? 'bg-primary-500 text-white'
                                            : 'hover:bg-primary-500 hover:bg-opacity-20',
                                        isDateDisabled &&
                                            'cursor-not-allowed opacity-30'
                                    )}
                                >
                                    <p>{date.day}</p>
                                    <p>{date.month}</p>
                                </button>
                            </div>
                        );
                    })}
                </div>
                <button
                    type="button"
                    title="next"
                    className="flex h-10 items-center justify-center rounded-md border px-2 transition-opacity hover:opacity-60"
                    onClick={nextSlideHandler}
                >
                    <ChevronRightIcon className="h-4 w-4 text-accent-200" />
                </button>
            </section>

            <Time
                day={appointmentDate.day}
                weekday={appointmentDate.weekday}
                appointmentTime={appointmentTime}
                appointmentTimeHandler={appointmentTimeHandler}
            />
        </>
    );
};

export default DateTimePicker;
