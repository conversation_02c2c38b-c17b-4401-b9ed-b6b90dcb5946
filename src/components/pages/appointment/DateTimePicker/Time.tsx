import { useFormContext } from 'react-hook-form';
import { useTrans } from '@src/hooks';
import { FormValues } from '@src/interfaces';
import { twMerge } from '@src/utils';
import { getDates } from './getDates';

const times = [
    '09:00',
    '09:30',
    '10:00',
    '10:30',
    '11:00',
    '11:30',
    '13:00',
    '13:30',
    '14:00',
    '14:30',
    '15:00',
    '15:30',
    '16:00',
    '16:30',
    '17:00'
];

const allowedTimes: Record<string, string[]> = {
    Pzt: [
        '09:00',
        '09:30',
        '10:30',
        '11:00',
        '11:30',
        '13:30',
        '14:30',
        '15:00',
        '16:00',
        '16:30',
        '17:00'
    ],
    Sal: [
        '09:00',
        '10:00',
        '10:30',
        '11:00',
        '11:30',
        '13:00',
        '13:30',
        '14:00',
        '14:30',
        '16:00',
        '17:00'
    ],
    Çar: [
        '09:00',
        '10:30',
        '11:30',
        '13:00',
        '13:30',
        '14:00',
        '14:30',
        '16:00',
        '17:00'
    ],
    Per: [
        '09:00',
        '09:30',
        '10:00',
        '11:30',
        '13:00',
        '13:30',
        '14:30',
        '16:00',
        '16:30',
        '17:00'
    ],
    Cum: [
        '09:00',
        '09:30',
        '10:30',
        '11:00',
        '11:30',
        '13:00',
        '14:00',
        '14:30',
        '16:00',
        '16:30',
        '17:00'
    ],
    Cmt: ['09:00', '09:30', '10:00', '10:30', '11:00', '11:30', '13:00']
};

interface TimeProps {
    weekday: string;
    day: string;
    appointmentTime: string;
    appointmentTimeHandler: (time: string) => void;
}

const Time = ({
    weekday,
    day,
    appointmentTime,
    appointmentTimeHandler
}: TimeProps) => {
    const t = useTrans();

    const {
        formState: { errors }
    } = useFormContext<FormValues>();

    return (
        <>
            <div className="grid gap-4">
                <p className="text-center font-means text-3xl">
                    {t('Ne zaman sizin için uygun olur?')}
                </p>
                <p className="mx-auto text-center text-lg text-accent-200 lg:w-8/12">
                    {t('Toplantı süresi 45 Dakika Europe/İstanbul Time')}
                </p>
                {errors.time?.type === 'required' && (
                    <p className="text-center font-medium text-red-600">
                        {t('Lütfen bir zaman aralığı seçiniz.')}
                    </p>
                )}
            </div>

            <div className="grid select-none grid-cols-3 gap-x-6 gap-y-4 2xl:grid-cols-5">
                {times.map((time) => {
                    const isTimeDisabled =
                        !allowedTimes[weekday]?.includes(time);

                    const isTimePassedToday =
                        getDates.currentDay.toString() === day &&
                        Number(time.slice(0, 2)) <= getDates.currentHour;

                    return (
                        <button
                            type="button"
                            disabled={isTimeDisabled || isTimePassedToday}
                            onClick={() => {
                                appointmentTimeHandler(time);
                            }}
                            key={time}
                            className={twMerge(
                                'cursor-pointer rounded-lg border border-accent-100 bg-white py-2 text-center transition',
                                time === appointmentTime
                                    ? 'bg-primary-500 text-white'
                                    : 'hover:border-primary-500 hover:bg-primary-500 hover:bg-opacity-20',
                                (isTimeDisabled || isTimePassedToday) &&
                                    'cursor-not-allowed opacity-30'
                            )}
                        >
                            {time}
                        </button>
                    );
                })}
            </div>
        </>
    );
};

export default Time;
