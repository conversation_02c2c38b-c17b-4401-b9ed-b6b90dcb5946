import { useTrans } from '@src/hooks';
import IntegrationCards from './IntegrationCards';

interface SectionHeaderProps {
    title: string;
    description: string;
}

const SectionHeader = ({ title, description }: SectionHeaderProps) => {
    const t = useTrans();

    return (
        <>
            <div className="mx-auto grid w-full gap-4 text-center lg:w-8/12 lg:gap-16">
                <h2 className="font-means text-3xl font-light lg:text-6xl">
                    {t(title)}
                </h2>
                <p className="text-accent-200 lg:text-xl">{t(description)}</p>
            </div>
        </>
    );
};

export default SectionHeader;
