import BuildIcon from '@public/images/icons/Build.svg';
import ScaleIcon from '@public/images/icons/Scale.svg';
import SecurityIcon from '@public/images/icons/Security.svg';
import { InfoCard } from '@src/components/shared';
import SectionHeader from '../shared/SectionHeader';
import InfoBorderCard from '@src/components/shared/InfoBorderCard';

const SectionTwo = () => {
    return (
        <>
            <section className=" container">
                <div className="grid place-items-center gap-8 pt-8 md:grid-cols-2 lg:grid-cols-4 lg:gap-4 lg:pt-16">
                    <InfoBorderCard
                        title="Kapsamlı ve Güçlü"
                        description="Tüm süreçleri yönetmenizi ve işinizi büyütmenizi destekler"
                        src={BuildIcon}
                    />
                    <InfoBorderCard
                        title="Sektörel Çözümler"
                        description="Esnek ve özelleştirilmiş işinize uygun çözüm ve fonksiyonlar"
                        src={ScaleIcon}
                    />
                    <InfoBorderCard
                        title="Hızlı ve Kolay"
                        description="Esnek ve özelleştirilmiş işinize uygun çözüm ve fonksiyonlar"
                        src={SecurityIcon}
                    />
                    <InfoBorderCard
                        title="Uygun Maliyet"
                        description="Esnek ve özelleştirilmiş işinize uygun çözüm ve fonksiyonlar"
                        src={SecurityIcon}
                    />
                </div>
            </section>
        </>
    );
};

export default SectionTwo;
