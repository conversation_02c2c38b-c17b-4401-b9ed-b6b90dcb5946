import { useTrans } from '@src/hooks';
import ExtendedImage from '@src/components/shared/ExtendedImage';
import IntegrationImage1 from '@public/images/pages/home/<USER>';
import IntegrationImage2 from '@public/images/pages/home/<USER>';
import IntegrationImage3 from '@public/images/pages/home/<USER>';
import IntegrationImage4 from '@public/images/pages/home/<USER>';
import IntegrationImage5 from '@public/images/pages/home/<USER>';

import CargoImage1 from '@public/images/pages/home/<USER>/hepsijet.png';
import CargoImage2 from '@public/images/pages/home/<USER>/aras.png';
import CargoImage3 from '@public/images/pages/home/<USER>/mgn.png';
import CargoImage4 from '@public/images/pages/home/<USER>/surat.png';
import CargoImage5 from '@public/images/pages/home/<USER>/yurtici.png';
import Link from 'next/link';

interface IconCardProps {
    src: string;
    alt: string;
    bgColor?: string;
}

const IconCard = ({ src, alt, bgColor = 'bg-white' }: IconCardProps) => {
    return (
        <div
            className={`aspect-square flex h-8 w-8 items-center justify-center rounded-2xl lg:h-16 lg:w-16 ${bgColor}  `}
        >
            <ExtendedImage
                src={src}
                width={32}
                height={32}
                alt={alt}
                className="h-full w-full rounded-lg object-contain"
            />
        </div>
    );
};

const IntegrationCards = () => {
    const t = useTrans();

    return (
        <div className="relative mx-auto mt-8 grid w-full gap-3 lg:gap-6 ">
            <div className="grid grid-cols-2 gap-3 lg:gap-6">
                <div className="card-shadow relative grid gap-4  rounded-4xl border bg-white p-4 lg:p-8 lg:py-10">
                    <h3 className="mb-4 text-left  font-semibold text-[#001e2b] lg:text-2xl">
                        {t('Onboarding & client portals')}
                    </h3>
                    <div className="flex flex-wrap gap-2 lg:gap-4">
                        <IconCard
                            src={IntegrationImage1.src}
                            alt="Integration icon"
                            bgColor="bg-[#00c389]"
                        />
                        <IconCard
                            src={IntegrationImage2.src}
                            alt="Integration icon"
                            bgColor="bg-[#0a1929]"
                        />
                        <IconCard
                            src={IntegrationImage3.src}
                            alt="Integration icon"
                            bgColor="bg-[#6366f1]"
                        />
                        <IconCard
                            src={IntegrationImage4.src}
                            alt="Integration icon"
                            bgColor="bg-[#333]"
                        />
                        <IconCard
                            src={IntegrationImage5.src}
                            alt="Integration icon"
                            bgColor="bg-[#333]"
                        />
                        <IconCard
                            src={IntegrationImage1.src}
                            alt="Integration icon"
                            bgColor="bg-[#00c389]"
                        />
                    </div>
                </div>

                <Link href={'/integrations-2'}>
                    <div className="card-shadow relative grid gap-4  rounded-4xl border bg-white p-4 lg:p-8 lg:py-10">
                        <h3 className="mb-4  text-right font-semibold text-[#001e2b] lg:text-2xl">
                            {t('Sales & deal rooms')}
                        </h3>
                        <div className="flex flex-wrap justify-end gap-2 lg:gap-4">
                            <IconCard
                                src={CargoImage1.src}
                                alt="Integration icon"
                                bgColor="bg-[#4285f4]"
                            />
                            <IconCard
                                src={CargoImage2.src}
                                alt="Integration icon"
                                bgColor="bg-[#ea4335]"
                            />
                            <IconCard
                                src={CargoImage3.src}
                                alt="Integration icon"
                                bgColor="bg-[#4285f4]"
                            />
                            <IconCard
                                src={CargoImage4.src}
                                alt="Integration icon"
                                bgColor="bg-[#ff7a00]"
                            />
                            <IconCard
                                src={CargoImage5.src}
                                alt="Integration icon"
                                bgColor="bg-[#0a1929]"
                            />
                            <IconCard
                                src={CargoImage1.src}
                                alt="Integration icon"
                                bgColor="bg-[#4285f4]"
                            />
                        </div>
                    </div>
                </Link>
            </div>

            <div className="absolute left-1/2 top-1/2 z-10 -translate-x-1/2 -translate-y-1/2">
                <Link href="/integrations" className="m-0 p-0">
                    <div className="card-shadow flex h-24 w-24 flex-col items-center justify-center rounded-4xl border bg-white p-8 lg:h-44 lg:w-44">
                        <svg
                            viewBox="0 0 29 32"
                            className="h-40 w-40"
                            xmlns="http://www.w3.org/2000/svg"
                        >
                            <path
                                d="M22.735 26.015a2.206 2.206 0 0 0 2.089-2.914 2.233 2.233 0 0 0-2.143-1.49h-2.143v2.202a2.142 2.142 0 0 0 2.197 2.202Z"
                                fill="#050B18"
                            />
                            <path
                                d="M24.894 8.476a2.213 2.213 0 0 0-2.615-2.61 2.277 2.277 0 0 0-1.746 2.261v2.1h2.1a2.277 2.277 0 0 0 2.26-1.751Z"
                                fill="#050B18"
                            />
                            <path
                                d="M4.735 23.824a2.207 2.207 0 0 0 2.914 2.09 2.234 2.234 0 0 0 1.49-2.144v-2.142H6.994a2.217 2.217 0 0 0-2.26 2.196Z"
                                fill="#050B18"
                            />
                            <path
                                d="M6.936 5.824a2.207 2.207 0 0 0-2.089 2.914 2.234 2.234 0 0 0 2.143 1.49h2.143V8.025A2.304 2.304 0 0 0 6.93 5.824"
                                fill="#050B18"
                            />
                            <path
                                d="M9.808 12.504H4.06a3.45 3.45 0 0 0-3.434 3.418 3.45 3.45 0 0 0 3.418 3.418h5.764l3.418-3.418-3.418-3.418Z"
                                fill="#050B18"
                            />
                            <path
                                d="M19.858 12.504h5.753a3.45 3.45 0 0 1 3.407 3.418 3.45 3.45 0 0 1-3.417 3.418h-5.743l-3.418-3.418 3.418-3.418Z"
                                fill="#050B18"
                            />
                            <path
                                d="M11.415 21.027v5.749a3.45 3.45 0 0 0 3.418 3.418 3.45 3.45 0 0 0 3.418-3.418v-5.748l-3.418-3.418-3.418 3.418Z"
                                fill="#050B18"
                            />
                            <path
                                d="M11.415 10.972V5.224a3.45 3.45 0 0 1 3.418-3.418 3.45 3.45 0 0 1 3.418 3.418v5.748l-3.418 3.423-3.418-3.423Z"
                                fill="#050B18"
                            />
                        </svg>
                    </div>
                </Link>
            </div>

            <div className="grid grid-cols-2 gap-3 lg:gap-6">
                <div className="card-shadow relative grid gap-4  rounded-4xl border bg-white p-4 lg:p-8 lg:py-10">
                    <h3 className="mb-4 text-left font-semibold text-[#001e2b] lg:text-2xl">
                        {t('AI & conversation intelligence')}
                    </h3>
                    <div className="flex flex-wrap gap-2 lg:gap-4">
                        <IconCard
                            src={IntegrationImage1.src}
                            alt="Integration icon"
                            bgColor="bg-[#00c389]"
                        />
                        <IconCard
                            src={IntegrationImage2.src}
                            alt="Integration icon"
                            bgColor="bg-[#0a1929]"
                        />
                        <IconCard
                            src={IntegrationImage3.src}
                            alt="Integration icon"
                            bgColor="bg-[#6366f1]"
                        />
                        <IconCard
                            src={IntegrationImage4.src}
                            alt="Integration icon"
                            bgColor="bg-[#333]"
                        />
                        <IconCard
                            src={IntegrationImage5.src}
                            alt="Integration icon"
                            bgColor="bg-[#333]"
                        />
                        <IconCard
                            src={IntegrationImage1.src}
                            alt="Integration icon"
                            bgColor="bg-[#00c389]"
                        />
                    </div>
                </div>

                <div className="card-shadow relative grid gap-4  rounded-4xl border bg-white p-4 lg:p-8 lg:py-10">
                    <h3 className="mb-4 text-right font-semibold text-[#001e2b] lg:text-2xl">
                        {t('Knowledge management & file storage')}
                    </h3>
                    <div className="flex flex-wrap justify-end gap-2 lg:gap-4">
                        <IconCard
                            src={IntegrationImage1.src}
                            alt="Integration icon"
                            bgColor="bg-[#00c389]"
                        />
                        <IconCard
                            src={IntegrationImage2.src}
                            alt="Integration icon"
                            bgColor="bg-[#0a1929]"
                        />
                        <IconCard
                            src={IntegrationImage3.src}
                            alt="Integration icon"
                            bgColor="bg-[#6366f1]"
                        />
                        <IconCard
                            src={IntegrationImage4.src}
                            alt="Integration icon"
                            bgColor="bg-[#333]"
                        />
                        <IconCard
                            src={IntegrationImage5.src}
                            alt="Integration icon"
                            bgColor="bg-[#333]"
                        />
                        <IconCard
                            src={IntegrationImage1.src}
                            alt="Integration icon"
                            bgColor="bg-[#00c389]"
                        />
                    </div>
                </div>
            </div>
        </div>
    );
};

export default IntegrationCards;
