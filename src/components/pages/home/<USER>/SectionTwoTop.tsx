import LogoImage1 from '@public/images/pages/site-logo/1.png';
import LogoImage2 from '@public/images/pages/site-logo/2.png';
import LogoImage3 from '@public/images/pages/site-logo/3.png';
import LogoImage4 from '@public/images/pages/site-logo/4-0.png';
import LogoImage5 from '@public/images/pages/site-logo/4-1.png';
import LogoImage6 from '@public/images/pages/site-logo/5.png';
import LogoImage7 from '@public/images/pages/site-logo/6.png';
import LogoImage8 from '@public/images/pages/site-logo/8.png';
import LogoImage9 from '@public/images/pages/site-logo/9.png';
import LogoImage10 from '@public/images/pages/site-logo/10.png';
import LogoImage11 from '@public/images/pages/site-logo/11.png';
import LogoImage12 from '@public/images/pages/site-logo/12.png';
import LogoImage13 from '@public/images/pages/site-logo/13.png';
import LogoImage14 from '@public/images/pages/site-logo/14.png';
import LogoImage15 from '@public/images/pages/site-logo/15.png';
import Image from 'next/image';
import { Swiper, SwiperSlide } from 'swiper/react';
import { Navigation } from 'swiper/modules';
import { ChevronLeftIcon, ChevronRightIcon } from '@src/icons/solid';

import 'swiper/css';
import 'swiper/css/pagination';
import 'swiper/css/navigation';
import { twMerge } from 'tailwind-merge';
const SectionTwoTop = () => {
    return (
        <>
            <section className=" container">
                <div className="mx-auto grid w-full gap-4 text-center lg:w-7/12 lg:gap-16">
                    <h2 className="font-means text-xs font-light text-accent-200 lg:text-sm">
                        10.000’den Fazla Kullanıcı İşletmesi İçin Bize Güveniyor
                    </h2>
                </div>

                <div className="grid cursor-pointer select-none grid-cols-1 place-items-center gap-8 pt-8 lg:gap-36 lg:pt-4">
                    <div className="group relative w-full">
                        <div className="absolute left-0 top-1/2 z-10 -translate-x-1/2 -translate-y-1/2 opacity-0 transition-opacity duration-300 group-hover:opacity-100">
                            <button
                                className="slider-prev-btn flex h-8 w-8 items-center justify-center rounded-full border  shadow-md transition-opacity duration-200 hover:opacity-70 lg:h-10 lg:w-10"
                                aria-label="Previous slide"
                            >
                                <ChevronLeftIcon className="h-4 w-4 text-primary-200" />
                            </button>
                        </div>
                        <div className="absolute right-0 top-1/2 z-10 -translate-y-1/2 translate-x-1/2 opacity-0 transition-opacity duration-300 group-hover:opacity-100">
                            <button
                                className="slider-next-btn flex h-8 w-8 items-center justify-center rounded-full border  shadow-md transition-opacity duration-200 hover:opacity-70 lg:h-10 lg:w-10"
                                aria-label="Next slide"
                            >
                                <ChevronRightIcon className="h-4 w-4 text-primary-200 " />
                            </button>
                        </div>
                        <Swiper
                            className="h-fit w-full select-none"
                            modules={[Navigation]}
                            spaceBetween={10}
                            loop
                            threshold={2}
                            navigation={{
                                prevEl: '.slider-prev-btn',
                                nextEl: '.slider-next-btn'
                            }}
                            breakpoints={{
                                300: {
                                    slidesPerView: 2
                                },
                                512: {
                                    slidesPerView: 3
                                },
                                768: {
                                    slidesPerView: 4
                                },
                                1280: {
                                    slidesPerView: 6
                                },
                                1444: {
                                    slidesPerView: 6
                                }
                            }}
                        >
                            {[
                                LogoImage1,
                                LogoImage2,
                                LogoImage3,
                                LogoImage4,
                                LogoImage5,
                                LogoImage6,
                                LogoImage7,
                                LogoImage8,
                                LogoImage9,
                                LogoImage10,
                                LogoImage11,
                                LogoImage12,
                                LogoImage13,
                                LogoImage14,
                                LogoImage15
                            ].map((com, index) => (
                                <SwiperSlide
                                    className="group flex items-center justify-center"
                                    key={index}
                                >
                                    <div className="relative flex h-auto w-full items-center justify-center">
                                        <Image
                                            className="mx-auto w-48 transition-opacity duration-200 group-hover:opacity-90"
                                            src={
                                                com
                                                    ? `${com.src}`
                                                    : '/no-image.png'
                                            }
                                            alt={'company'}
                                            width={1920}
                                            height={1000}
                                        />
                                    </div>
                                </SwiperSlide>
                            ))}
                        </Swiper>
                    </div>
                </div>
            </section>
        </>
    );
};

export default SectionTwoTop;
