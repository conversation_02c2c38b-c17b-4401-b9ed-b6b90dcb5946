.sectionFourBg {
    background: linear-gradient(135deg, #f5f3ff 0%, #ebf7ff 100%);
    min-height: 100vh;
    padding: 48px 0;
    position: relative;
}

.grid {
    display: grid;
    grid-template-columns: repeat(5, 1fr);
    gap: 24px;
    justify-items: center;
    align-items: center;
    width: 100%;
    max-width: 1400px;
    margin: 0 auto;
}

.card {
    background: #fff;
    border-radius: 20px;
    box-shadow: 0 4px 24px 0 rgba(80, 80, 180, 0.08);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 24px 12px 16px 12px;
    min-height: 120px;
    min-width: 120px;
    transition:
        box-shadow 0.2s,
        transform 0.2s;
    cursor: pointer;
    border: 1px solid #f0f0f0;
}

.card:hover {
    box-shadow: 0 8px 32px 0 rgba(80, 80, 180, 0.16);
    transform: translateY(-4px) scale(1.03);
}

.cardIcon {
    margin-bottom: 12px;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.cardTitle {
    font-size: 15px;
    font-weight: 500;
    color: #222;
    text-align: center;
}

/* Büyük kartlar için */
.largeCard {
    grid-column: span 2;
    grid-row: span 2;
    min-height: 260px;
    min-width: 260px;
    padding: 32px 20px 24px 20px;
    box-shadow: 0 8px 32px 0 rgba(80, 80, 180, 0.12);
    position: relative;
}

.largeCardImage {
    width: 100%;
    max-width: 180px;
    margin: 0 auto 16px auto;
    display: block;
}

.largeCardTitle {
    font-size: 20px;
    font-weight: 700;
    color: #222;
    margin-top: 12px;
}

@media (max-width: 1200px) {
    .grid {
        grid-template-columns: repeat(4, 1fr);
    }
}
@media (max-width: 900px) {
    .grid {
        grid-template-columns: repeat(2, 1fr);
    }
    .largeCard {
        min-width: 180px;
        min-height: 180px;
    }
}
@media (max-width: 600px) {
    .sectionFourBg {
        padding: 16px 0;
    }
    .grid {
        gap: 12px;
    }
    .card,
    .largeCard {
        min-width: 100px;
        min-height: 100px;
        padding: 12px 6px 8px 6px;
    }
    .largeCard {
        min-width: 120px;
        min-height: 120px;
    }
}
