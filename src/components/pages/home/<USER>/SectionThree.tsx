import AtlasIcon from '@public/images/icons/Atlas.svg';
import RealmIcon from '@public/images/icons/Realm.svg';
import { FeatureCard, FeatureCardWithBgImage } from '@src/components/shared';
import SectionHeader from '../shared/SectionHeader';

const SectionThree = () => {
    return (
        <section className="spacer relative overflow-hidden">
            <div className="container">
                <SectionHeader
                    title="Gerçek Bir Bulut ERP Çözümü"
                    description="Büyüyen şirketlerin bulut üzerinde çalışmalarının her yönünü görmelerine ve bağlamalarına yardımcı olan akıllı, sektör odaklı iş işlevselliği."
                />

                <div className="flex flex-col gap-8 pt-8 lg:pt-16 2xl:flex-row">
                    <div className="grid 2xl:w-8/12">
                        <FeatureCardWithBgImage
                            title="Tüm İhtiyaçlarınız Tek Programda"
                            description="<PERSON><PERSON>, daha kapsamlı ve daha gü<PERSON>. Geleceğin teknolojisine dokunun. Her ihtiyacınız için bir uygulama."
                            href="company/all-in-one"
                            icon={RealmIcon}
                            info="DİNAMİK"
                            buttonLabel="Devamını Oku"
                            animationPath="/animations/home/<USER>"
                        />
                    </div>

                    <div className="grid 2xl:w-4/12">
                        <FeatureCard
                            title="Bulut İçin Neden EnterERP ?"
                            description="EnterERP, şirketlerin ve müşterilerinin sürekli gelişen ihtiyaçlarını karşılamak için doğrudan bulutta doğdu."
                            src={AtlasIcon}
                            info="DOĞUŞ"
                            href="company/why-entererp"
                            buttonLabel="Devamını Oku"
                        />
                    </div>
                </div>
            </div>

            <div className="absolute  right-0 top-20 -z-20 hidden w-screen lg:block">
                <svg
                    xmlns="http://www.w3.org/2000/svg"
                    width="4014"
                    height="1964"
                    viewBox="0 0 4014 1964"
                    fill="none"
                >
                    <path
                        d="M113.346 784.57C266.885 614.355 519.369 592.018 675.723 735.04L679.843 738.809C836 881.651 1088.48 859.313 1242.22 689.277L1243.19 688.214C1397.12 517.574 1650.39 495.564 1806.55 639.582L1807.53 640.479C1962.32 783.241 2213.25 762.995 2367.78 595.245L2656.03 282.088C2965.48 -53.8363 3467.14 -95.2907 3778.08 189.143C4090.99 475.364 4092.15 982.624 3780.75 1319.89L3768.32 1333.48C3455.77 1672.43 2948.23 1710.45 2638.81 1418.44L2373.55 1167.99C2218.75 1022.1 1964.88 1041.61 1809.39 1211.59L1251.55 1821.43C1097.61 1989.72 846.688 2011.14 691.119 1868.84L117.886 1344.48C-36.507 1203.25 -38.6388 953.086 113.346 784.57Z"
                        fill="#EBF7FF"
                    />
                </svg>
            </div>
        </section>
    );
};

export default SectionThree;
