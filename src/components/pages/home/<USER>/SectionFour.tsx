import { GhostButton } from '@src/components/shared';
import { useTrans } from '@src/hooks';

import Image from 'next/image';
import React from 'react';

// Import image

import LargeSatinAlma from '@public/images/pages/home/<USER>/new-satin-alma.png';
import LargeDepo from '@public/images/pages/home/<USER>/new-depo.png';
import LargeBi from '@public/images/pages/home/<USER>/new-bi.png';
import LargeFinans from '@public/images/pages/home/<USER>/new-finans.png';
import LargeTicaret from '@public/images/pages/home/<USER>/new-ticaret.png';
import LargeUretim from '@public/images/pages/home/<USER>/new-uretim.png';

import {
    CrmIcon,
    DemirbasIcon,
    EcozumlerIcon,
    GiderIcon,
    MedikalIcon,
    MrpIcon,
    ProjeIcon,
    UretimIcon
} from '@public/icons';
import ServisIcon from '@public/icons/Servis';
import PcmIcon from '@public/icons/Pcm';

import CrmImage from '@public/icons/crm.png';
import CrmBlueImage from '@public/icons/crmBlue.png';
import GiderImage from '@public/icons/Gider.png';
import GiderBlueImage from '@public/icons/GiderBlue.png';
import MedikalImage from '@public/icons/Medikal.png';
import MedikalBlueImage from '@public/icons/MedikalBlue.png';
import ServisImage from '@public/icons/Servis.png';
import ServisBlueImage from '@public/icons/ServisBlue.png';
import DeskImage from '@public/icons/Desk.png';
import DeskBlueImage from '@public/icons/DeskBlue.png';
import ProjeImage from '@public/icons/Proje.png';
import ProjeBlueImage from '@public/icons/ProjeBlue.png';
import HrImage from '@public/icons/Hr.png';
import HrBlueImage from '@public/icons/HrBlue.png';
import EcozumImage from '@public/icons/Ecozum.png';
import EcozumBlueImage from '@public/icons/ECozumBlue.png';
import UretimImage from '@public/icons/Uretim.png';
import UretimBlueImage from '@public/icons/UretimBlue.png';
import PcmImage from '@public/icons/Pcm.png';
import PcmBlueImage from '@public/icons/PcmBlue.png';
import MrpImage from '@public/icons/Mrp.png';
import MrpBlueImage from '@public/icons/MrpBlue.png';
import LojistikImage from '@public/icons/Lojistik.png';
import LojistikBlueImage from '@public/icons/LojistikBlue.png';
import FlowImage from '@public/icons/Flow.png';
import FlowBlueImage from '@public/icons/FlowBlue.png';
import KaliteImage from '@public/icons/Kalite.png';
import KaliteBlueImage from '@public/icons/KaliteBlue.png';
import AktiviteImage from '@public/icons/Aktivite.png';
import AktiviteBlueImage from '@public/icons/AktiviteBlue.png';
import DemirbasImage from '@public/icons/demir.png';
import DemirbasBlueImage from '@public/icons/demirblue.png';

// Small Feature Card Component interface
interface SmallFeatureCardProps {
    title: string;
    icon?: any; // Using 'any' for SVG imports
    hoverIcon?: any; // Blue icon for hover state
    isSelected?: boolean;
}

const SmallFeatureCard = ({
    title,
    icon,
    hoverIcon,
    isSelected = false
}: SmallFeatureCardProps) => {
    const t = useTrans();

    return (
        <div
            className={`aspect-square group flex h-20 w-full cursor-pointer flex-col items-center justify-center rounded-[18px] border py-5 lg:h-[125px]  ${'border-gray-200 bg-white'} p-3 text-center shadow-sm transition-all hover:border-primary-200 hover:shadow-md`}
        >
            {icon && (
                <div className="mb-2 flex h-6 w-6 items-center justify-center lg:h-[50px] lg:w-[50px]">
                    <div className="relative h-6 w-6 overflow-hidden lg:h-[50px] lg:w-[50px]">
                        <div className="relative h-full w-full">
                            <Image
                                src={AktiviteImage}
                                alt="Demirbas"
                                className="absolute inset-0 h-full w-full object-contain  group-hover:opacity-0"
                                width={1000}
                                height={1000}
                                style={{
                                    filter: 'none',
                                    maxWidth: '100%',
                                    maxHeight: '100%'
                                }}
                            />
                            <Image
                                src={AktiviteBlImage}
                                alt="Demirbas"
                                className="absolute inset-0 h-full w-full object-contain opacity-0  group-hover:opacity-100"
                                width={1000}
                                height={1000}
                                style={{
                                    filter: 'none',
                                    maxWidth: '100%',
                                    maxHeight: '100%'
                                }}
                            />
                        </div>
                    </div>
                </div>
            )}
            <p className="text-xs font-medium text-[#5D6C74] group-hover:text-primary-200 lg:text-xs">
                {t(title)}
            </p>
        </div>
    );
};

const SmallFeatureGhostCard = ({
    title,
    icon,
    isSelected = false
}: SmallFeatureCardProps) => {
    const t = useTrans();

    return (
        <div
            className={`aspect-square group flex h-16 w-full cursor-pointer flex-col items-center justify-center rounded-[18px] border py-5  ${'border-gray-200 bg-white'} p-3 text-center shadow-sm transition-all hover:border-primary-200 hover:shadow-md`}
        >
            {icon && (
                <div className="mb-2 flex h-10 w-10 items-center justify-center">
                    <Image
                        src={icon}
                        alt={t(title)}
                        className="flex h-6 w-6 items-center justify-center text-gray-700"
                    />
                </div>
            )}
            <p className="text-base font-medium text-gray-700 group-hover:text-primary-200">
                {t(title)}
            </p>
        </div>
    );
};

// Large Feature Card Component interface
interface LargeFeatureCardProps {
    title: string;
    icon?: any; // Using 'any' for SVG imports
    description?: string;
}

const LargeFeatureCard = ({
    title,
    icon,
    description
}: LargeFeatureCardProps) => {
    const t = useTrans();

    return (
        <div className="aspect-square group relative flex cursor-pointer flex-col items-center justify-between overflow-hidden rounded-[18px] border border-gray-200 bg-white p-4 text-center shadow-sm transition-all hover:border-primary-200 hover:shadow-md">
            {/* Background gradient */}
            <svg
                xmlns="http://www.w3.org/2000/svg"
                width="292"
                height="150"
                viewBox="0 0 292 150"
                fill="none"
                className="absolute top-0 h-24 w-full"
            >
                <g opacity="0.25" filter="url(#filter0_f_1667_67363)">
                    <path
                        d="M29.0887 -19.4352C27.9489 -54.4732 111.185 -24.8859 146.332 -26.0292L156.765 -26.3686C191.912 -27.512 250.81 -54.1748 260.006 -29.6526C269.201 -5.13029 247.234 11.7285 227.82 21.946C208.407 32.1636 145.129 37.5189 145.129 37.5189C109.982 38.6623 30.2286 15.6028 29.0887 -19.4352Z"
                        fill="url(#paint0_linear_1667_67363)"
                    />
                </g>
                <defs>
                    <filter
                        id="filter0_f_1667_67363"
                        x="-82.6754"
                        y="-151.74"
                        width="456.592"
                        height="301.052"
                        filterUnits="userSpaceOnUse"
                        color-interpolation-filters="sRGB"
                    >
                        <feFlood
                            flood-opacity="0"
                            result="BackgroundImageFix"
                        />
                        <feBlend
                            mode="normal"
                            in="SourceGraphic"
                            in2="BackgroundImageFix"
                            result="shape"
                        />
                        <feGaussianBlur
                            stdDeviation="55.8763"
                            result="effect1_foregroundBlur_1667_67363"
                        />
                    </filter>
                    <linearGradient
                        id="paint0_linear_1667_67363"
                        x1="249.764"
                        y1="17.7855"
                        x2="188.069"
                        y2="-77.7938"
                        gradientUnits="userSpaceOnUse"
                    >
                        <stop stop-color="#40DDFF" />
                        <stop offset="0.568916" stop-color="#0091FF" />
                        <stop offset="1" stop-color="#0038FF" />
                    </linearGradient>
                </defs>
            </svg>

            {/* Main content area */}
            <div className="relative z-10 flex w-full flex-1 flex-col items-center justify-center p-4">
                <Image
                    src={icon}
                    alt={t(title)}
                    className="h-12 w-12 lg:h-full lg:w-full"
                />
            </div>

            {/* Title section */}
            <div className="relative z-10 mt-auto">
                <h3 className="text-sm font-semibold text-gray-800 group-hover:text-primary-600 lg:text-base">
                    {t(title)}
                </h3>
            </div>
        </div>
    );
};

const SectionFour = () => {
    const t = useTrans();

    // Top row small cards
    const topRowCards = [
        {},
        {
            title: 'CRM',
            icon: CrmImage,
            hoverIcon: CrmBlueImage,
            isSelected: true
        },
        { title: 'Demirbaş', icon: DemirbasIcon, hoverIcon: DemirbasIcon },
        { title: 'Gider', icon: CrmImage, hoverIcon: CrmBlueImage },
        { title: 'Medikal', icon: CrmImage, hoverIcon: CrmBlueImage },
        { title: 'Servis', icon: CrmImage, hoverIcon: CrmBlueImage },
        { title: 'Desk', icon: CrmImage, hoverIcon: CrmBlueImage },
        { title: 'Proje', icon: CrmImage, hoverIcon: CrmBlueImage },
        { title: 'İnsan Kaynakları', icon: CrmImage, hoverIcon: CrmBlueImage },
        {}
    ];
    const topRowCardsTop = [{}, {}, {}, {}, {}, {}, {}, {}, {}, {}];

    // Middle row small cards (left side)
    const leftSideCards = [{}, {}, {}, {}];

    // Middle row small cards (right side)
    const rightSideCards = [{}, {}, {}, {}];

    // Large feature cards (center)
    const centerLargeCards = [
        {
            title: 'Muhasebe',
            icon: LargeFinans,
            description:
                'lorem ipsum dolor sit amet consectetur adipiscing elit.'
        },
        {
            title: 'Finans',
            icon: LargeFinans,
            description:
                'lorem ipsum dolor sit amet consectetur adipiscing elit.'
        },
        {
            title: 'Satış',
            icon: LargeSatinAlma,
            description:
                'lorem ipsum dolor sit amet consectetur adipiscing elit.'
        },
        {
            title: 'Satın Alma',
            icon: LargeSatinAlma,
            description:
                'lorem ipsum dolor sit amet consectetur adipiscing elit.'
        },
        {
            title: 'Depo & Stok',
            icon: LargeDepo,
            description:
                'lorem ipsum dolor sit amet consectetur adipiscing elit.'
        },
        {
            title: 'E-Ticaret & B2B',
            icon: LargeTicaret,
            description:
                'lorem ipsum dolor sit amet consectetur adipiscing elit.'
        },
        {
            title: 'Gelişmiş Üretim',
            icon: LargeUretim,
            description:
                'lorem ipsum dolor sit amet consectetur adipiscing elit.'
        },
        {
            title: 'İş Zekası (BI)',
            icon: LargeBi,
            description:
                'lorem ipsum dolor sit amet consectetur adipiscing elit.'
        }
    ];

    // Bottom row small cards
    const bottomRowCards = [
        {},
        {
            title: 'E-Çözümler',
            icon: CrmImage,
            hoverIcon: CrmBlueImage,
            isSelected: true
        },
        { title: 'Üretim', icon: CrmImage, hoverIcon: CrmBlueImage },
        { title: 'PCM', icon: CrmImage, hoverIcon: CrmBlueImage },
        { title: 'MRP', icon: CrmImage, hoverIcon: CrmBlueImage },
        { title: 'Lojistik', icon: CrmImage, hoverIcon: CrmBlueImage },
        { title: 'Flow', icon: CrmImage, hoverIcon: CrmBlueImage },
        {
            title: 'Kalite Kontrol',
            icon: CrmImage,
            hoverIcon: CrmBlueImage
        },
        { title: 'Aktivite', icon: CrmImage, hoverIcon: CrmBlueImage },
        {}
    ];

    return (
        <section className="relative bg-[#EBF7FF] py-8">
            <div className="container grid gap-4 text-center lg:gap-6">
                <h2 className="font-means text-3xl font-light text-primary-800 lg:text-5xl">
                    {t('Bir Muhasebe  yazılımından çok daha fazlası')}
                </h2>
                <p className="text-black lg:text-base">
                    {t(
                        'EnterERP, güçlü bir muhasebe ve finansal yönetim ürünüdür ancak aynı zamanda yeteneklerini bunun ötesine taşımak isteyen şirketler için eşsiz bir ERP çözümüdür.'
                    )}
                </p>
            </div>

            {/* Feature Cards Grid */}
            <div className="mx-auto py-16 lg:container lg:px-4">
                {/* Top row - small cards */}
                <div className="mt-3 hidden grid-cols-10  gap-3 [mask-image:linear-gradient(to_bottom,rgba(0,0,0,0.0),rgba(0,0,0,100))] lg:grid">
                    {topRowCardsTop.map((card, index) => {
                        const isFirst = index === 0;
                        const isLast = index === topRowCards.length - 1;

                        const maskClass = isFirst
                            ? '[mask-image:linear-gradient(to_right,rgba(0,0,0,0.1),rgba(0,0,0,1))]'
                            : isLast
                            ? '[mask-image:linear-gradient(to_left,rgba(0,0,0,0.1),rgba(0,0,0,1))]'
                            : '';

                        return (
                            <div key={`bottom-${index}`} className={maskClass}>
                                <SmallFeatureGhostCard title={''} icon={''} />
                            </div>
                        );
                    })}
                </div>
                <div className="mt-3 grid grid-cols-5  gap-3 [mask-image:linear-gradient(to_bottom,rgba(0,0,0,0.0),rgba(0,0,0,100))] lg:hidden">
                    {topRowCardsTop.slice(0, 5).map((card, index) => {
                        const isFirst = index === 0;
                        const isLast = index === topRowCards.length - 1;

                        const maskClass = isFirst
                            ? '[mask-image:linear-gradient(to_right,rgba(0,0,0,0.1),rgba(0,0,0,1))]'
                            : isLast
                            ? '[mask-image:linear-gradient(to_left,rgba(0,0,0,0.1),rgba(0,0,0,1))]'
                            : '';

                        return (
                            <div key={`bottom-${index}`} className={maskClass}>
                                <SmallFeatureGhostCard title={''} icon={''} />
                            </div>
                        );
                    })}
                </div>
                <div className="mt-3  hidden grid-cols-10 gap-3 lg:grid ">
                    {topRowCards.map((card, index) => {
                        const isFirst = index === 0;
                        const isLast = index === topRowCards.length - 1;

                        const maskClass = isFirst
                            ? '[mask-image:linear-gradient(to_right,rgba(0,0,0,0.1),rgba(0,0,0,1))]'
                            : isLast
                            ? '[mask-image:linear-gradient(to_left,rgba(0,0,0,0.1),rgba(0,0,0,1))]'
                            : '';

                        return (
                            <div key={`bottom-${index}`} className={maskClass}>
                                <SmallFeatureCard
                                    title={card.title || ''}
                                    icon={card.icon}
                                    hoverIcon={card.hoverIcon}
                                />
                            </div>
                        );
                    })}
                </div>
                <div className="mt-3 grid  grid-cols-5 gap-3 lg:hidden ">
                    {topRowCards.slice(1, 6).map((card, index) => {
                        const isFirst = index === 0;
                        const isLast = index === topRowCards.length - 1;

                        const maskClass = isFirst
                            ? '[mask-image:linear-gradient(to_right,rgba(0,0,0,0.1),rgba(0,0,0,1))]'
                            : isLast
                            ? '[mask-image:linear-gradient(to_left,rgba(0,0,0,0.1),rgba(0,0,0,1))]'
                            : '';

                        return (
                            <div key={`bottom-${index}`} className={maskClass}>
                                <SmallFeatureCard
                                    title={card.title || ''}
                                    icon={card.icon}
                                    hoverIcon={card.hoverIcon}
                                />
                            </div>
                        );
                    })}
                </div>

                {/* Middle section with large center cards and surrounding small cards */}
                <div className="mt-3 grid grid-cols-10 gap-3">
                    {/* Left side small cards */}
                    <div className="col-span-1 -ml-10  hidden grid-cols-1 gap-3   [mask-image:linear-gradient(to_right,rgba(0,0,0,0.1),rgba(0,0,0,1))] lg:ml-0 lg:grid lg:w-full">
                        {leftSideCards.map((card, index) => (
                            <SmallFeatureCard
                                key={`left-${index}`}
                                title={card.title || ''}
                                icon={card.icon || ''}
                            />
                        ))}
                    </div>

                    {/* Center large cards - 2x2 grid */}
                    <div className="col-span-10 grid grid-cols-2 gap-3 px-2 md:col-span-8 lg:grid-cols-4 lg:px-0">
                        {centerLargeCards.map((card, index) => (
                            <LargeFeatureCard
                                key={`center-${index}`}
                                title={card.title}
                                icon={card.icon}
                                description={card.description}
                            />
                        ))}
                    </div>

                    {/* Right side small cards */}
                    <div className="col-span-1 -mr-10 hidden grid-cols-1 gap-3 [mask-image:linear-gradient(to_left,rgba(0,0,0,0.1),rgba(0,0,0,1))] lg:grid lg:w-full">
                        {rightSideCards.map((card, index) => (
                            <SmallFeatureCard
                                key={`right-${index}`}
                                title={card.title || ''}
                                icon={card.icon || ''}
                            />
                        ))}
                    </div>
                </div>
                {/* Bottom row - small cards */}
                <div className="mt-3 hidden grid-cols-10 gap-3 lg:grid ">
                    {bottomRowCards.map((card, index) => {
                        const isFirst = index === 0;
                        const isLast = index === bottomRowCards.length - 1;

                        const maskClass = isFirst
                            ? '[mask-image:linear-gradient(to_right,rgba(0,0,0,0.1),rgba(0,0,0,1))]'
                            : isLast
                            ? '[mask-image:linear-gradient(to_left,rgba(0,0,0,0.1),rgba(0,0,0,1))]'
                            : '';

                        return (
                            <div key={`bottom-${index}`} className={maskClass}>
                                <SmallFeatureCard
                                    title={card.title || ''}
                                    icon={card.icon}
                                    hoverIcon={card.hoverIcon}
                                />
                            </div>
                        );
                    })}
                </div>
                <div className="mt-3 grid grid-cols-5  gap-3 lg:hidden ">
                    {bottomRowCards.slice(1, 6).map((card, index) => {
                        const isFirst = index === 0;
                        const isLast = index === bottomRowCards.length - 1;

                        const maskClass = isFirst
                            ? '[mask-image:linear-gradient(to_right,rgba(0,0,0,0.1),rgba(0,0,0,1))]'
                            : isLast
                            ? '[mask-image:linear-gradient(to_left,rgba(0,0,0,0.1),rgba(0,0,0,1))]'
                            : '';

                        return (
                            <div key={`bottom-${index}`} className={maskClass}>
                                <SmallFeatureCard
                                    title={card.title || ''}
                                    icon={card.icon}
                                    hoverIcon={card.hoverIcon}
                                />
                            </div>
                        );
                    })}
                </div>
                <div className="mt-3 hidden grid-cols-10  gap-3 [mask-image:linear-gradient(to_top,rgba(0,0,0,0.0),rgba(0,0,0,100))] lg:grid">
                    {topRowCardsTop.map((card, index) => {
                        const isFirst = index === 0;
                        const isLast = index === topRowCards.length - 1;

                        const maskClass = isFirst
                            ? '[mask-image:linear-gradient(to_right,rgba(0,0,0,0.1),rgba(0,0,0,1))]'
                            : isLast
                            ? '[mask-image:linear-gradient(to_left,rgba(0,0,0,0.1),rgba(0,0,0,1))]'
                            : '';

                        return (
                            <div key={`bottom-${index}`} className={maskClass}>
                                <SmallFeatureGhostCard title={''} icon={''} />
                            </div>
                        );
                    })}
                </div>
                <div className="mt-3 grid grid-cols-5  gap-3 [mask-image:linear-gradient(to_top,rgba(0,0,0,0.0),rgba(0,0,0,100))] lg:hidden">
                    {topRowCardsTop.slice(0, 5).map((card, index) => {
                        const isFirst = index === 0;
                        const isLast = index === topRowCards.length - 1;

                        const maskClass = isFirst
                            ? '[mask-image:linear-gradient(to_right,rgba(0,0,0,0.1),rgba(0,0,0,1))]'
                            : isLast
                            ? '[mask-image:linear-gradient(to_left,rgba(0,0,0,0.1),rgba(0,0,0,1))]'
                            : '';

                        return (
                            <div key={`bottom-${index}`} className={maskClass}>
                                <SmallFeatureGhostCard title={''} icon={''} />
                            </div>
                        );
                    })}
                </div>
            </div>

            {/* <svg
                xmlns="http://www.w3.org/2000/svg"
                viewBox="0 0 760.53 646.4"
                className="absolute -bottom-[1px] right-0 hidden w-1/5 fill-white lg:block"
            >
                <path d="M760.53,0c-46.8,30.34-102.62,47.96-162.54,47.96-165.24,0-299.22,133.98-299.22,299.22S165.03,646.16,0,646.4H760.52" />
            </svg>

            <svg
                xmlns="http://www.w3.org/2000/svg"
                viewBox="0 0 417.96 520.7"
                className="absolute bottom-0 right-0 hidden w-[11%] fill-primary-50 lg:block"
            >
                <path d="M416.99,516.57c-9.63-1.87-19.63-2.63-29.87-2.14l-124.9,5.98C124.16,527.02,6.89,420.66,.29,282.84-6.32,144.85,100.5,27.67,238.74,21.3l124.3-5.7c19.95-.91,38.59-6.48,54.91-15.61" />
            </svg> */}
        </section>
    );
};

export default SectionFour;
