// import Hero from '@public/images/pages/home/<USER>';
import Hero from '@public/images/pages/home/<USER>';
import {
    ExtendedImage,
    GhostButton,
    SolidButton
} from '@src/components/shared';
import { useTrans } from '@src/hooks';

const SectionOne = () => {
    const t = useTrans();

    return (
        <section className="home-page-hero relative ">
            <svg
                xmlns="http://www.w3.org/2000/svg"
                viewBox="0 0 433.26 420.43"
                className="absolute -bottom-[1px] right-0 hidden w-1/5 fill-white xl:block"
            >
                <path d="M433.23 0c-4.48.29-9 .43-13.55.43-115.97 0-210 94.03-210 210S115.82 420.26 0 420.43h433.26" />
            </svg>

            <div className="container flex flex-col items-center gap-4 lg:flex-row lg:justify-between lg:gap-12">
                <div className="grid gap-8 lg:w-4/12 lg:gap-14">
                    <div className="flex items-center justify-center gap-4 lg:justify-start">
                        <span className="rounded bg-[#0065FF] px-2 py-1 text-xs font-normal uppercase leading-4 text-white">
                            {t('YENİ')}
                        </span>
                        <p className="text-xs tracking-[3px] text-[#0048FF] md:text-sm">
                            {t('En yeni ve en iyisine EnterERP ile sahip olun')}
                        </p>
                    </div>
                    <p className="hidden space-y-3 whitespace-nowrap text-center font-means text-5xl text-primary-800 md:block lg:space-y-6 lg:text-left 2xl:text-5xl">
                        <span className="block">
                            {t("Türkiye'nin En Kapsamlı ve")}
                        </span>
                        <span className="block">
                            {t('En Hızlı Bulut ERP Sistemi')}
                        </span>
                    </p>
                    <h1 className="text-center font-means text-4xl !leading-snug text-white sm:text-5xl md:hidden [@media(max-width:360px)]:text-3xl">
                        {t(
                            "Türkiye'nin En Kapsamlı ve En Hızlı Bulut ERP Sistemi"
                        )}
                    </h1>
                    <p className="text-center text-black lg:text-left lg:text-xl lg:leading-relaxed">
                        {t(
                            'Gerçek Zamanlı Bulut Tabanlı Kurumsal Kaynak Planlama Yazılımı ile işinizi bir adım öteye taşıyın'
                        )}
                    </p>
                    <div className="flex flex-col items-center gap-6 lg:flex-row">
                        <SolidButton variant="white" href="/signup">
                            {t('Ücretsiz Dene')}
                        </SolidButton>

                        <GhostButton
                            href="/appointment"
                            color="white"
                            className="font-normal text-primary-800 max-lg:ml-5"
                        >
                            {t('Randevu Talep Et')}
                        </GhostButton>
                    </div>

                    <p className="text-center text-sm text-black lg:text-left">
                        {t('Benzersiz Hız')} &nbsp; &middot; &nbsp;{' '}
                        {t('Kusursuz Kullanım Kolaylığı')} &nbsp;{' '}
                        <br className="sm:hidden" /> &middot; &nbsp;{' '}
                        {t('Sınır Tanımayan Özellikler')}
                    </p>
                </div>

                <ExtendedImage
                    src={Hero}
                    alt={t('Ana sayfa hero resim')}
                    className="mx-auto w-full max-w-lg lg:mx-0 lg:ml-auto 2xl:max-w-3xl"
                    sizes="(max-width: 1024px) 70vw, 35vw"
                    quality={85}
                    priority
                />
            </div>
        </section>
    );
};

export default SectionOne;
