import { useState } from 'react';
import { useTrans } from '@src/hooks';

interface FilterSidebarProps {
    selectedFilters: {
        collections: string[];
        useCases: string[];
        industries: string[];
    };
    onFiltersChange: (filters: any) => void;
}

const FilterSidebar = ({ selectedFilters, onFiltersChange }: FilterSidebarProps) => {
    const t = useTrans();
    const [isCollectionOpen, setIsCollectionOpen] = useState(true);

    const collections = [
        'Integration',
        'MongoDB & Applications Program',
        'MongoDB Storage Program',
        'Popular',
        'Trending'
    ];

    const useCases = [
        'AI/ML',
        'Analytics',
        'Catalog Management',
        'Content Management',
        'Customer Data'
    ];

    const industries = [
        'Automotive & Manufacturing',
        'Education',
        'Financial Services'
    ];

    const handleFilterChange = (category: string, value: string, checked: boolean) => {
        const newFilters = { ...selectedFilters };
        if (checked) {
            newFilters[category] = [...newFilters[category], value];
        } else {
            newFilters[category] = newFilters[category].filter(item => item !== value);
        }
        onFiltersChange(newFilters);
    };

    return (
        <div className="bg-white rounded-lg p-6 shadow-sm border">
            <h2 className="text-lg font-semibold text-gray-900 mb-6">Filter by</h2>
            
            {/* Collection Section */}
            <div className="mb-8">
                <div 
                    className="flex items-center justify-between cursor-pointer mb-4"
                    onClick={() => setIsCollectionOpen(!isCollectionOpen)}
                >
                    <h3 className="text-sm font-medium text-gray-700">Collection</h3>
                    <svg 
                        className={`w-4 h-4 transition-transform ${isCollectionOpen ? 'rotate-180' : ''}`}
                        fill="none" 
                        stroke="currentColor" 
                        viewBox="0 0 24 24"
                    >
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                    </svg>
                </div>
                
                {isCollectionOpen && (
                    <div className="space-y-3">
                        {collections.map((collection) => (
                            <label key={collection} className="flex items-center">
                                <input
                                    type="checkbox"
                                    className="w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
                                    checked={selectedFilters.collections.includes(collection)}
                                    onChange={(e) => handleFilterChange('collections', collection, e.target.checked)}
                                />
                                <span className="ml-3 text-sm text-gray-600">{collection}</span>
                            </label>
                        ))}
                    </div>
                )}
            </div>

            {/* Use Cases Section */}
            <div className="mb-8">
                <h3 className="text-sm font-medium text-gray-700 mb-4">Use Cases</h3>
                <div className="space-y-3">
                    {useCases.map((useCase) => (
                        <label key={useCase} className="flex items-center">
                            <input
                                type="checkbox"
                                className="w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
                                checked={selectedFilters.useCases.includes(useCase)}
                                onChange={(e) => handleFilterChange('useCases', useCase, e.target.checked)}
                            />
                            <span className="ml-3 text-sm text-gray-600">{useCase}</span>
                        </label>
                    ))}
                </div>
                <button className="text-blue-600 text-sm mt-3 hover:text-blue-800">
                    Show more
                </button>
            </div>

            {/* Industries Section */}
            <div>
                <h3 className="text-sm font-medium text-gray-700 mb-4">Industries</h3>
                <div className="space-y-3">
                    {industries.map((industry) => (
                        <label key={industry} className="flex items-center">
                            <input
                                type="checkbox"
                                className="w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
                                checked={selectedFilters.industries.includes(industry)}
                                onChange={(e) => handleFilterChange('industries', industry, e.target.checked)}
                            />
                            <span className="ml-3 text-sm text-gray-600">{industry}</span>
                        </label>
                    ))}
                </div>
            </div>
        </div>
    );
};

export default FilterSidebar;
