import { useState } from 'react';
import IntegrationGrid from './IntegrationGrid';
import EcosystemSection from './EcosystemSection';

const Integrations = () => {
    const [searchTerm, setSearchTerm] = useState('');
    const [selectedFilters, setSelectedFilters] = useState({
        collections: [],
        useCases: [],
        industries: []
    });

    return (
        <div className="min-h-screen bg-gray-50">
            <EcosystemSection />
        </div>
    );
};

export default Integrations;
