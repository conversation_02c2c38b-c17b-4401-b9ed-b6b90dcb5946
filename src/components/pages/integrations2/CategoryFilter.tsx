import { useTrans } from '@src/hooks';
import { twMerge } from '@src/utils';

interface CategoryFilterProps {
    categories: string[];
    selectedCategory: string;
    onCategoryChange: (category: string) => void;
}

const CategoryFilter = ({ categories, selectedCategory, onCategoryChange }: CategoryFilterProps) => {
    const t = useTrans();

    return (
        <div className="flex flex-wrap justify-center gap-3">
            {categories.map((category) => (
                <button
                    key={category}
                    onClick={() => onCategoryChange(category)}
                    className={twMerge(
                        'px-6 py-3 rounded-full text-sm font-medium transition-all duration-200 border',
                        selectedCategory === category
                            ? 'bg-primary-500 text-white border-primary-500 shadow-md'
                            : 'bg-white text-accent-200 border-gray-300 hover:border-primary-300 hover:text-primary-500 hover:shadow-sm'
                    )}
                >
                    {t(category)}
                </button>
            ))}
        </div>
    );
};

export default CategoryFilter;
