import { useTrans } from '@src/hooks';

interface SearchBarProps {
    searchTerm: string;
    onSearchChange: (term: string) => void;
}

const SearchBar = ({ searchTerm, onSearchChange }: SearchBarProps) => {
    const t = useTrans();

    return (
        <div className="relative mx-auto max-w-2xl">
            <div className="relative">
                <div className="pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3">
                    <svg
                        className="h-5 w-5 text-accent-200"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                    >
                        <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth={2}
                            d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
                        />
                    </svg>
                </div>
                <input
                    type="text"
                    value={searchTerm}
                    onChange={(e) => onSearchChange(e.target.value)}
                    className="block w-full rounded-4xl border border-gray-300 bg-white py-4 pl-10 pr-3 text-lg leading-5 placeholder-accent-200 focus:border-primary-500 focus:placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-primary-500"
                    placeholder={t(
                        'Search partners, technologies, or solutions...'
                    )}
                />
                {searchTerm && (
                    <div className="absolute inset-y-0 right-0 flex items-center pr-3">
                        <button
                            onClick={() => onSearchChange('')}
                            className="text-accent-200 hover:text-gray-600 focus:outline-none"
                        >
                            <svg
                                className="h-5 w-5"
                                fill="none"
                                stroke="currentColor"
                                viewBox="0 0 24 24"
                            >
                                <path
                                    strokeLinecap="round"
                                    strokeLinejoin="round"
                                    strokeWidth={2}
                                    d="M6 18L18 6M6 6l12 12"
                                />
                            </svg>
                        </button>
                    </div>
                )}
            </div>
        </div>
    );
};

export default SearchBar;
