import { useTrans } from '@src/hooks';

interface Integration {
    id: number;
    name: string;
    description: string;
    logo: string;
    category: string;
    tags: string[];
    bgColor: string;
}

interface IntegrationCardProps {
    integration: Integration;
}

const IntegrationCard = ({ integration }: IntegrationCardProps) => {
    const t = useTrans();

    return (
        <div className="bg-white rounded-lg border border-gray-200 p-6 hover:shadow-lg transition-shadow duration-200 cursor-pointer">
            {/* Logo */}
            <div className="mb-4">
                <div className={`w-12 h-12 ${integration.bgColor} rounded-lg flex items-center justify-center text-white font-bold text-lg`}>
                    {integration.logo}
                </div>
            </div>

            {/* Company Name */}
            <h3 className="text-lg font-semibold text-gray-900 mb-2">
                {integration.name}
            </h3>

            {/* Description */}
            <p className="text-sm text-gray-600 mb-4 line-clamp-3">
                {integration.description}
            </p>

            {/* Tags */}
            <div className="flex flex-wrap gap-2">
                {integration.tags.map((tag, index) => (
                    <span
                        key={index}
                        className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800"
                    >
                        {tag}
                    </span>
                ))}
            </div>
        </div>
    );
};

export default IntegrationCard;
