import dynamic from 'next/dynamic';
import { FormProvider, useForm } from 'react-hook-form';
import { GhostButton } from '@src/components/shared';
import {
    Input,
    Phone,
    PrivacyManifest,
    SubmitPartial,
    TermsManifest,
    Textarea
} from '@src/components/shared/Form';
import { useSubmit, useTrans } from '@src/hooks';
import { FormValues } from '@src/interfaces';

const Toast = dynamic(() => import('@src/components/shared/Form/Toast'));

const Form = () => {
    const t = useTrans();

    const methods = useForm<FormValues>({ mode: 'onChange' });

    const { isSubmitting, status, showToast, onSubmit } = useSubmit({
        methods
    });

    return (
        <div className="flex flex-col items-start justify-center gap-8 bg-white py-12 lg:pl-12">
            <div className="mx-auto grid w-11/12 gap-8 lg:mx-0 2xl:w-8/12">
                <p className="text-center font-means text-5xl">{t('Kaydol')}</p>
                <p className="mx-auto text-center text-lg text-accent-200 lg:w-8/12">
                    {t(
                        'EnterERP hakkında daha fazla bilgi almak istiyorsanız formu doldurabilir veya bu numaradan bizi arayabilirsiniz. 0 850 582 00 35'
                    )}
                </p>
            </div>

            <Toast
                showToast={showToast}
                status={status}
                successMessage="Kaydınız başarıyla gerçekleşti."
                errorMessage="Bir şeyler ters gitti. Lütfen daha sonra tekrar deneyin."
            />

            <FormProvider {...methods}>
                <form
                    className="signup-form mx-auto flex w-11/12 flex-col justify-between gap-7 lg:mx-0 2xl:w-8/12"
                    onSubmit={methods.handleSubmit(onSubmit)}
                >
                    <fieldset
                        disabled={methods.formState.isSubmitting}
                        className="grid w-full gap-7 lg:grid-cols-2"
                    >
                        <Input name="name" label="Ad *" required />
                        <Input name="surname" label="Soyad *" required />
                        <Phone />
                        <Input name="mail" label="E-Posta (Opsiyonel)" />
                        <div className="lg:col-span-2">
                            <Input
                                name="company"
                                label="İşletme Adı *"
                                required
                            />
                        </div>
                    </fieldset>

                    <Textarea
                        name="description"
                        rows={5}
                        placeholder={t(
                            'Size nasıl yardımcı olabileceğimiz hakkında daha detaylı bilgi verin.'
                        )}
                    />

                    <PrivacyManifest />

                    <TermsManifest />

                    <SubmitPartial
                        isSubmitting={isSubmitting}
                        title="Gönder"
                        type="submit"
                        id="signup-submit"
                    />

                    <div className="mx-auto">
                        <GhostButton
                            color="dark"
                            href="/appointment"
                            className="ml-5"
                        >
                            {t('Randevu Talep Et')}
                        </GhostButton>
                    </div>
                </form>
            </FormProvider>
        </div>
    );
};

export default Form;
