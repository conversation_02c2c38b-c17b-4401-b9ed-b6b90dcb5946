import HeroImage from '@public/images/pages/signup/Hero.svg';
import SiteLogo from '@public/images/shared/site-logo.svg';
import { ExtendedImage, ExtendedLink } from '@src/components/shared';
import { useTrans } from '@src/hooks';
import Form from './Form';

const Signup = () => {
    const t = useTrans();

    return (
        <section className="relative grid xl:grid-cols-2">
            <svg
                xmlns="http://www.w3.org/2000/svg"
                viewBox="0 0 424.37 226.65"
                className="absolute left-0 top-0 w-56 fill-white lg:w-72"
            >
                <path d="M.16 0h424.21s-21.43 58.81-80.38 102.45c0 0-63.65 55.11-156 53.55 0 0-76.16-5.47-151.04 41.88 0 0-24.81 16.66-36.95 28.78L.16 0Z" />
            </svg>

            <div className="absolute left-0 top-0 pl-5 pt-5">
                <ExtendedLink href="/">
                    <ExtendedImage
                        src={SiteLogo}
                        alt={t('EnterERP Site Logo')}
                        className="w-36 select-none lg:w-48"
                        priority
                    />
                </ExtendedLink>
            </div>

            <div className="container mx-auto mt-24 flex flex-col gap-8 lg:mt-12">
                <h1 className="z-10 mx-auto text-center font-means text-5xl lg:w-1/2">
                    {t('Ücretsiz denemenizi bugün başlatın')}
                </h1>
                <p className="mx-auto text-center text-accent-200 lg:w-1/2">
                    {t(
                        'EnterERP ücretsiz denemenizin kilidini açın ve önümüzdeki 7 gün boyunca kurumsal kaynak planlama otomasyonunun gücünü kullanın.'
                    )}
                </p>
                <strong className="text-center font-means text-lg max-xl:mb-8">
                    <q>{t('Kredi Kartı Gerekmez')}</q>
                </strong>
                <div className="mt-auto hidden xl:block">
                    <ExtendedImage
                        src={HeroImage}
                        alt={t('Ücretsiz dene')}
                        className="mx-auto w-full select-none"
                        priority
                    />
                </div>
            </div>

            <Form />
        </section>
    );
};

export default Signup;
