import React from 'react';
import {
    Accordion,
    AccordionItem,
    AccordionItemButton,
    AccordionItemHeading,
    AccordionItemPanel
} from 'react-accessible-accordion';
import { FeatureTabType } from '@src/interfaces';
import ExtendedImage from '../ExtendedImage';
import TabPartial from './TabPartial';

interface FeatureTabProps {
    tabData: FeatureTabType[];
}

const FeatureTab = ({ tabData }: FeatureTabProps) => {
    return (
        <div className="flex gap-16">
            <div className="relative w-full lg:w-5/12">
                <Accordion preExpanded={[0]}>
                    {tabData.map((accordionItem, index) => {
                        const haveTabs =
                            accordionItem.disclosureTabs.length > 1;

                        return (
                            <AccordionItem
                                key={accordionItem.disclosureTitle}
                                uuid={index}
                                className="border-b border-gray-800 pb-4"
                            >
                                <AccordionItemHeading>
                                    <AccordionItemButton>
                                        <h3 className="w-10/12 font-means font-light">
                                            {accordionItem.disclosureTitle}
                                        </h3>
                                    </AccordionItemButton>
                                </AccordionItemHeading>
                                <AccordionItemPanel>
                                    <p className="text-accent-100">
                                        {accordionItem.disclosureDescription}
                                    </p>

                                    <div className="lg:absolute lg:-top-32 lg:left-full lg:w-full lg:translate-x-[42%]">
                                        {haveTabs ? (
                                            <TabPartial
                                                tabItems={
                                                    accordionItem.disclosureTabs
                                                }
                                            />
                                        ) : (
                                            <ExtendedImage
                                                src={
                                                    accordionItem
                                                        .disclosureTabs[0]
                                                        .tabImage
                                                }
                                                alt={
                                                    accordionItem
                                                        .disclosureTabs[0]
                                                        .tabTitle
                                                }
                                                className="mt-32"
                                            />
                                        )}
                                    </div>
                                </AccordionItemPanel>
                            </AccordionItem>
                        );
                    })}
                </Accordion>
            </div>
        </div>
    );
};

export default FeatureTab;
