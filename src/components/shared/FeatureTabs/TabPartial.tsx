import { Tab } from '@headlessui/react';
import { Fragment } from 'react';
import { twMerge } from '@src/utils';
import { ExtendedImage } from '..';

interface TabPartialProps {
    tabItems: {
        tabTitle: string;
        tabImage: any;
    }[];
}

const TabPartial = ({ tabItems }: TabPartialProps) => {
    return (
        <Tab.Group>
            <Tab.List className="mt-8 flex flex-wrap items-center justify-start gap-4 lg:mt-28">
                {tabItems.map((item) => (
                    <Tab as={Fragment} key={item.tabTitle}>
                        {({ selected }) => (
                            <button
                                className={twMerge(
                                    'whitespace-nowrap rounded-full border border-transparent bg-primary-700 px-6 py-2 text-white hover:border-primary-500',
                                    selected && 'bg-primary-500'
                                )}
                            >
                                {item.tabTitle}
                            </button>
                        )}
                    </Tab>
                ))}
            </Tab.List>
            <Tab.Panels>
                {tabItems.map((item) => (
                    <Tab.Panel key={item.tabTitle} className="w-full">
                        <ExtendedImage
                            src={item.tabImage}
                            alt={item.tabTitle}
                            className="mt-8"
                        />
                    </Tab.Panel>
                ))}
            </Tab.Panels>
        </Tab.Group>
    );
};

export default TabPartial;
