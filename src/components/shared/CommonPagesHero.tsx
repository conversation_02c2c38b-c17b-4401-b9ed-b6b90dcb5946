import { StaticImageData } from 'next/image';
import {
    ExtendedImage,
    GhostButton,
    SolidButton
} from '@src/components/shared';
import { useTrans } from '@src/hooks';

interface CommonPagesHeroProps {
    info: string;
    title: string;
    description: string;
    image: StaticImageData | string;
}

const CommonPagesHero = ({
    title,
    description,
    info,
    image
}: CommonPagesHeroProps) => {
    const t = useTrans();

    return (
        <section className="border-b">
            <div className="spacer container flex flex-col items-center justify-between gap-8 lg:flex-row">
                <div className="grid gap-6 md:gap-8 lg:w-1/2">
                    <span className="text-xs tracking-widest text-primary-400">
                        {t(info)}
                    </span>
                    <h1 className="font-means text-4xl md:text-5xl">
                        {t(title)}
                    </h1>
                    <p className="text-lg text-accent-200">{t(description)}</p>
                    <div className="flex flex-col items-center gap-6 md:mt-8 md:flex-row">
                        <SolidButton href="/signup">
                            {t('Ücretsiz Dene')}
                        </SolidButton>
                        <GhostButton
                            href="/appointment"
                            color="dark"
                            className="max-md:ml-5"
                        >
                            {t('Randevu Talep Et')}
                        </GhostButton>
                    </div>
                </div>
                <div className="grid max-w-xl lg:aspect-1 lg:place-content-center">
                    <ExtendedImage
                        priority
                        src={image}
                        alt={t(title)}
                        quality={90}
                        sizes="(max-width: 1024px) 100vw, 33vw"
                    />
                </div>
            </div>
        </section>
    );
};

export default CommonPagesHero;
