import React, { useState } from 'react';
import { useTrans } from '@src/hooks';
import { ChevronLeftIcon, ChevronRightIcon } from '@src/icons/solid';
import { ReviewSliderType } from '@src/interfaces';
import ExtendedImage from './ExtendedImage';
import GhostButton from './GhostButton';

interface ReviewSliderProps {
    sliderData: ReviewSliderType[];
}

const ReviewSlider = ({ sliderData }: ReviewSliderProps) => {
    const [activeSlide, setActiveSlide] = useState(0);

    const t = useTrans();

    const prevSlideHandler = () => {
        setActiveSlide((prev) => {
            if (prev === 0) return sliderData.length - 1;
            return prev - 1;
        });
    };

    const nextSlideHandler = () => {
        setActiveSlide((prev) => {
            if (prev + 1 === sliderData.length) return 0;
            return prev + 1;
        });
    };

    return (
        <ul className="card-shadow ml-auto mt-14 rounded-4xl border bg-white p-6 lg:w-10/12 lg:p-20">
            {sliderData.map((slide, index) => {
                return (
                    activeSlide === index && (
                        <React.Fragment key={slide.title}>
                            <li className="mt-36 grid gap-6 xs:mt-48 lg:!mt-0 lg:ml-24 lg:gap-10">
                                <span className="text-xs tracking-widest text-primary-400">
                                    {t(slide.title)}
                                </span>
                                <blockquote className="text-lg leading-relaxed lg:text-3xl">
                                    &ldquo;{t(slide.quote)}&rdquo;
                                </blockquote>
                                <div className="grid gap-2 lg:gap-0">
                                    <p className="text-sm lg:text-lg">
                                        {t(slide.name)}
                                    </p>
                                    <p className="text-sm text-primary-200 lg:text-lg">
                                        {t(slide.role)}
                                    </p>
                                </div>
                                <div className="flex items-center justify-between gap-2">
                                    <GhostButton color="dark" href={slide.href}>
                                        {t('Tüm hikâyeyi oku')}
                                    </GhostButton>

                                    <div className="grid grid-cols-2 gap-3">
                                        <button
                                            className="flex h-10 w-10 items-center justify-center rounded-full border-2 border-primary-400 transition-opacity duration-200 hover:opacity-40"
                                            onClick={prevSlideHandler}
                                            aria-label="Prev Slide"
                                        >
                                            <ChevronLeftIcon className="h-5 w-5" />
                                        </button>
                                        <button
                                            className="flex h-10 w-10 items-center justify-center rounded-full border-2 border-primary-400 transition-opacity duration-200 hover:opacity-40"
                                            onClick={nextSlideHandler}
                                            aria-label="Next Slide"
                                        >
                                            <ChevronRightIcon className="h-5 w-5" />
                                        </button>
                                    </div>
                                </div>
                                <ExtendedImage
                                    src={slide.image}
                                    alt={t(slide.title)}
                                    containerClassName="absolute left-6 top-12 w-7/12 max-w-[240px] rounded-4xl border card-shadow lg:left-0 lg:top-28 lg:w-3/12 lg:max-w-xs h-auto aspect-1 flex items-center bg-white px-12"
                                />
                            </li>
                        </React.Fragment>
                    )
                );
            })}
        </ul>
    );
};

export default ReviewSlider;
