import NextImage, { ImageProps, StaticImageData } from 'next/image';
import { twMerge } from '@src/utils';

interface ExtendedImageProps extends ImageProps {
    src: string | StaticImageData;
    alt: string;
    containerClassName?: string;
}

const ExtendedImage = ({
    src,
    alt,
    containerClassName,
    ...rest
}: ExtendedImageProps) => {
    return (
        <div className={twMerge('relative h-full w-full', containerClassName)}>
            <NextImage
                src={src}
                alt={alt}
                sizes="(max-width: 1024px) 100vw, 50vw"
                {...rest}
            />
        </div>
    );
};

export default ExtendedImage;
