import { twMerge } from 'tailwind-merge';
import ManWithBlueBg from '@public/images/shared/ManWithBlueBg.png';
import ManWithYellowBg from '@public/images/shared/ManWithYellowBg.png';
import { useTrans } from '@src/hooks';
import ExtendedImage from './ExtendedImage';
import GhostButton from './GhostButton';
import SolidButton from './SolidButton';

interface DemoSectionProps {
    variant?: 'yellow' | 'blue';
}

const DemoSection = ({ variant = 'yellow' }: DemoSectionProps) => {
    const t = useTrans();

    return (
        <div
            className={twMerge(
                'card-shadow container relative grid place-items-center gap-6 overflow-hidden rounded-4xl bg-[#FDF8EA] py-8 max-lg:px-4 lg:py-16',
                variant === 'blue' && 'bg-white'
            )}
        >
            <h3 className="text-center font-means text-3xl font-light lg:text-5xl lg:leading-tight">
                {t('Hemen ücretsiz deneyin!')}
            </h3>
            <p className="text-center text-lg text-accent-200">
                {t(
                    'Kredi kartı gerekmeden ücretsiz deneyin, memnun kalırsanız abone olun.'
                )}
            </p>
            <div className="flex flex-col items-center justify-center gap-6 lg:flex-row">
                <SolidButton
                    variant="dark"
                    href="/signup"
                    className={twMerge(
                        variant === 'yellow' &&
                            'border-transparent bg-[#FEE785] text-black'
                    )}
                >
                    {t('Ücretsiz Dene')}
                </SolidButton>
                <GhostButton
                    href="/appointment"
                    color="dark"
                    className="max-lg:ml-5"
                >
                    {t('Randevu Talep Et')}
                </GhostButton>
            </div>
            <svg
                className={twMerge(
                    'absolute right-0 top-0 hidden w-1/4 fill-[#FEE785] lg:block',
                    variant === 'blue' && 'fill-primary-100'
                )}
                viewBox="0 0 441 380"
                xmlns="http://www.w3.org/2000/svg"
            >
                <path d="M440.217 380V0H0C5.02146 58.8301 54.1376 105.191 114.287 105.619L118.25 105.653C181.722 106.106 232.959 157.723 232.959 221.271C232.959 285.131 284.683 336.888 348.503 336.888H350.206C386.587 336.888 419.038 353.707 440.217 380Z" />
            </svg>
            <div className="absolute bottom-0 left-0 hidden w-[360px] xl:block">
                <ExtendedImage
                    src={variant === 'yellow' ? ManWithYellowBg : ManWithBlueBg}
                    alt={t('İnsan resmi')}
                />
            </div>
        </div>
    );
};

export default DemoSection;
