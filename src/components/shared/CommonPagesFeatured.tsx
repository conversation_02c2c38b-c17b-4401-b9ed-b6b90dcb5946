import { useRef } from 'react';
import { useSectionIntersect, useTrans } from '@src/hooks';
import { FeatureTabType, TabEnum } from '@src/interfaces';
import FeatureTab from './FeatureTabs/FeatureTab';

interface CommonPagesFeaturedProps {
    id: TabEnum;
    title: string;
    tabData: FeatureTabType[];
    intersectionHandler?: (activeTab: TabEnum) => void;
}

const CommonPagesFeatured = ({
    id,
    title,
    tabData,
    intersectionHandler
}: CommonPagesFeaturedProps) => {
    const t = useTrans();

    const ref = useRef<HTMLElement>(null);

    useSectionIntersect({ ref, id, intersectionHandler });

    return (
        <section
            id={id}
            ref={ref}
            className="relative bg-yellow-50 py-20 lg:py-32 2xl:py-60"
        >
            <svg
                className="absolute left-0 right-0 top-0 w-full"
                viewBox="0 0 2160 167"
                xmlns="http://www.w3.org/2000/svg"
            >
                <path
                    d="M2159.5 0H-5v148c422.5 68 608.374-77.91 1105-81.499 583.18-4.214 607 159.999 1059.5 49.999V0Z"
                    fill="#fff"
                />
            </svg>
            <svg
                className="absolute bottom-0 left-0 right-0 w-full"
                viewBox="0 0 2160 167"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
            >
                <path
                    d="M-5 166.038h2164.5v-148c-422.5-68-608.37 77.91-1105 81.499C471.318 103.75 447.5-60.462-5 49.538v116.5Z"
                    fill="#fff"
                />
            </svg>

            <div className="container">
                <h2 className="mx-auto pb-12 text-center font-means text-3xl font-light lg:w-8/12 lg:text-5xl">
                    {t(title)}
                </h2>

                <div className="relative lg:mb-16 [&>div>div>div>div]:border-yellow-600 [&>div>div>div>div]:border-opacity-50 [&>div>div>div>div]:py-2 [&_h3]:text-primary-800 max-lg:[&_img]:mt-8 [&_p]:text-primary-800">
                    <FeatureTab tabData={tabData} />
                </div>
            </div>
        </section>
    );
};

export default CommonPagesFeatured;
