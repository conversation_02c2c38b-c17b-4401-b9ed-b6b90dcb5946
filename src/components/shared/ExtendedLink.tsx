import Link from 'next/link';
import { ReactNode } from 'react';

interface ExtendedLinkProps
    extends React.AnchorHTMLAttributes<HTMLAnchorElement> {
    children: ReactNode;
    href: string;
    className?: string;
    scroll?: boolean;
}

const ExtendedLink = ({
    children,
    href,
    className,
    scroll = true,
    ...rest
}: ExtendedLinkProps) => {
    return (
        <Link
            scroll={scroll}
            href={href}
            className={className}
            prefetch={false}
            {...rest}
        >
            {children}
        </Link>
    );
};

export default ExtendedLink;
