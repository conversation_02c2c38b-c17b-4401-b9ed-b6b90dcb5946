import { ReactNode } from 'react';
import { twMerge } from '@src/utils';
import ExtendedLink from './ExtendedLink';

interface SolidButtonProps {
    children: ReactNode;
    href: string;
    className?: string;
    variant?: 'dark' | 'white';
}

const SolidButton = ({
    children,
    href,
    className,
    variant = 'dark'
}: SolidButtonProps) => {
    return (
        <div className="group inline-block">
            <ExtendedLink
                className={twMerge(
                    'btn-transition inline-block cursor-pointer whitespace-nowrap rounded-[100vw] border border-[#011D2A] bg-primary-200 px-8 py-4 font-medium leading-4 text-white group-hover:-translate-y-1 group-hover:shadow-[0px_6px_0px_0px_#011D2A]',
                    variant === 'dark'
                        ? 'group-hover:shadow-[0px_6px_0px_0px_#011D2A]'
                        : 'group-hover:shadow-[0px_6px_0px_0px_#ffffff]',
                    className
                )}
                href={href}
            >
                {children}
            </ExtendedLink>
        </div>
    );
};

export default SolidButton;
