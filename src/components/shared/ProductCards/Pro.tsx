import { useTrans } from '@src/hooks';
import GhostButton from '../GhostButton';

const Pro = () => {
    const t = useTrans();

    return (
        <article className="card-shadow flex flex-col justify-start rounded-4xl border bg-white p-8 lg:p-16">
            <div className="mb-6 grid content-start gap-8">
                <h2>
                    <span className="block font-means text-3xl font-light lg:text-4xl">
                        {t('EnterERP')}
                    </span>
                    <span className="mt-2 block font-means text-3xl font-light text-primary-500 lg:text-4xl">
                        {t('Pro')}
                    </span>
                </h2>
                <span className="text-xs tracking-widest text-primary-400">
                    {t('YÜKSELİŞ')}
                </span>
                <p className="text-accent-200 lg:text-xl xl:w-1/2">
                    {t(
                        'EnterERP Pro, orta ve büyük ölçekli işletmelerin büyümelerini ve sürekli gelişen ihtiyaçlarını karşılayarak sürdürülebilir hale getirmek için geliştirildi.'
                    )}
                </p>
            </div>
            <div className="mt-auto">
                <GhostButton color="dark" href="/pricing">
                    {t('İnceleyin')}
                </GhostButton>
            </div>
        </article>
    );
};

export default Pro;
