import EleganceImage from '@public/images/shared/Elegance.svg';
import { useTrans } from '@src/hooks';
import ExtendedImage from '../ExtendedImage';
import GhostButton from '../GhostButton';

const Elegance = () => {
    const t = useTrans();

    return (
        <article className="card-shadow relative flex flex-col items-center gap-6 overflow-hidden rounded-4xl border bg-white py-8 md:flex-row lg:py-16">
            <div className="flex w-full flex-col justify-start px-8 lg:pl-16">
                <div className="mb-6 grid gap-8">
                    <h2 className="font-means text-3xl font-light lg:text-4xl">
                        {t('EnterERP')}{' '}
                        <span className="text-primary-500">
                            {t('Elegance')}
                        </span>
                    </h2>
                    <span className="text-xs tracking-widest text-primary-400">
                        {t('ZİRVE')}
                    </span>
                    <p className="text-accent-200 lg:text-xl">
                        {t(
                            'EnterERP Elegance, orta ve büyük ölçekli şirketlerin teknoloji ile birlikte sürekli gelişen ihtiyaçlarını karşılamak için özelleştirilebilir bir alt yapıyı hazırlıyor.'
                        )}
                    </p>
                </div>
                <div className="z-10">
                    <GhostButton color="dark" href="/pricing">
                        {t('İnceleyin')}
                    </GhostButton>
                </div>
            </div>

            <ExtendedImage src={EleganceImage} alt={t('Elegance paket')} />
        </article>
    );
};

export default Elegance;
