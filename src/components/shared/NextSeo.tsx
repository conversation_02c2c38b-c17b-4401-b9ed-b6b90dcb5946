import Head from 'next/head';
import { DefaultSeo } from 'next-seo';
import { useRouter } from 'next/router';
import { useTrans } from '@src/hooks';

const NextSeo = () => {
    const router = useRouter();
    const SiteNameJsonLdScript = () => (
        <script
            type="application/ld+json"
            id="site-name"
            dangerouslySetInnerHTML={{
                __html: JSON.stringify({
                    '@context': 'http://schema.org',
                    '@type': 'WebSite',
                    name: 'EnterERP',
                    url: 'https://www.entererp.com'
                })
            }}
        />
    );

    const t = useTrans();

    return (
        <>
            <DefaultSeo
                titleTemplate="%s - EnterERP"
                defaultTitle={t(
                    "EnterERP - Türkiye'nin En Kapsamlı ve En Hızlı Bulut ERP Sistemi"
                )}
                description={t(
                    'EnterERP ile tedarik zincirinden finansal süreçlere kadar tüm işinizin kontrolünü elinize alın.'
                )}
                openGraph={{
                    url: `https://www.entererp.com${
                        router.asPath.split('?')[0]
                    }`,
                    siteName: 'EnterERP',
                    type: 'website',
                    locale: router.locale
                }}
                canonical={`https://www.entererp.com${
                    router.asPath.split('?')[0]
                }`}
                defaultOpenGraphImageWidth={1200}
                defaultOpenGraphImageHeight={630}
                themeColor="#ffffff"
                robotsProps={{
                    maxImagePreview: 'large',
                    maxSnippet: -1
                }}
                twitter={{
                    site: '@entererp',
                    cardType: 'summary_large_image'
                }}
                additionalMetaTags={[
                    {
                        name: 'msapplication-TileImage',
                        content: '/favicons/ms-icon-144x144.png'
                    },
                    {
                        name: 'msapplication-TileColor',
                        content: '#ffffff'
                    }
                ]}
                additionalLinkTags={[
                    {
                        rel: 'apple-touch-icon',
                        type: 'image/png',
                        href: '/favicons/apple-icon-57x57.png',
                        sizes: '57x57'
                    },
                    {
                        rel: 'apple-touch-icon',
                        type: 'image/png',
                        href: '/favicons/apple-icon-60x60.png',
                        sizes: '60x60'
                    },
                    {
                        rel: 'apple-touch-icon',
                        type: 'image/png',
                        href: '/favicons/apple-icon-76x76.png',
                        sizes: '76x76'
                    },
                    {
                        rel: 'apple-touch-icon',
                        type: 'image/png',
                        href: '/favicons/apple-icon-120x120.png',
                        sizes: '120x120'
                    },
                    {
                        rel: 'apple-touch-icon',
                        type: 'image/png',
                        href: '/favicons/apple-icon-144x144.png',
                        sizes: '144x144'
                    },
                    {
                        rel: 'apple-touch-icon',
                        type: 'image/png',
                        href: '/favicons/apple-icon-152x152.png',
                        sizes: '152x152'
                    },
                    {
                        rel: 'apple-touch-icon',
                        type: 'image/png',
                        href: '/favicons/apple-icon-180x180.png',
                        sizes: '180x180'
                    },
                    {
                        rel: 'icon',
                        type: 'image/x-icon',
                        href: '/favicons/favicon.ico'
                    },
                    {
                        rel: 'icon',
                        type: 'image/png',
                        href: '/favicons/favicon-16x16.png',
                        sizes: '16x16'
                    },
                    {
                        rel: 'icon',
                        type: 'image/png',
                        href: '/favicons/favicon-32x32.png',
                        sizes: '32x32'
                    },
                    {
                        rel: 'icon',
                        type: 'image/png',
                        href: '/favicons/favicon-96x96.png',
                        sizes: '96x96'
                    },
                    {
                        rel: 'icon',
                        type: 'image/png',
                        href: '/favicons/android-icon-192x192.png',
                        sizes: '192x192'
                    }
                ]}
            />

            <Head>
                <meta
                    name="google-site-verification"
                    content="xe5g_XJSgC53ijWygAmsFNh5NIHLTnBlPic2nU1eTZU"
                />
                {SiteNameJsonLdScript()}
            </Head>
        </>
    );
};

export default NextSeo;
