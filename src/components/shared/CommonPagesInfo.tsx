import { useTrans } from '@src/hooks';

interface CommonPagesInfoProps {
    title: string;
    description: string;
}

const CommonPagesInfo = ({ title, description }: CommonPagesInfoProps) => {
    const t = useTrans();

    return (
        <div className="container my-12 flex flex-col items-center justify-center gap-10 lg:my-24">
            <p className="text-center text-xs tracking-widest text-primary-400">
                {t(title)}
            </p>
            <p className="text-center text-lg text-accent-200">
                {t(description)}
            </p>
        </div>
    );
};

export default CommonPagesInfo;
