import { useRef } from 'react';
import { useSectionIntersect, useTrans } from '@src/hooks';
import { TabEnum } from '@src/interfaces';
import { twMerge } from '@src/utils';
import ExtendedImage from './ExtendedImage';

const maxImageSize = [
    'max-w-none',
    'max-w-xs',
    'max-w-sm',
    'max-w-md',
    'max-w-lg',
    'max-w-xl',
    'max-w-2xl'
] as const;

interface CommonPagesSectionProps {
    title: string;
    description: string;
    src: any;
    order: '0' | '1';
    id: TabEnum;
    info: string | number;
    intersectionHandler?: (activeTab: TabEnum) => void;
    maxImageSize?: (typeof maxImageSize)[number];
}

const CommonPagesSection = ({
    title,
    description,
    src,
    order,
    id,
    info,
    intersectionHandler,
    maxImageSize
}: CommonPagesSectionProps) => {
    const t = useTrans();

    const ref = useRef<HTMLElement>(null);

    useSectionIntersect({ ref, id, intersectionHandler });

    return (
        <article
            id={id}
            ref={ref}
            className="container my-8 flex flex-col items-center justify-between gap-24 py-16 max-lg:py-8 lg:flex-row"
        >
            <div
                style={{ order: order }}
                className={twMerge(
                    'grid gap-4 max-lg:!-order-1 lg:w-1/2 lg:gap-10',
                    typeof info === 'number' && 'place-items-center text-center'
                )}
            >
                {typeof info === 'number' ? (
                    <span className="flex h-32 w-32 items-center justify-center rounded-full border border-dashed border-primary-800 font-means text-6xl">
                        {t(info)}
                    </span>
                ) : (
                    <span className="text-sm tracking-widest text-primary-400">
                        {t(info)}
                    </span>
                )}
                <h2 className="font-means text-3xl font-light lg:text-5xl lg:leading-tight">
                    {t(title)}
                </h2>
                <p className="text-lg text-accent-200">{t(description)}</p>
            </div>
            <div className={twMerge('w-full max-w-2xl lg:w-1/2', maxImageSize)}>
                <ExtendedImage src={src} alt={t(title)} quality={100} />
            </div>
        </article>
    );
};

export default CommonPagesSection;
