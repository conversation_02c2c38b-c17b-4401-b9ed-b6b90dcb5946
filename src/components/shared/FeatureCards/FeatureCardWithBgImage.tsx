import { StaticImageData } from 'next/image';
import { useLottie, useTrans } from '@src/hooks';
import ExtendedImage from '../ExtendedImage';
import GhostButton from '../GhostButton';

interface FeatureCardWithBgImageProps {
    title: string;
    description: string;
    href: string;
    icon: any;
    info: string;
    buttonLabel: string;
    animationPath?: string;
    image?: StaticImageData | string;
}

const FeatureCardWithBgImage = ({
    title,
    description,
    icon,
    href,
    info,
    buttonLabel,
    animationPath,
    image
}: FeatureCardWithBgImageProps) => {
    const t = useTrans();

    const { animationRef, observerRef } = useLottie({
        path: animationPath ?? '',
        autoplay: false,
        loop: false
    });

    return (
        <article className="card-shadow relative flex flex-col overflow-hidden rounded-4xl border bg-white py-8 md:flex-row lg:gap-6 lg:py-16">
            <div className="flex w-full flex-col justify-start px-8 md:w-8/12 lg:w-6/12 lg:pl-16">
                <div className="mb-16">
                    <ExtendedImage
                        className="h-16 w-16"
                        src={icon}
                        alt={t(info)}
                    />
                </div>
                <div className="mb-6 grid content-start gap-6">
                    <span className="text-xs tracking-widest text-primary-400">
                        {t(info)}
                    </span>
                    <h3 className="font-means text-3xl font-light lg:text-4xl">
                        {t(title)}
                    </h3>
                    <p className="text-accent-200 lg:text-xl">
                        {t(description)}
                    </p>
                </div>
                <div className="z-10 mt-auto">
                    <GhostButton color="dark" href={href}>
                        {t(buttonLabel)}
                    </GhostButton>
                </div>
            </div>

            <div
                ref={observerRef}
                className="ml-auto aspect-1 h-[300px] pl-8 md:h-[400px] lg:mb-12 lg:h-[500px] lg:pl-0"
            >
                {animationPath && <div ref={animationRef}></div>}
            </div>

            {image && (
                <div className="ml-auto">
                    <ExtendedImage src={image} alt={t(title)} />
                </div>
            )}
        </article>
    );
};

export default FeatureCardWithBgImage;
