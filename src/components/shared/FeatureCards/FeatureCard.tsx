import { useTrans } from '@src/hooks';
import ExtendedImage from '../ExtendedImage';
import GhostButton from '../GhostButton';

interface FeatureCardProps {
    title: string;
    description: string;
    href: string;
    src: any;
    info: string;
    buttonLabel: string;
}

const FeatureCard = ({
    title,
    description,
    src,
    href,
    info,
    buttonLabel
}: FeatureCardProps) => {
    const t = useTrans();

    return (
        <article className="card-shadow flex flex-col justify-start rounded-4xl border bg-white p-8 lg:p-16">
            <div className="mb-16">
                <ExtendedImage className="h-16 w-16" src={src} alt={t(title)} />
            </div>
            <div className="mb-6 grid content-start gap-6">
                <span className="text-xs tracking-widest text-primary-400">
                    {t(info)}
                </span>
                <h3 className="font-means text-3xl font-light lg:text-4xl">
                    {t(title)}
                </h3>
                <p className="text-accent-200 lg:text-xl">{t(description)}</p>
            </div>
            <div className="mt-auto">
                <GhostButton color="dark" href={href}>
                    {t(buttonLabel)}
                </GhostButton>
            </div>
        </article>
    );
};

export default FeatureCard;
