import { StaticImageData } from 'next/image';
import { useLottie, useTrans } from '@src/hooks';
import ExtendedImage from '../ExtendedImage';
import GhostButton from '../GhostButton';

interface FeatureCardWithImageProps {
    title: string;
    description: string;
    href: string;
    buttonLabel: string;
    animationPath?: string;
    image?: StaticImageData | string;
}

const FeatureCardWithImage = ({
    title,
    description,
    href,
    buttonLabel,
    animationPath,
    image
}: FeatureCardWithImageProps) => {
    const t = useTrans();

    const { animationRef, observerRef } = useLottie({
        path: animationPath ?? '',
        autoplay: false,
        loop: false
    });

    return (
        <article className="card-shadow flex flex-col items-center justify-center rounded-4xl border bg-white p-8 md:flex-row lg:gap-12 lg:p-16">
            <div className="order-1 mt-6 w-full md:-order-1 md:mt-0 md:w-1/2">
                <div className="mb-6 flex flex-col items-start justify-center gap-6">
                    <h2 className="font-means text-3xl font-light lg:text-4xl">
                        {t(title)}
                    </h2>
                    <p className="text-accent-200 lg:text-xl">
                        {t(description)}
                    </p>
                </div>
                <div className="mt-auto">
                    <GhostButton color="dark" href={href}>
                        {t(buttonLabel)}
                    </GhostButton>
                </div>
            </div>

            <div
                ref={observerRef}
                className="grid aspect-1 w-1/2 place-content-center"
            >
                {animationPath && <div ref={animationRef}></div>}
            </div>

            {image && (
                <div className="w-1/2">
                    <ExtendedImage
                        className="mx-auto w-9/12 lg:ml-auto lg:w-full"
                        src={image}
                        alt={t(title)}
                    />
                </div>
            )}
        </article>
    );
};

export default FeatureCardWithImage;
