import { ReactNode } from 'react';
import { ChevronRightIcon } from '@src/icons/solid';
import { twMerge } from '@src/utils';
import ExtendedLink from './ExtendedLink';

interface GhostButtonProps {
    href: string;
    className?: string;
    children: ReactNode;
    color: 'dark' | 'white';
}

const GhostButton = ({
    href,
    className,
    children,
    color
}: GhostButtonProps) => {
    return (
        <div className="group inline-block">
            <ExtendedLink
                className={twMerge(
                    'flex cursor-pointer items-center font-medium text-white group-hover:text-primary-200',
                    color === 'dark' &&
                        'text-primary-800 group-hover:text-primary-400',
                    className
                )}
                href={href}
            >
                {children}
                <span
                    className={twMerge(
                        'invisible -mr-3 ml-3 h-[2px] w-8 origin-left scale-x-0 overflow-hidden bg-primary-200 opacity-40 transition-all duration-300 group-hover:visible group-hover:scale-x-100 group-hover:opacity-100',
                        color === 'dark' && 'bg-primary-400'
                    )}
                ></span>
                <ChevronRightIcon
                    className={twMerge(
                        'h-4 w-4 -translate-x-4 text-primary-200 transition-transform duration-300 group-hover:translate-x-0',
                        color === 'dark' && 'text-primary-400'
                    )}
                />
            </ExtendedLink>
        </div>
    );
};

export default GhostButton;
