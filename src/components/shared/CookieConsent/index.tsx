import { Fragment, useRef, useState } from 'react';
import siteLogo from '@public/images/shared/site-logo.svg';
import CookieConsent from 'react-cookie-consent';

import { Dialog, Disclosure, Switch, Transition } from '@headlessui/react';
import { useTrans } from '@src/hooks';
import Image from 'next/image';
import {
    ChevronRightIcon,
    MinusIcon,
    PlusIcon,
    XMarkIcon
} from '@src/icons/solid';
import { twMerge } from 'tailwind-merge';

interface CookieItem {
    id: number;
    title: string;
    description: string;
    defaultChecked: boolean;
    disabled: boolean;
    checked?: boolean;
    gtmCategory: string;
}

const cookieAccordionItems: CookieItem[] = [
    {
        id: 1,
        title: 'Zorunlu Tanımlama Bilgileri',
        description:
            'Bu tanımlama bilgileri, web sitesinin çalışması için gereklidir ve sistemlerimizde kapatılamaz. Bunlar genellikle yalnızca sizin işlemlerinizi gerçekleştirmek için ayarlanmıştır. B<PERSON> iş<PERSON>, gizlilik tercihlerinizi belirlemek, oturum açmak veya form doldurmak gibi hizmet taleplerinizi içerir. Tarayıcınızı, bu tanımlama bilgilerini engelleyecek veya bunlar hakkında sizi uyaracak şekilde ayarlayabilirsiniz ancak bu durumda sitenin bazı bölümleri çalışmayabilir.',
        defaultChecked: true,
        disabled: true,
        checked: true,
        gtmCategory: 'ad_user_data'
    },
    {
        id: 2,
        title: 'Performans Tanımlama Bilgileri',
        description:
            'Bu tanımlama bilgileri, sitemizin performansını ölçebilmemiz ve iyileştirebilmemiz için sitenin ziyaret edilme sayısını ve trafik kaynaklarını sayabilmemizi sağlar. Hangi sayfaların en fazla ve en az ziyaret edildiğini ve ziyaretçilerin sitede nasıl gezindiklerini öğrenmemize yardımcı olurlar. Bu tanımlama bilgilerinin topladığı tüm bilgiler derlenir ve bu nedenle anonimdir. Bu tanımlama bilgilerine izin vermezseniz sitemizi ne zaman ziyaret ettiğinizi bilemeyiz.',
        defaultChecked: true,
        disabled: false,
        checked: true,
        gtmCategory: 'ad_storage'
    },
    {
        id: 3,
        title: 'Hedefleme Amaçlı Tanımlama Bilgileri',
        description:
            'Bu çerezler reklam ortaklarımız tarafından sitemize yerleştirilir. İlgili şirketler tarafından ilgi alanı profilinizi oluşturmak ve diğer sitelerde alakalı reklamlar göstermek için kullanılabilirler. Tarayıcınızı ve cihazınızı benzersiz bir şekilde tanımlayarak çalışırlar. Bu çerezlere izin vermezseniz, farklı sitelerde size özel bir reklam deneyimi sunamayız.',
        defaultChecked: true,
        disabled: false,
        checked: true,
        gtmCategory: 'ad_personalization'
    },
    {
        id: 4,
        title: 'Analitik',
        description: 'Bu Çerezler Analiz amaçları için kullanılır.',
        defaultChecked: true,
        disabled: false,
        checked: true,
        gtmCategory: 'analytics_storage'
    }
];

declare global {
    interface Window {
        dataLayer: Array<Record<string, any>>;
    }
}

const Cookie = () => {
    const [showDrawer, setShowDrawer] = useState(false);
    const [cookieItems, setCookieItems] =
        useState<CookieItem[]>(cookieAccordionItems);
    const cookieConsentRef = useRef<CookieConsent>(null);
    const t = useTrans();

    const handleSwitchChange = (id: number, value: boolean) => {
        setCookieItems((prevItems) =>
            prevItems.map((item) =>
                item.id === id ? { ...item, checked: value } : item
            )
        );
    };

    const handleConfirmChoices = (accepted: boolean) => {
        setShowDrawer(false);

        const consent: { [key: string]: string } = {};

        cookieItems.forEach((cookieItem) => {
            if (cookieItem.gtmCategory === 'ad_user_data') {
                consent[cookieItem.gtmCategory] = 'granted';
            } else {
                const cookieValue =
                    accepted && cookieItem.checked ? 'granted' : 'denied';
                consent[cookieItem.gtmCategory] = cookieValue;
            }
        });

        const cookieDate = new Date();
        cookieDate.setTime(cookieDate.getTime() + 365 * 24 * 60 * 60 * 1000);

        document.cookie = `consent=${JSON.stringify(
            consent
        )};path=/;SameSite=Lax;expires=${cookieDate.toUTCString()};`;

        if (window.dataLayer && Array.isArray(window.dataLayer)) {
            window.dataLayer.push({ consent: null });
            window.dataLayer.push({
                event: 'consentUpdate',
                consent: consent
            });
        }
    };

    return (
        <>
            <CookieConsent
                ref={cookieConsentRef}
                enableDeclineButton
                disableStyles
                location="bottom"
                declineButtonText={t('Reddet')}
                buttonText={t('Kabul Et')}
                cookieName="enterstore-cookie-consent"
                buttonClasses="cookie-btn-accept"
                declineButtonClasses="cookie-btn-decline"
                buttonWrapperClasses="cookie-btn-wrapper"
                containerClasses="cookie-container"
                onAccept={() => handleConfirmChoices(true)}
                onDecline={() => handleConfirmChoices(false)}
            >
                <div className="flex flex-col items-center gap-4 max-lg:mt-4 lg:flex-row lg:gap-10">
                    <p>
                        {t(
                            'Web sitemiz, cihazınızda çerez depolar ve çerez bildirimimize uygun olarak bilgileri kontrol eder. Çerezleri kontrol etmek için “Çerez Tercihleri”ni seçin. Çerez tercihlerinizden bağımsız olarak tarayıcınızdan belirli toplu ve anonim veriler toplayabiliriz.'
                        )}
                    </p>
                    <div className="group flex items-center space-x-2 py-1.5">
                        <button
                            className="flex cursor-pointer items-center whitespace-nowrap font-medium text-[#011D2A] group-hover:text-primary-200"
                            onClick={() => setShowDrawer(true)}
                        >
                            {t('Çerez Tercihleri')}
                            <span
                                className={twMerge(
                                    'invisible -mr-3 ml-3 h-[2px] w-8 origin-left scale-x-0 overflow-hidden bg-primary-200 opacity-40 transition-all duration-300 group-hover:visible group-hover:scale-x-100 group-hover:opacity-100'
                                )}
                            ></span>
                            <ChevronRightIcon
                                className={twMerge(
                                    'h-4 w-4 -translate-x-4 text-primary-200 transition-transform duration-300 group-hover:translate-x-0'
                                )}
                            />
                        </button>
                    </div>
                </div>
            </CookieConsent>

            <Transition.Root show={showDrawer} as={Fragment}>
                <Dialog
                    as="div"
                    className="relative z-[999999]"
                    onClose={() => setShowDrawer(false)}
                >
                    <Transition.Child
                        as={Fragment}
                        enter="transition duration-300"
                        enterFrom="opacity-0"
                        enterTo="opacity-100"
                        leave="transition duration-300"
                        leaveFrom="opacity-100"
                        leaveTo="opacity-0"
                    >
                        <div className="fixed inset-0 bg-gray-900 bg-opacity-20 transition-opacity" />
                    </Transition.Child>

                    <div className="fixed inset-0 overflow-hidden">
                        <div className="absolute inset-0 overflow-hidden">
                            <div className="pointer-events-none fixed inset-y-0 left-0 flex max-w-full">
                                <Transition.Child
                                    as={Fragment}
                                    enter="transform transition duration-500"
                                    enterFrom="translate-y-full translate-x-0 xl:-translate-x-full xl:translate-y-0"
                                    enterTo="translate-y-0 xl:-translate-x-0"
                                    leave="transform transition duration-500"
                                    leaveFrom="translate-y-0 xl:-translate-x-0"
                                    leaveTo="translate-y-full translate-x-0 xl:-translate-x-full xl:translate-y-0"
                                >
                                    <Dialog.Panel className="pointer-events-auto w-screen xl:max-w-md">
                                        <div className="flex h-full flex-col overflow-y-auto bg-white shadow-xl">
                                            <div className="hidden border-b p-6 xl:block">
                                                <div className="flex items-center justify-between">
                                                    <Dialog.Title className="flex select-none items-center text-lg font-medium">
                                                        <Image
                                                            src={siteLogo}
                                                            alt="Logo"
                                                            width={170}
                                                            height={170}
                                                        />
                                                    </Dialog.Title>
                                                    <div className="ml-3 flex h-8 items-center">
                                                        <button
                                                            type="button"
                                                            className="flex h-8 w-8 items-center justify-center rounded-full bg-gray-100 text-gray-400 outline-none transition hover:bg-gray-200 hover:text-gray-500"
                                                            onClick={() =>
                                                                setShowDrawer(
                                                                    false
                                                                )
                                                            }
                                                        >
                                                            <XMarkIcon
                                                                className="h-5 w-5"
                                                                aria-hidden="true"
                                                            />
                                                        </button>
                                                    </div>
                                                </div>
                                            </div>

                                            <div className="h-mobile-header flex w-full select-none border-b border-gray-200 bg-white xl:hidden">
                                                <div className="container">
                                                    <div className="flex h-12 items-center justify-start">
                                                        <div className="flex items-center ">
                                                            <button
                                                                className="flex items-center gap-1 font-semibold text-primary-600 transition active:opacity-30"
                                                                onClick={() =>
                                                                    setShowDrawer(
                                                                        false
                                                                    )
                                                                }
                                                            >
                                                                <XMarkIcon
                                                                    className="h-4 w-4"
                                                                    aria-hidden="true"
                                                                />
                                                                {t('Kapat')}
                                                            </button>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>

                                            <div className="flex-1 overflow-y-auto overflow-x-hidden">
                                                <div className="px-6 pt-4">
                                                    <p className="text-lg font-bold">
                                                        {t(
                                                            'Web Sitemiz Çerezleri Nasıl Kullanır'
                                                        )}
                                                    </p>
                                                    <p className="mt-2 text-sm">
                                                        {t(
                                                            'Sitemiz, cihazınızda çerezlerin ayarlanmasını isteyebilir. Çerezleri sitemizi ziyaret ettiğinizde bize bildirmek, bizimle nasıl etkileşim kurduğunuzu anlamak, kullanıcı deneyiminizi zenginleştirmek ve kişiselleştirmek, sosyal medya işlevselliğini etkinleştirmek ve size daha alakalı reklamlar sağlamak da dahil olmak üzere şirketimizle olan ilişkinizi kişiselleştirmek için kullanıyoruz. Daha fazlasını öğrenmek için farklı kategori başlıklarına tıklayın. Bazı çerez türlerini engellemenin sitemizdeki deneyiminizi ve sunabileceğimiz hizmetleri etkileyebileceğini unutmayın.'
                                                        )}
                                                    </p>

                                                    <p className="mt-8 text-lg font-bold">
                                                        {t(
                                                            'Çerez Tercihlerini Yönet'
                                                        )}
                                                    </p>
                                                </div>
                                                <div className="mt-4 flex flex-col border-b px-6 pb-2.5">
                                                    {cookieItems.map(
                                                        (cookieItem) => (
                                                            <Disclosure
                                                                key={
                                                                    cookieItem.id
                                                                }
                                                            >
                                                                {({ open }) => (
                                                                    <>
                                                                        <Disclosure.Button
                                                                            as="div"
                                                                            className="py-2.5"
                                                                        >
                                                                            <div className="flex cursor-pointer items-center justify-between">
                                                                                <div className="flex items-center gap-4">
                                                                                    {!open && (
                                                                                        <PlusIcon className="h-7 w-7 rounded-full bg-gray-100 p-1.5" />
                                                                                    )}
                                                                                    {open && (
                                                                                        <MinusIcon className="h-7 w-7 rounded-full bg-gray-100 p-1.5" />
                                                                                    )}
                                                                                    <p className="text-left text-xs font-semibold lg:text-sm">
                                                                                        {t(
                                                                                            cookieItem.title
                                                                                        )}
                                                                                    </p>
                                                                                </div>
                                                                                <Switch
                                                                                    disabled={
                                                                                        cookieItem.disabled
                                                                                    }
                                                                                    className={`${
                                                                                        cookieItem.checked
                                                                                            ? 'bg-blue-600'
                                                                                            : 'bg-gray-200'
                                                                                    } relative inline-flex h-7 w-[58px] items-center rounded-full disabled:bg-blue-300`}
                                                                                    checked={
                                                                                        cookieItem.checked
                                                                                    }
                                                                                    onChange={(
                                                                                        checked
                                                                                    ) =>
                                                                                        handleSwitchChange(
                                                                                            cookieItem.id,
                                                                                            checked
                                                                                        )
                                                                                    }
                                                                                >
                                                                                    <span className="sr-only">
                                                                                        {t(
                                                                                            'Toggle cookie consent'
                                                                                        )}
                                                                                    </span>
                                                                                    <span
                                                                                        className={`${
                                                                                            cookieItem.checked
                                                                                                ? 'translate-x-8'
                                                                                                : 'translate-x-1'
                                                                                        } pointer-events-none inline-block h-6 w-6 transform rounded-full bg-white transition`}
                                                                                    />
                                                                                </Switch>

                                                                                <span className="sr-only">
                                                                                    {t(
                                                                                        cookieItem.title
                                                                                    )}
                                                                                </span>
                                                                            </div>
                                                                        </Disclosure.Button>
                                                                        <Disclosure.Panel className="text-sm text-gray-500">
                                                                            <p className="text-xs text-gray-600 lg:text-sm">
                                                                                {t(
                                                                                    cookieItem.description
                                                                                )}
                                                                            </p>
                                                                        </Disclosure.Panel>
                                                                    </>
                                                                )}
                                                            </Disclosure>
                                                        )
                                                    )}
                                                </div>
                                                <div className=" px-6 pb-6">
                                                    <button
                                                        onClick={() => (
                                                            handleConfirmChoices(
                                                                true
                                                            ),
                                                            cookieConsentRef.current?.accept()
                                                        )}
                                                        className="btn-transition mt-4 inline-block cursor-pointer whitespace-nowrap rounded-[100vw] border border-[#011D2A] bg-primary-200 px-8 py-4 font-medium leading-4 text-white hover:-translate-y-1 hover:shadow-[0px_6px_0px_0px_#011D2A]"
                                                    >
                                                        {t(
                                                            'Tercihlerimi Onayla'
                                                        )}
                                                    </button>
                                                </div>
                                            </div>
                                        </div>
                                    </Dialog.Panel>
                                </Transition.Child>
                            </div>
                        </div>
                    </div>
                </Dialog>
            </Transition.Root>
        </>
    );
};

export default Cookie;
