import { Transition, Dialog } from '@headlessui/react';
import { Fragment } from 'react';
import { useTrans } from '@src/hooks';
import { XMarkIcon } from '@src/icons/solid';
import Content from './Content';

interface ManifestDrawerProps {
    showDrawer: boolean;
    drawerToggleHandler: (val: boolean) => void;
}

const ManifestDrawer = ({
    showDrawer,
    drawerToggleHandler
}: ManifestDrawerProps) => {
    const t = useTrans();

    return (
        <Transition.Root show={showDrawer} as={Fragment}>
            <Dialog
                as="div"
                className="relative z-[999]"
                onClose={() => drawerToggleHandler(false)}
            >
                <Transition.Child
                    as={Fragment}
                    enter="ease-in-out duration-300"
                    enterFrom="opacity-0"
                    enterTo="opacity-100"
                    leave="ease-in-out duration-300"
                    leaveFrom="opacity-100"
                    leaveTo="opacity-0"
                >
                    <div className="fixed inset-0 bg-gray-900 bg-opacity-20 transition-opacity" />
                </Transition.Child>

                <div className="fixed inset-0 overflow-hidden">
                    <div className="absolute inset-0 overflow-hidden">
                        <div className="pointer-events-none fixed inset-y-0 left-0 flex max-w-full">
                            <Transition.Child
                                as={Fragment}
                                enter="transform transition ease-in-out duration-500"
                                enterFrom="translate-y-full lg:translate-y-0 translate-x-0 lg:-translate-x-full"
                                enterTo="translate-y-0 lg:-translate-x-0"
                                leave="transform transition ease-in-out duration-500"
                                leaveFrom="translate-y-0 lg:-translate-x-0"
                                leaveTo="translate-y-full lg:translate-y-0 translate-x-0 lg:-translate-x-full"
                            >
                                <Dialog.Panel className="pointer-events-auto w-screen xl:max-w-md">
                                    <div className="flex h-full flex-col overflow-y-auto bg-white shadow-xl">
                                        <div className="hidden border-b p-6 xl:block">
                                            <div className="flex items-center justify-between">
                                                <Dialog.Title className="flex select-none items-center text-lg font-medium">
                                                    {t(
                                                        'EnterERP Gizlilik Politikası'
                                                    )}
                                                </Dialog.Title>
                                                <div className="ml-3 flex h-8 items-center">
                                                    <button
                                                        type="button"
                                                        className="flex h-8 w-8 items-center justify-center rounded-full bg-gray-100 text-gray-400 outline-none transition duration-150 ease-in-out hover:bg-gray-200 hover:text-gray-500"
                                                        onClick={() =>
                                                            drawerToggleHandler(
                                                                false
                                                            )
                                                        }
                                                    >
                                                        <XMarkIcon
                                                            className="h-5 w-5"
                                                            aria-hidden="true"
                                                        />
                                                    </button>
                                                </div>
                                            </div>
                                        </div>

                                        <div className="flex w-full select-none border-b border-gray-200 bg-white xl:hidden">
                                            <div className="container">
                                                <div className="flex h-full items-center">
                                                    <div className="flex items-center">
                                                        <button
                                                            className="py-4 font-semibold text-primary-500 transition duration-150 ease-in-out active:opacity-30"
                                                            onClick={() =>
                                                                drawerToggleHandler(
                                                                    false
                                                                )
                                                            }
                                                        >
                                                            {t('Kapat')}
                                                        </button>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>

                                        <div className="flex-1 overflow-y-auto overflow-x-hidden">
                                            <Content />
                                        </div>
                                    </div>
                                </Dialog.Panel>
                            </Transition.Child>
                        </div>
                    </div>
                </div>
            </Dialog>
        </Transition.Root>
    );
};

export default ManifestDrawer;
