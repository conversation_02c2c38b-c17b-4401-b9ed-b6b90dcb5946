import dynamic from 'next/dynamic';
import { useState } from 'react';
import { useTrans } from '@src/hooks';

const ManifestDrawer = dynamic(() => import('./ManifestDrawer'), {
    ssr: false
});

const PrivacyManifest = () => {
    const [showDrawer, setShowDrawer] = useState(false);

    const drawerToggleHandler = (value: boolean) => {
        setShowDrawer(value);
    };

    const t = useTrans();

    return (
        <>
            <p className="text-center text-xs text-accent-200 md:text-sm">
                {t('EnterERP, burada sağlanan verileri')}{' '}
                <span
                    onClick={() => setShowDrawer(true)}
                    className="cursor-pointer select-none text-primary-500"
                >
                    {t('Gizlilik Bildirimi')}
                </span>{' '}
                {t('doğrultusunda kullanır.')}
            </p>

            <ManifestDrawer
                showDrawer={showDrawer}
                drawerToggleHandler={drawerToggleHandler}
            />
        </>
    );
};

export default PrivacyManifest;
