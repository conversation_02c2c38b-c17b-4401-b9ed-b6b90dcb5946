import { ReactNode } from 'react';
import { Path, useFormContext } from 'react-hook-form';
import { FormValues } from '@src/interfaces';

interface SelectProps extends React.HTMLProps<HTMLSelectElement> {
    name: Path<FormValues>;
    label: string;
    children?: ReactNode;
}

const Select = ({ name, label, children, ...props }: SelectProps) => {
    const { register } = useFormContext<FormValues>();

    return (
        <div className="relative w-full">
            <label
                htmlFor={name}
                className="absolute -top-2 left-2 select-none bg-white px-1 text-xs text-accent-200"
            >
                {label}
            </label>
            <select
                id={name}
                {...props}
                {...register(name)}
                className="w-full rounded-xl border-2 border-gray-200 py-3 pl-4 text-accent-200 focus:border-primary-500 focus:outline-none focus:ring-0 focus:ring-primary-500"
                name={name}
            >
                {children}
            </select>
        </div>
    );
};

export default Select;
