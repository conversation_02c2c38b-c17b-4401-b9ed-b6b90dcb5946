import { useEffect, useState } from 'react';
import { Path, useFormContext } from 'react-hook-form';
import { useTrans } from '@src/hooks';
import { FormValues } from '@src/interfaces';
import { twMerge } from '@src/utils';

interface InputProps {
    name: Path<FormValues>;
    required?: boolean;
    label: string;
}

const patterns: { [key: string]: RegExp } = {
    mail: /[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,4}\s*$/,
    name: /^([^0-9]*){2,}$/,
    surname: /^([^0-9]*){2,}$/
};

const Input = ({ name, required, label }: InputProps) => {
    const [isFocused, setIsFocused] = useState(false);
    const [isEmpty, setIsEmpty] = useState(true);

    const {
        formState: { errors, isSubmitSuccessful },
        register
    } = useFormContext<FormValues>();

    useEffect(() => {
        if (isSubmitSuccessful) {
            setIsEmpty(true);
        }
    }, [isSubmitSuccessful]);

    const t = useTrans();

    return (
        <div className="relative w-full">
            <label
                className={twMerge(
                    'absolute left-4 select-none text-accent-200 transition-all duration-200',
                    isFocused || !isEmpty
                        ? '-translate-y-1/2 bg-white px-1 text-sm'
                        : 'translate-y-1/2'
                )}
                htmlFor={name}
            >
                {t(label)}
            </label>
            <input
                id={name}
                autoComplete="off"
                onFocus={() => setIsFocused(true)}
                className={twMerge(
                    'w-full cursor-default rounded-xl border-2 border-gray-200 py-3 pl-4 focus:border-red-700 focus:outline-none focus:ring-0 focus:ring-primary-500',
                    errors[name]?.type === 'required' ||
                        errors[name]?.type === 'pattern'
                        ? 'border-red-700 focus:border-red-700'
                        : !isEmpty
                        ? 'border-green-700 focus:border-green-700'
                        : 'border-gray-200 focus:border-primary-500'
                )}
                {...register(name, {
                    required,
                    pattern: patterns[name],
                    onBlur() {
                        setIsFocused(false);
                    },
                    onChange(e) {
                        if (e.target.value.length > 0) {
                            setIsEmpty(false);
                        } else {
                            setIsEmpty(true);
                        }
                    }
                })}
            />
        </div>
    );
};

export default Input;
