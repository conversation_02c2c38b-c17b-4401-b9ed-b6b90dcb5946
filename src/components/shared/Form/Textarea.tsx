import { Path, useFormContext } from 'react-hook-form';
import { FormValues } from '@src/interfaces';

interface TextareaProps extends React.HTMLProps<HTMLTextAreaElement> {
    name: Path<FormValues>;
}

const Textarea = ({ name, ...props }: TextareaProps) => {
    const { register } = useFormContext<FormValues>();

    return (
        <textarea
            {...props}
            {...register(name)}
            className="w-full cursor-default resize-none rounded-xl border-2 border-gray-200 py-3 pl-4 focus:border-primary-500 focus:outline-none focus:ring-0 focus:ring-primary-500"
        ></textarea>
    );
};

export default Textarea;
