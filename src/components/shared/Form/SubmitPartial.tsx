import { useTrans } from '@src/hooks';
import { twMerge } from '@src/utils';

interface SubmitPartialProps
    extends React.ButtonHTMLAttributes<HTMLButtonElement> {
    isSubmitting: boolean;
    title: string;
}

const SubmitPartial = ({
    isSubmitting,
    title,
    ...props
}: SubmitPartialProps) => {
    const t = useTrans();

    return (
        <>
            <div className="group mx-auto inline-block w-fit">
                <button
                    {...props}
                    disabled={isSubmitting}
                    className={twMerge(
                        'btn-transition flex w-40 items-center justify-center whitespace-nowrap rounded-[100vw] border border-[#011D2A] bg-primary-200 px-8 py-4 font-medium leading-4 text-white group-hover:-translate-y-1 group-hover:shadow-[0px_6px_0px_0px_#011D2A]',
                        isSubmitting
                            ? 'cursor-progress opacity-60'
                            : 'cursor-pointer'
                    )}
                >
                    {isSubmitting ? (
                        <svg
                            width="18"
                            height="18"
                            viewBox="0 0 24 24"
                            className="animate-spin fill-white"
                            xmlns="http://www.w3.org/2000/svg"
                        >
                            <circle cx="3" cy="12" r="2" />
                            <circle cx="21" cy="12" r="2" />
                            <circle cx="12" cy="21" r="2" />
                            <circle cx="12" cy="3" r="2" />
                            <circle cx="5.64" cy="5.64" r="2" />
                            <circle cx="18.36" cy="18.36" r="2" />
                            <circle cx="5.64" cy="18.36" r="2" />
                            <circle cx="18.36" cy="5.64" r="2" />
                        </svg>
                    ) : (
                        t(title)
                    )}
                </button>
            </div>
        </>
    );
};

export default SubmitPartial;
