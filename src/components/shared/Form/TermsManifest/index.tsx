import dynamic from 'next/dynamic';
import { useState } from 'react';
import { useFormContext } from 'react-hook-form';
import { useTrans } from '@src/hooks';
import { FormValues } from '@src/interfaces';
import { twMerge } from '@src/utils';

const ManifestDrawer = dynamic(() => import('./ManifestDrawer'), {
    ssr: false
});

const TermsManifest = () => {
    const [showDrawer, setShowDrawer] = useState(false);

    const drawerToggleHandler = (value: boolean) => {
        setShowDrawer(value);
    };

    const {
        register,
        formState: { errors }
    } = useFormContext<FormValues>();

    const t = useTrans();

    return (
        <>
            <div className="flex items-center justify-center gap-2">
                <input
                    type="checkbox"
                    className={twMerge(
                        'rounded-md p-2.5',
                        errors.terms?.type === 'required' &&
                            'focus:ring-red-700'
                    )}
                    id="privacy"
                    defaultChecked
                    {...register('terms', { required: true })}
                />
                <label
                    htmlFor="privacy"
                    className="select-none text-xs text-accent-200 md:text-sm"
                >
                    <span
                        onClick={(e) => {
                            e.preventDefault();
                            setShowDrawer(true);
                        }}
                        className="cursor-pointer text-primary-500"
                    >
                        {t(
                            'Kişisel verilerimin işlenmesine ilişkin aydınlatma metnini'
                        )}
                    </span>{' '}
                    {t('okudum, onaylıyorum.')}
                </label>
            </div>

            <ManifestDrawer
                showDrawer={showDrawer}
                drawerToggleHandler={drawerToggleHandler}
            />
        </>
    );
};

export default TermsManifest;
