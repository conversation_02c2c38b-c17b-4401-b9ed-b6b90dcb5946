import { Transition } from '@headlessui/react';
import { useTrans } from '@src/hooks';
import { twMerge } from '@src/utils';

interface FormToastProps {
    showToast: boolean;
    status: 'success' | 'error' | null;
    successMessage: string;
    errorMessage: string;
}

const Toast = ({
    showToast,
    status,
    successMessage,
    errorMessage
}: FormToastProps) => {
    const t = useTrans();

    return (
        <Transition
            show={showToast}
            enter="transition-opacity duration-300"
            enterFrom="opacity-0"
            enterTo="opacity-100"
            leave="transition-opacity duration-300"
            leaveFrom="opacity-100"
            leaveTo="opacity-0"
            className={twMerge(
                'fixed right-4 top-4 z-40 rounded-xl p-5 text-white',
                status === 'success' && 'bg-green-500',
                status === 'error' && 'bg-red-500'
            )}
        >
            <div className="flex items-center gap-3">
                {status === 'success' && (
                    <>
                        <svg
                            xmlns="http://www.w3.org/2000/svg"
                            className="h-6 w-6 flex-shrink-0 stroke-current"
                            fill="none"
                            viewBox="0 0 24 24"
                        >
                            <path
                                strokeLinecap="round"
                                strokeLinejoin="round"
                                strokeWidth="2"
                                d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"
                            />
                        </svg>
                        <span>{t(successMessage)}</span>
                    </>
                )}
                {status === 'error' && (
                    <>
                        <svg
                            xmlns="http://www.w3.org/2000/svg"
                            className="h-6 w-6 flex-shrink-0 stroke-current"
                            fill="none"
                            viewBox="0 0 24 24"
                        >
                            <path
                                strokeLinecap="round"
                                strokeLinejoin="round"
                                strokeWidth="2"
                                d="M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z"
                            />
                        </svg>
                        <span>{t(errorMessage)}</span>
                    </>
                )}
            </div>
        </Transition>
    );
};

export default Toast;
