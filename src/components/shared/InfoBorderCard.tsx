import { StaticImageData } from 'next/image';
import { useTrans } from '@src/hooks';
import { twMerge } from '@src/utils';
import ExtendedImage from './ExtendedImage';

interface InfoCardProps {
    title: string;
    description: string;
    src: string | StaticImageData;
    isCard?: boolean;
}

const InfoBorderCard = ({ title, description, src, isCard }: InfoCardProps) => {
    const t = useTrans();

    return (
        <article
            className={twMerge(
                'grid w-full place-content-start gap-8 rounded-4xl border p-12 px-8 text-center',
                isCard &&
                    'card-shadow rounded-4xl border bg-white p-8 xl:gap-10 xl:p-12'
            )}
        >
            <ExtendedImage
                src={src}
                alt={t(title)}
                className="mx-auto aspect-1 max-h-24"
            />
            <p
                className={twMerge(
                    'font-means text-2xl',
                    !isCard && 'font-light'
                )}
            >
                {t(title)}
            </p>
            <p className="text-sm text-black">{t(description)}</p>
        </article>
    );
};

export default InfoBorderCard;
