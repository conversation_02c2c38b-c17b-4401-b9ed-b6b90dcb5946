import { useEffect, useRef, useState } from 'react';
import { useScrollDirection, useWindowDimensions } from '@src/hooks';
import { TabEnum } from '@src/interfaces';
import SectionTab from './SectionTab';

interface SectionTabsProps {
    intersectionHandler: (activeTab: TabEnum) => void;
    activeTab: TabEnum;
    sections: {
        id: TabEnum;
        title: string;
    }[];
}

const tabs: Record<TabEnum, number> = {
    first: 1,
    second: 2,
    third: 3,
    fourth: 4,
    fifth: 5,
    sixth: 6
};

const SLIDE_RIGHT = 50;
const SLIDE_LEFT = -50;

const INITIAL_ACTIVE_TAB = 1;

const SectionTabs = ({
    intersectionHandler,
    activeTab,
    sections
}: SectionTabsProps) => {
    const listRef = useRef<HTMLUListElement>(null);

    const [newActiveTab, setNewActiveTab] = useState(INITIAL_ACTIVE_TAB);

    const previousActiveTab = useRef(INITIAL_ACTIVE_TAB);

    previousActiveTab.current = tabs[activeTab];

    const scrollDirection = useScrollDirection();

    const { viewportWidth } = useWindowDimensions();

    useEffect(() => {
        if (viewportWidth > 1024) return;

        if (newActiveTab > previousActiveTab.current) {
            listRef.current?.scrollBy({
                left: SLIDE_LEFT,
                behavior: 'smooth'
            });
        } else if (scrollDirection === 'up') {
            listRef.current?.scrollBy({
                left: SLIDE_LEFT,
                behavior: 'smooth'
            });
        } else if (scrollDirection === 'down') {
            listRef.current?.scrollBy({
                left: SLIDE_RIGHT,
                behavior: 'smooth'
            });
        }
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [activeTab]);

    return (
        <ul
            id="section-wrapper"
            ref={listRef}
            className="scrollbar-hidden mobile-sticky-section-nav sticky z-20 flex h-[59px] snap-x snap-mandatory items-center justify-start gap-4 overflow-x-auto overflow-y-hidden border-b bg-white lg:desktop-sticky-section-nav lg:justify-center"
        >
            {sections.map((section) => (
                <SectionTab
                    key={section.id}
                    href={section.id}
                    title={section.title}
                    onClick={() => {
                        intersectionHandler(section.id);
                        setNewActiveTab(tabs[activeTab]);
                    }}
                    activeTab={activeTab}
                />
            ))}
        </ul>
    );
};

export default SectionTabs;
