import { useIOSDevice, useTrans, useWindowDimensions } from '@src/hooks';
import { TabEnum } from '@src/interfaces';
import { twMerge } from '@src/utils';

interface SectionTabProps {
    title: string;
    href: TabEnum;
    activeTab: TabEnum;
    onClick: () => void;
}

const SectionTab = ({ title, href, activeTab, ...rest }: SectionTabProps) => {
    const t = useTrans();

    const { isIOSDevice } = useIOSDevice();

    const { viewportWidth } = useWindowDimensions();

    const scroll = () => {
        const section = document.querySelector(`#${href}`);
        section?.scrollIntoView({
            behavior: isIOSDevice || viewportWidth > 1024 ? 'smooth' : 'auto'
        });
    };

    return (
        <li {...rest} className="mx-2 snap-start">
            <span
                onClick={scroll}
                className={twMerge(
                    'focus-visible:desktop-navbar-underline lg:hover:desktop-navbar-underline cursor-pointer whitespace-nowrap px-2 py-5 font-means font-light text-accent-200',
                    activeTab === href && 'desktop-navbar-underline'
                )}
            >
                {t(title)}
            </span>
        </li>
    );
};

export default SectionTab;
