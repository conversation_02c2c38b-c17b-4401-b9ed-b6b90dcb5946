import { Inter } from 'next/font/google';
import localFont from 'next/font/local';

const inter = Inter({
    subsets: ['latin'],
    weight: 'variable',
    display: 'swap'
});

const means = localFont({
    src: [
        {
            path: '../../../public/fonts/Means-Light.woff2',
            weight: '300',
            style: 'normal'
        },
        {
            path: '../../../public/fonts/Means-Regular.woff2',
            weight: '400',
            style: 'normal'
        }
    ],
    display: 'swap'
});

const NextFont = () => {
    return (
        <style jsx global>
            {`
                html {
                    --inter-font: ${inter.style.fontFamily};
                    --means-font: ${means.style.fontFamily};
                }
            `}
        </style>
    );
};

export default NextFont;
