import { ParsedUrlQuery } from 'querystring';

export default function processParams(params: ParsedUrlQuery | undefined) {
    if (params) {
        if (Array.isArray(params.slug) && params.slug.length > 0) {
            const slug = params.slug;
            const page = Number(slug[slug.length - 1]);
            if (!isNaN(page)) {
                return {
                    slug: slug.slice(0, params.slug.length - 1).join('/'),
                    pageNumber: page - 1
                };
            }
            return { slug: slug.join('/') };
        } else if (typeof params.slug === 'string' && params.slug.length > 0) {
            return { slug: params.slug };
        }
    }
    return {};
}
