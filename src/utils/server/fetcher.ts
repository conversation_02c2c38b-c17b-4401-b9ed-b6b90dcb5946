const fetcher = async ({
    url,
    method = 'GET',
    payload,
    headers
}: {
    url: string;
    method: 'GET' | 'POST' | 'PUT' | 'PATCH' | 'DELETE';
    payload?: object;
    headers?: object;
}) => {
    return (
        await fetch(`${process.env.API_URL}${url}`, {
            method,
            headers: {
                'Content-Type': 'application/json',
                ...(headers || {})
            },
            ...(payload ? { body: JSON.stringify(payload) } : {})
        })
    ).json();
};

export default fetcher;
