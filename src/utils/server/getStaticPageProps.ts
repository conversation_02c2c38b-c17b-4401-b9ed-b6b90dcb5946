import { GetStaticPropsContext } from 'next';
import { serverSideTranslations } from 'next-i18next/serverSideTranslations';
import appConfig from 'app.config';
import { TranslationPages } from '@src/interfaces';

type PageProps = {
    ctx: GetStaticPropsContext;
    translationPage?: TranslationPages;
};

const getStaticPageProps = async ({ ctx, translationPage }: PageProps) => {
    const translation = {
        ...(await serverSideTranslations(
            ctx.locale ?? appConfig.initialLocale,
            ['common', translationPage ?? '']
        ))
    };

    return {
        ...translation
    };
};

export default getStaticPageProps;
