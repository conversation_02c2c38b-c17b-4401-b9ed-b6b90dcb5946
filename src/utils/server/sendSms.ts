import { FormRequestDto } from '@src/interfaces';

const areaCodes = [
    '501',
    '505',
    '506',
    '507',
    '516',
    '530',
    '531',
    '532',
    '533',
    '534',
    '535',
    '536',
    '537',
    '538',
    '539',
    '540',
    '541',
    '542',
    '543',
    '544',
    '545',
    '546',
    '547',
    '548',
    '549',
    '551',
    '552',
    '553',
    '554',
    '555',
    '559',
    '561'
];

export default async function sendSms(payload: FormRequestDto) {
    try {
        const isTurkeyNumber = payload.phone.startsWith('+90');

        if (!isTurkeyNumber || !payload.shouldSendSms) return;

        const digitsOnly = payload.phone.replace(/\D/g, '').slice(2);

        const phoneNumber = (
            digitsOnly.startsWith('5') ? digitsOnly : digitsOnly.slice(1)
        ).substring(0, 10);

        if (
            !phoneNumber.startsWith('5') ||
            !areaCodes.includes(phoneNumber.substring(0, 3))
        ) {
            return;
        }

        const appointmentTime = payload.extra?.find(
            (item) => item.label === 'Randevu Saati'
        )?.value;
        const appointmentDate = payload.extra?.find(
            (item) => item.label === 'Randevu Tarihi'
        )?.value;

        let message = '';
        if (appointmentTime && appointmentTime.length > 0) {
            message = `Sayın ${
                payload.name
            } talebiniz alınmıştır. Danışmanlarımız ${
                appointmentDate ?? 'Bugün'
            } Saat ${appointmentTime} tarihinde sizlerle iletişime geçecektir. EnterERP'ye göstermiş olduğunuz ilgi için teşekkür ederiz.`;
        } else {
            message = `Sayın ${payload.name} talebiniz alınmıştır. Danışmanlarımız en kısa sürede sizlerle iletişim sağlayacaktır. EnterERP'ye göstermiş olduğunuz ilgi için teşekkür ederiz.`;
        }

        const formData = new FormData();

        formData.append('usercode', '8505820035');
        formData.append('password', 'D.9-Y49.');
        formData.append('gsmno', phoneNumber);
        formData.append('message', message);
        formData.append('dil', 'TR');
        formData.append('msgheader', 'ENTERERP');
        formData.append('filter', '0');

        await fetch('https://api.netgsm.com.tr/sms/send/get/', {
            method: 'POST',
            body: formData
        });
    } catch (error) {
        console.error('Error while sending SMS: ', error);
    }
}
