import nodemailer from 'nodemailer';
import { FormRequestDto } from '@src/interfaces';

const transporter = nodemailer.createTransport({
    port: 465,
    host: 'smtp.gmail.com',
    auth: {
        user: '<EMAIL>',
        pass: 'pjjukkpivfatixhg'
    },
    secure: true
});

export default async function sendMail(payload: FormRequestDto) {
    const companyName = payload.extra?.find(
        (item) => item.label === 'İşletme Adı'
    )?.value;

    try {
        await transporter.sendMail({
            from: '<EMAIL>',
            to: '<EMAIL>',
            subject: `EnterERP Sitesinden Mesajınız Var!`,
            html: `<div>İsim: ${payload.name}</div>
                   <div>E-Posta: ${payload.email}</div>
                   <div>Telefon Numarası: ${payload.phone}</div>
                   <div>İşletme Adı: ${companyName ?? ''}</div>
                   <div>Mesaj: ${payload.message}</div>
                  `
        });
    } catch (error) {
        console.error('Error while sending email: ', error);
    }
}
