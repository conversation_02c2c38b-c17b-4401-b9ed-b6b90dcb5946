const Icon = ({ ...props }: React.SVGProps<SVGSVGElement>) => {
    return (
        <svg
            {...props}
            xmlns="http://www.w3.org/2000/svg"
            viewBox="0 0 56 55"
            fill="currentColor"
        >
            <path
                d="M8.38281 25.9961C8.38281 37.3328 17.565 46.5229 28.8919 46.5229C34.6658 46.5691 46.4537 43.7859 48.3383 29.3616C46.1211 29.824 41.2115 28.7366 40.1029 23.6326C35.3913 22.4769 27.6458 16.8754 33.2997 6.44562C32.7454 5.89084 30.9729 5.72949 28.6448 5.72949C23.6985 5.72949 19.4082 7.2219 15.8658 10.1405C14.9672 10.8809 14.1325 11.6964 13.3715 12.5773C10.2631 16.1756 8.38281 20.8661 8.38281 25.9961Z"
                fill="#0048FF"
            />
            <path
                d="M8.67188 25.9961C8.67188 37.3328 17.8541 46.5229 29.1809 46.5229C34.9549 46.5691 36.5713 44.4996 36.5713 44.4996C34.3541 44.9619 28.7693 46.3684 21.1258 42.61C14.3316 38.2733 8.65571 28.1865 14.3096 17.7566C17.3792 7.90163 30.9731 5.72949 28.6451 5.72949C23.6987 5.72949 19.6972 7.2219 16.1549 10.1405C15.2562 10.8809 14.4216 11.6964 13.6606 12.5773C10.5521 16.1756 8.67188 20.8661 8.67188 25.9961Z"
                fill="#0048FF"
            />
            <path
                d="M14.3106 11.9746C6.40722 19.878 6.40722 32.6919 14.3106 40.5953C21.6423 47.927 33.1999 48.4574 41.1429 42.1863"
                stroke="black"
                strokeWidth="1.00455"
                strokeLinecap="round"
            />
            <path
                d="M43.6545 40.0179C47.2395 36.7798 48.2804 31.9227 48.5695 29.6098C42.9029 30.4193 40.5224 25.6586 40.0406 23.5384C28.7073 19.7221 30.8372 10.6464 33.1019 6.51325C27.2543 5.23611 20.9503 6.57448 16.0594 10.5284"
                stroke="black"
                strokeWidth="1.00455"
                strokeLinecap="round"
                strokeLinejoin="round"
            />
            <path
                d="M37.499 11.7421L37.6254 10.8932C37.7415 10.1132 38.334 9.48966 39.1071 9.33391L41.205 8.91121C42.5107 8.64813 43.6656 9.79441 43.4123 11.1021L43.001 13.226C42.769 14.4239 41.4712 15.0831 40.367 14.564L38.554 13.7117C37.8061 13.36 37.3773 12.5595 37.499 11.7421Z"
                fill="#0048FF"
                stroke="black"
                strokeWidth="1.00455"
            />
            <path
                d="M44.628 21.7302L44.1526 22.3313C43.6566 22.9584 43.7673 23.8698 44.3991 24.3599L45.5523 25.2544C46.3219 25.8513 47.4498 25.5268 47.7844 24.6122L48.1843 23.5192C48.4559 22.7768 48.0775 21.9544 47.3369 21.6777L46.2592 21.2752C45.6744 21.0567 45.0154 21.2406 44.628 21.7302ZM45.3828 17.0258L45.7155 17.3967C46.1489 17.8798 46.892 17.9202 47.3752 17.4867C47.9874 16.9375 47.8607 15.9454 47.13 15.5677L46.6874 15.3389C46.2167 15.0956 45.639 15.2283 45.3216 15.6526C45.0121 16.0664 45.0377 16.6411 45.3828 17.0258Z"
                fill="#0048FF"
                stroke="black"
                strokeWidth="1.00455"
            />
            <path
                d="M17.1593 22.135L16.418 22.3289C15.6444 22.5313 15.1844 23.3258 15.394 24.0974L15.7767 25.5058C16.0321 26.4457 17.1282 26.8652 17.9459 26.3361L18.9231 25.7038C19.5867 25.2744 19.7794 24.3898 19.3544 23.7232L18.7359 22.7532C18.4003 22.2268 17.7634 21.977 17.1593 22.135ZM22.8766 16.8094L22.9191 17.3059C22.9745 17.9526 23.5437 18.432 24.1905 18.3766C25.01 18.3064 25.5058 17.4377 25.1495 16.6964L24.9337 16.2473C24.7041 15.7697 24.1629 15.5281 23.6541 15.676C23.1579 15.8202 22.8325 16.2946 22.8766 16.8094Z"
                fill="white"
            />
            <path
                d="M17.1593 22.135L16.418 22.3289C15.6444 22.5313 15.1844 23.3258 15.394 24.0974L15.7767 25.5058C16.0321 26.4457 17.1282 26.8652 17.9459 26.3361L18.9231 25.7038C19.5867 25.2744 19.7794 24.3898 19.3544 23.7232L18.7359 22.7532C18.4003 22.2268 17.7634 21.977 17.1593 22.135ZM17.1593 22.135L17.2865 22.6209M22.8766 16.8094L22.9191 17.3059C22.9745 17.9526 23.5437 18.432 24.1905 18.3766C25.01 18.3064 25.5058 17.4377 25.1495 16.6964L24.9337 16.2473C24.7041 15.7697 24.1629 15.5281 23.6541 15.676C23.1579 15.8202 22.8325 16.2946 22.8766 16.8094Z"
                stroke="black"
                strokeWidth="1.00455"
            />
            <path
                d="M32.878 35.3622L33.3567 36.1204C33.7835 36.7966 34.68 36.9947 35.352 36.5614L36.7474 35.6618C37.5659 35.1341 37.6343 33.9625 36.8826 33.3431L35.8425 32.4861C35.2324 31.9835 34.331 32.0674 33.8242 32.6741L32.9903 33.6725C32.5901 34.1517 32.5447 34.8343 32.878 35.3622ZM23.3743 36.4531L23.8597 36.252C24.4781 35.9958 24.7717 35.2868 24.5155 34.6684C24.1908 33.8848 23.1824 33.6685 22.5649 34.2499L22.1824 34.6101C21.7854 34.9839 21.7168 35.5901 22.0203 36.0432C22.3163 36.4851 22.883 36.6566 23.3743 36.4531Z"
                fill="white"
                stroke="black"
                strokeWidth="1.00455"
            />
            <path
                d="M26.5615 26.1646L26.9327 26.5363C27.4057 27.01 28.1731 27.0105 28.6467 26.5376C29.2469 25.9383 29.0617 24.9237 28.2885 24.5751L27.8095 24.3591C27.3123 24.135 26.7259 24.3031 26.4231 24.7567C26.1278 25.199 26.1857 25.7883 26.5615 26.1646Z"
                fill="white"
                stroke="black"
                strokeWidth="1.00455"
            />
        </svg>
    );
};

export default Icon;
