const Icon = ({ ...props }: React.SVGProps<SVGSVGElement>) => {
    return (
        <svg
            {...props}
            width="151"
            height="32"
            viewBox="0 0 151 32"
            xmlns="http://www.w3.org/2000/svg"
        >
            <path
                d="M22.735 26.015a2.206 2.206 0 0 0 2.089-2.914 2.233 2.233 0 0 0-2.143-1.49h-2.143v2.202a2.142 2.142 0 0 0 2.197 2.202Z"
                fill="#F8AA18"
            />
            <path
                d="M24.894 8.476a2.213 2.213 0 0 0-2.615-2.61 2.277 2.277 0 0 0-1.746 2.261v2.1h2.1a2.277 2.277 0 0 0 2.26-1.751Z"
                fill="#3EB17F"
            />
            <path
                d="M4.735 23.824a2.207 2.207 0 0 0 2.914 2.09 2.234 2.234 0 0 0 1.49-2.144v-2.142H6.994a2.217 2.217 0 0 0-2.26 2.196Z"
                fill="#EB593A"
            />
            <path
                d="M6.936 5.824a2.207 2.207 0 0 0-2.089 2.914 2.234 2.234 0 0 0 2.143 1.49h2.143V8.025A2.304 2.304 0 0 0 6.93 5.824"
                fill="#00B7D8"
            />
            <path
                d="M9.808 12.504H4.06a3.45 3.45 0 0 0-3.434 3.418 3.45 3.45 0 0 0 3.418 3.418h5.764l3.418-3.418-3.418-3.418Z"
                fill="#00B7D8"
            />
            <path
                d="M19.858 12.504h5.753a3.45 3.45 0 0 1 3.407 3.418 3.45 3.45 0 0 1-3.417 3.418h-5.743l-3.418-3.418 3.418-3.418Z"
                fill="#F8AA18"
            />
            <path
                d="M11.415 21.027v5.749a3.45 3.45 0 0 0 3.418 3.418 3.45 3.45 0 0 0 3.418-3.418v-5.748l-3.418-3.418-3.418 3.418Z"
                fill="#EB593A"
            />
            <path
                d="M11.415 10.972V5.224a3.45 3.45 0 0 1 3.418-3.418 3.45 3.45 0 0 1 3.418 3.418v5.748l-3.418 3.423-3.418-3.423Z"
                fill="#3EB17F"
            />
            <path d="M48.808 15.898a1.543 1.543 0 0 1-1.607 1.522H36.428a4.853 4.853 0 0 0 4.559 3.268 6.059 6.059 0 0 0 3.493-1.072 1.495 1.495 0 0 1 2.051.761 1.574 1.574 0 0 1-.76 2.052 7.633 7.633 0 0 1-4.57 1.366c-3.632 0-7.377-2.802-8.036-6.375a7.9 7.9 0 0 1 7.741-9.418c3.799.075 7.902 2.807 7.902 7.896Zm-12.38-1.537h9.037a4.59 4.59 0 0 0-4.559-3.214 4.752 4.752 0 0 0-4.478 3.214Zm30.916 1.499v6.194a1.608 1.608 0 0 1-1.007 1.548 1.49 1.49 0 0 1-2.03-1.452v-6.24a4.875 4.875 0 0 0-3.386-4.714 4.952 4.952 0 0 0-6.472 4.703v6.156a1.607 1.607 0 0 1-1.002 1.548 1.495 1.495 0 0 1-2.035-1.452v-6.29a7.976 7.976 0 1 1 15.953 0h-.021Zm10.543-6.074a1.57 1.57 0 0 1-1.56 1.559h-2.142V22.16a1.563 1.563 0 0 1-1.671 1.553 1.548 1.548 0 0 1-1.367-1.607V11.345h-2.035a1.65 1.65 0 0 1-1.607-1.136 1.49 1.49 0 0 1 1.409-1.907h2.223V2.281A1.559 1.559 0 0 1 72.744.673a1.495 1.495 0 0 1 1.42 1.474v6.155h2.142a1.51 1.51 0 0 1 1.58 1.484Zm16.741 6.112a1.543 1.543 0 0 1-1.607 1.522H82.156a4.822 4.822 0 0 0 4.554 3.268 6.06 6.06 0 0 0 3.498-1.072 1.489 1.489 0 0 1 2.047.76 1.608 1.608 0 0 1-.756 2.053 7.644 7.644 0 0 1-4.57 1.366c-3.632 0-7.376-2.802-8.035-6.375a7.904 7.904 0 0 1 7.757-9.423c3.932.08 7.977 2.812 7.977 7.901ZM82.23 14.361h9.038a4.59 4.59 0 0 0-4.559-3.214 4.746 4.746 0 0 0-4.479 3.214Zm24.001-4.688a1.569 1.569 0 0 1-1.559 1.56 4.643 4.643 0 0 0-4.687 4.665v6.123a1.522 1.522 0 1 1-3.038.027v-6.15a7.747 7.747 0 0 1 7.704-7.703 1.506 1.506 0 0 1 1.58 1.478Zm16.64 6.225a1.545 1.545 0 0 1-1.608 1.522H110.41a4.854 4.854 0 0 0 4.559 3.268 6.04 6.04 0 0 0 3.493-1.072 1.493 1.493 0 0 1 2.051.76 1.574 1.574 0 0 1-.76 2.053 7.635 7.635 0 0 1-4.565 1.366c-3.637 0-7.382-2.802-8.035-6.375a7.895 7.895 0 0 1 1.653-6.544 7.908 7.908 0 0 1 6.104-2.88c3.932.08 7.961 2.813 7.961 7.902Zm-12.386-1.537h9.043a4.598 4.598 0 0 0-4.559-3.214 4.75 4.75 0 0 0-4.484 3.214Zm23.85-4.688a1.57 1.57 0 0 1-1.559 1.56 4.642 4.642 0 0 0-4.666 4.665v6.124a1.523 1.523 0 0 1-2.616 1.156 1.52 1.52 0 0 1-.422-1.13v-6.15a7.75 7.75 0 0 1 7.704-7.703 1.503 1.503 0 0 1 1.559 1.478Zm3.648 12.547v7.5a1.571 1.571 0 0 1-1.607 1.607 1.501 1.501 0 0 1-1.42-1.479V16.07a7.71 7.71 0 1 1 7.709 7.709 8.038 8.038 0 0 1-4.682-1.56Zm.23-6.252a4.782 4.782 0 0 0 2.954 4.42 4.784 4.784 0 1 0 1.83-9.204 4.77 4.77 0 0 0-4.784 4.784Z" />
        </svg>
    );
};

export default Icon;
