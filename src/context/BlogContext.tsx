import {
    createContext,
    ReactNode,
    useCallback,
    useContext,
    useMemo,
    useState
} from 'react';
import { BlogPost, Category, Tag } from '@src/interfaces';

type TBlogContext = {
    categories: Category[];
    tags: Tag[];
    blogPosts?: BlogPost[];
    featuredBlogPosts?: BlogPost[];
    popularBlogPosts?: BlogPost[];
    onSearchHandler: (value: string) => void;
    scrollToWrapper: () => void;
    pageHeading: string | undefined;
    pageDescription: string | undefined;
    totalBlogPostCount?: number;
    hasSearch: boolean;
};

const BlogContext = createContext<TBlogContext | undefined>(undefined);

interface BlogProviderProps {
    children: ReactNode;
    categories: Category[];
    tags: Tag[];
    blogPosts?: BlogPost[];
    featuredBlogPosts?: BlogPost[];
    popularBlogPosts?: BlogPost[];
    pageHeading?: string;
    pageDescription?: string;
    totalBlogPostCount?: number;
}

export const BlogProvider = ({
    blogPosts: initialBlogPosts,
    popularBlogPosts,
    featuredBlogPosts,
    categories,
    tags,
    pageHeading,
    pageDescription,
    totalBlogPostCount,
    children
}: BlogProviderProps) => {
    const [blogPosts, setBlogPosts] = useState(() => initialBlogPosts);
    const [hasSearch, setHasSearch] = useState(false);

    const scrollToWrapper = useCallback(() => {
        const section = document.querySelector('#blog-posts-wrapper');
        section?.scrollIntoView({ behavior: 'smooth', block: 'start' });
    }, []);

    const onSearchHandler = useCallback(
        async (value: string) => {
            if (value.length < 2) {
                setBlogPosts(initialBlogPosts);
                setHasSearch(false);
                return;
            }

            try {
                const response = await fetch('/api/search', {
                    method: 'POST',
                    body: value
                });
                const posts = await response.json();

                if (!response.ok) return;

                scrollToWrapper();
                setHasSearch(true);
                setBlogPosts(posts);
            } catch (err) {
                setBlogPosts(initialBlogPosts);
            }
        },
        [initialBlogPosts, scrollToWrapper]
    );

    const value = useMemo(
        () => ({
            categories,
            tags,
            blogPosts,
            featuredBlogPosts,
            popularBlogPosts,
            onSearchHandler,
            scrollToWrapper,
            pageHeading,
            pageDescription,
            totalBlogPostCount,
            hasSearch
        }),
        [
            categories,
            tags,
            blogPosts,
            featuredBlogPosts,
            popularBlogPosts,
            onSearchHandler,
            scrollToWrapper,
            pageHeading,
            pageDescription,
            totalBlogPostCount,
            hasSearch
        ]
    );

    return (
        <BlogContext.Provider value={value}>{children}</BlogContext.Provider>
    );
};

const useBlog = () => {
    const context = useContext(BlogContext);
    if (context === undefined) {
        throw new Error('useBlog must be used within a BlogProvider');
    }
    return context;
};

export default useBlog;
