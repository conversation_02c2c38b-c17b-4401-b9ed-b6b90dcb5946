import { BlogPost } from '@src/interfaces';
import { fetcher } from '@src/utils/server';

export default async function getBlogPost(payload: object): Promise<BlogPost> {
    try {
        return await fetcher({
            url: '/blog/post',
            method: 'POST',
            payload
        });
    } catch (err) {
        throw new Error('Something went wrong while fetching blog post', {
            cause: err
        });
    }
}
