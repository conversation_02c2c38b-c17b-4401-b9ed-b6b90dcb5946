import { Category } from '@src/interfaces';
import { fetcher } from '@src/utils/server';

export default async function getCategories(
    payload?: object
): Promise<Category[]> {
    try {
        return await fetcher({
            url: '/blog/categories',
            method: 'POST',
            payload
        });
    } catch (err) {
        throw new Error('Something went wrong while fetching blog categories', {
            cause: err
        });
    }
}
