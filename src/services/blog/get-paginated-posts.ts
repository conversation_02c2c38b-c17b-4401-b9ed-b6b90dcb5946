import { BlogPost } from '@src/interfaces';
import { fetcher } from '@src/utils/server';

export default async function getBlogPosts(
    payload?: object
): Promise<{ total: number; data: BlogPost[] }> {
    try {
        return await fetcher({
            url: '/blog/posts',
            method: 'POST',
            payload: {
                paginated: true,
                ...payload
            }
        });
    } catch (err) {
        throw new Error('Something went wrong while fetching blog posts', {
            cause: err
        });
    }
}
