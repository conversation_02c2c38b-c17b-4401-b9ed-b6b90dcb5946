import { Tag } from '@src/interfaces';
import { fetcher } from '@src/utils/server';

export default async function getTags(payload?: object): Promise<Tag[]> {
    try {
        return await fetcher({
            url: '/blog/tags',
            method: 'POST',
            payload
        });
    } catch (err) {
        throw new Error('Something went wrong while fetching blog tags', {
            cause: err
        });
    }
}
