import Category from './Category';
import Tag from './Tag';

type BlogPost = {
    _id: string;
    title: string;
    slug: string;
    categoryIds: string[];
    tagIds: string[];
    recordDate: string;
    postedOn: string;
    authorId: string;
    shortContent: string;
    thumbnails: string[];
    images: string[];
    content: string;
    status: 'published' | 'draft' | 'unpublished' | 'error';
    createdAt: string;
    updatedAt: string;
    createdBy: string;
    categories: Category[];
    tags: Tag[];
    author: {
        _id: string;
        code: string;
        name: string;
    };
    subTitles: {
        id: string;
        title: string;
    }[];
    isPopular: boolean;
    isFeatured: boolean;
};

export default BlogPost;
