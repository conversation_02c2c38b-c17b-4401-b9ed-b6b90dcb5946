import {
    AnimationItem,
    LottiePlayer
} from 'lottie-web/build/player/lottie_light';
import { useEffect, useRef, useState } from 'react';
import useIntersectionObserver from './useIntersectionObserver';

type LottieOptions = {
    path: string;
    lib?: LottiePlayer;
    isIntersecting?: boolean;
    autoplay: boolean;
    loop: boolean;
};

const useLottie = ({
    path,
    lib,
    autoplay,
    loop,
    isIntersecting
}: LottieOptions) => {
    const [lottie, setLottie] = useState<LottiePlayer | null>(null);
    const [animation, setAnimation] = useState<AnimationItem | null>(null);

    const observerRef = useRef<HTMLDivElement>(null);

    const entry = useIntersectionObserver(observerRef, {
        threshold: 0.1,
        freezeOnceVisible: false
    });

    const animationRef = useRef<HTMLDivElement>(null);

    useEffect(() => {
        if (!entry?.isIntersecting) return;

        if (!lib)
            import('lottie-web/build/player/lottie_light').then((Lottie) =>
                setLottie(Lottie.default)
            );

        if (lib) setLottie(lib);
    }, [lib, entry]);

    useEffect(() => {
        if (lottie && animationRef.current) {
            if (animation === null) {
                setAnimation(
                    lottie.loadAnimation({
                        container: animationRef.current,
                        renderer: 'svg',
                        loop,
                        autoplay,
                        path,
                        rendererSettings: {
                            className: 'fix-blur'
                        }
                    })
                );
            }

            if (entry?.isIntersecting || isIntersecting) animation?.play();

            return () => animation?.stop();
        }
    }, [
        entry?.isIntersecting,
        loop,
        lottie,
        path,
        isIntersecting,
        autoplay,
        animation
    ]);

    return { animationRef, observerRef };
};

export default useLottie;
