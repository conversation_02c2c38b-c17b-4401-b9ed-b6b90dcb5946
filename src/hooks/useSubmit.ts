import { useEffect, useState } from 'react';
import { SubmitHandler, UseFormReturn } from 'react-hook-form';
import { FormValues } from '@src/interfaces';

interface IArgs {
    methods: UseFormReturn<FormValues, any>;
}

const useSubmit = ({ methods }: IArgs) => {
    const [showToast, setShowToast] = useState(false);
    const [isSubmitting, setIsSubmitting] = useState(false);
    const [status, setStatus] = useState<'success' | 'error' | null>(null);

    useEffect(() => {
        if (methods.formState.isSubmitSuccessful) {
            methods.reset();
            methods.clearErrors();
            setShowToast(true);
        }
        const timer = setTimeout(() => {
            setShowToast(false);
        }, 3000);

        return () => clearTimeout(timer);
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [methods.formState.isSubmitSuccessful]);

    const onSubmit: SubmitHandler<FormValues> = async (data) => {
        const hasMessage = data?.description?.length > 0;
        const hasSector = data?.sector?.length > 0;
        const hasDepartment = data?.department?.length > 0;
        const hasCompany = data?.company?.length > 0;
        const hasCountry = data?.country?.length > 0;

        setIsSubmitting(true);
        try {
            const response = await fetch('/api/create-ticket', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    name: `${data.name} ${data.surname}`,
                    email: data.mail.replace(/ /g, '').toLowerCase(),
                    phone: data.phone,
                    message: hasMessage ? data.description : 'Mesaj Yok!',
                    shouldSendSms: true,
                    extra: [
                        {
                            label: 'Sektör',
                            value: hasSector
                                ? data.sector
                                : 'Sektör Belirtilmedi!'
                        },
                        {
                            label: 'Departman',
                            value: hasDepartment
                                ? data.department
                                : 'Departman Belirtilmedi!'
                        },
                        {
                            label: 'İşletme Adı',
                            value: hasCompany
                                ? data.company
                                : 'İşletme Adı Belirtilmedi!'
                        },
                        {
                            label: 'Ülke',
                            value: hasCountry ? data.country : 'Türkiye'
                        }
                    ]
                })
            });
            if (response.ok === true) setStatus('success');
        } catch (err) {
            setStatus('error');
        } finally {
            setIsSubmitting(false);
        }
    };
    return { isSubmitting, status, showToast, onSubmit };
};

export default useSubmit;
