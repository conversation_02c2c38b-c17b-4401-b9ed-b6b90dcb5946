import { useEffect, useState } from 'react';

const useIOSDevice = () => {
    const [isIOSDevice, setIsIOSDevice] = useState(false);

    useEffect(() => {
        if (
            /^((?!chrome|android).)*safari/i.test(navigator.userAgent) ||
            !!navigator.platform.match(/iPhone|iPod|iPad/)
        )
            setIsIOSDevice(true);
    }, []);

    return { isIOSDevice };
};

export default useIOSDevice;
