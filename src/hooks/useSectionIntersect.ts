import { RefObject, useEffect } from 'react';
import { TabEnum } from '@src/interfaces';
import useIntersectionObserver from './useIntersectionObserver';

type TArgs = {
    ref: RefObject<Element>;
    id: TabEnum;
    intersectionHandler: ((id: TabEnum) => void) | undefined;
};

const useSectionIntersect = ({ ref, id, intersectionHandler }: TArgs) => {
    const entry = useIntersectionObserver(ref, {
        freezeOnceVisible: false,
        threshold: 0.5
    });

    useEffect(() => {
        const timer = setTimeout(() => {
            if (
                entry?.isIntersecting &&
                typeof intersectionHandler === 'function'
            ) {
                intersectionHandler(id);
            }
        }, 250);

        return () => clearTimeout(timer);
        // eslint-disable-next-line
    }, [entry?.isIntersecting]);
};

export default useSectionIntersect;
