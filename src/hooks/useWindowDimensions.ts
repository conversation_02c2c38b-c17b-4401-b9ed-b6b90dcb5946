import { useEffect, useState } from 'react';

const useWindowDimensions = () => {
    const [windowSize, setWindowSize] = useState({
        width: 0,
        height: 0
    });

    useEffect(() => {
        function handleResize() {
            setWindowSize({
                width: window.innerWidth,
                height: window.innerHeight
            });
        }

        window.addEventListener('resize', handleResize);

        handleResize();

        return () => window.removeEventListener('resize', handleResize);
    }, []);

    return {
        viewportWidth: windowSize.width,
        viewportHeight: windowSize.height
    };
};

export default useWindowDimensions;
