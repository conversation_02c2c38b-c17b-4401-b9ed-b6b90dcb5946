import { GetStaticPropsContext } from 'next';
import { NextSeo } from 'next-seo';
import CustomerStories from '@src/components/pages/company/customer-stories';
import { useTrans } from '@src/hooks';
import { NextPageWithLayout } from '@src/interfaces';
import MainLayout from '@src/layouts/MainLayout';
import { getStaticPageProps } from '@src/utils/server';

const CustomerStoriesPage: NextPageWithLayout = () => {
    const t = useTrans();

    return (
        <>
            <NextSeo
                title={t('Müşteri Hikâyeleri')}
                description={t(
                    "Şirketlerin büyümeyi sağlamak için EnterERP'yi nasıl kullandıklarını öğrenmek için referansları görüntüleyin."
                )}
            />

            <CustomerStories />
        </>
    );
};

CustomerStoriesPage.PageLayout = MainLayout;

export default CustomerStoriesPage;

export const getStaticProps = async (ctx: GetStaticPropsContext) => {
    const props = await getStaticPageProps({
        ctx,
        translationPage: 'customer-stories'
    });

    return { props };
};
