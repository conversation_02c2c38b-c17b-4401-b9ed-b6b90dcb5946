import { Config } from 'tailwindcss';
import plugin from 'tailwindcss/plugin';
import { fontFamily } from 'tailwindcss/defaultTheme';
import { defaultTheme } from './app.config';

const tailwindConfig = {
    content: [
        './src/pages/**/*.{js,ts,jsx,tsx}',
        './src/components/**/*.{js,ts,jsx,tsx}',
        './src/layouts/**/*.{js,ts,jsx,tsx}'
    ],
    theme: {
        extend: {
            screens: {
                xs: '375px'
            },
            colors: {
                primary: {
                    50: '#ebf7ff',
                    100: '#0012ED',
                    200: '#0065FF',
                    300: '#211161',
                    400: '#0052CC',
                    500: '#0065FF',
                    600: '#021434',
                    700: '#21313c',
                    800: '#001e2b',
                    900: '#040C18'
                },
                accent: {
                    100: '#b8c4c2',
                    200: '#5d6c74',
                    300: '#3d4f58'
                },
                table: {
                    100: '#f9fbfa',
                    200: '#e7eeec'
                }
            },
            fontFamily: {
                sans: ['var(--inter-font)', ...fontFamily.sans],
                means: ['var(--means-font)', ...fontFamily.sans]
            },
            borderRadius: {
                '4xl': '2rem'
            }
        }
    },
    plugins: [
        require('@tailwindcss/typography'),
        require('@tailwindcss/aspect-ratio'),
        require('@tailwindcss/forms'),
        plugin(({ addComponents }) => {
            addComponents({
                '.mobile-navbar-height': {
                    height: defaultTheme.mobileNavbarHeight
                },
                '.desktop-navbar-height': {
                    height: defaultTheme.desktopNavbarHeight
                },
                '.container': {
                    maxWidth: '1440px',
                    width: '90%',
                    margin: '0 auto'
                },
                '.mobile-sticky-section-nav': {
                    top: defaultTheme.mobileNavbarHeight
                },
                '.desktop-sticky-section-nav': {
                    top: defaultTheme.desktopNavbarHeight
                }
            });
        })
    ]
} satisfies Config;

export default tailwindConfig;
