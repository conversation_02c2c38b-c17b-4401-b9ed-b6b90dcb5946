{"EnterFlow": "EnterFlow", "İş akış sürecini sistemli bir şekilde kontrol altına alın. İşletmenizin akışını anlık takip edin ve kurguyu değiştirin.": "Take the workflow process under control in a systematic way. Follow the flow of your business instantly and change the fiction.", "FLOW": "FLOW", "Onay": "Confirmation", "Akış": "Flow", "Merkezi": "Central", "Süreç": "Process", "DAHA AKICI, DAHA KONTROLLÜ, DAHA MERKEZİ": "MORE FLUID, <PERSON><PERSON><PERSON> CONTROLLED, MORE CENTRALIZED", "Manuel olarak yapılan süreç takibinin dağınık ve karmaşıklığından kaynaklanan yapısal hatalardan kurtulun.": "Get rid of structural errors caused by the messy and complexity of manual process tracking.", "ONAY TAKİP": "APPROVAL FOLLOW", "İş akış isteklerinizi belgeler üzerinde takip edin.": "Track your workflow requests on documents.", "Tüm iş süreçlerinde belirlediğiniz hiyerarşiye göre onay isteklerinizi EnterERP belgeler üzerinde izleyin.": "Track your approval requests on EnterERP documents according to the hierarchy you set in all business processes.", "AKIŞ YÖNETİMİ": "FLOW MANAGEMENT", "Akışı kontrol altına alarak sorumlulukları takip edin.": "Follow responsibilities by controlling the flow.", "Çalışanlarınızın onay, red ve bildirim gibi akış süreçlerini yöneterek aldıkları sorumlulukları sistem üzerinden evrak bazında kontrol edin.": "Manage the flow processes such as approval, rejection and notification of your employees and control their responsibilities on a document basis through the system.", "MERKEZİ TAKİP": "CENTRAL TRACKING", "EnterFlow ile süreçlerinizde akışa takılan tüm belgeleri görün.": "See all documents stuck in the flow in your processes with EnterFlow.", "Tüm departmanlarda kurulan EnterFlow akış parametrelerine takılan onay, red, doğrulama ve atama gibi iş süreçlerini tek bir panel üzerinden yöneterek takibini yapın.": "Manage and track business processes such as approval, rejection, verification and assignment attached to EnterFlow flow parameters established in all departments through a single panel.", "SÜREÇ YAPILANDIRMA": "PROCESS CONFIGURATION", "Akış parametrelerinizi kolayca tanımlayın.": "Easily define your flow parameters.", "İş akış tanımları ile iş süreçlerinize etki eden durumlar için onaylama, atama, bildirim ve doğrulama türleri oluşturarak görev dağılımını sistem üzerinden yönetin.": "Manage the distribution of tasks through the system by creating approval, assignment, notification and verification types for situations that affect your business processes with workflow definitions.", "Onaylama": "Confirmation", "Akış parametrelerindeki belgeleri kimin ne zaman onaylayacağını tanımlayın.": "Define who approves documents in the flow parameters and when.", "Atama": "Assignment", "Belgelerdeki durumlar ve veriye dayalı kritik noktalar için görev tanımlamaları oluşturun.": "Create task definitions for situations and data-driven critical points in documents.", "Bildirim": "Notification", "Belgelerdeki durumlar ve veriye dayalı kritik noktalar için bildirim tanımlamaları çalıştırarak haberdar olun.": "Stay informed by running notification definitions for situations and data-driven critical points in documents.", "Doğrulama": "Verification", "Belgelerdeki durumlar ve veriye dayalı kritik noktalar için doğrulama çalıştırarak yanlışlardan kaçının.": "Avoid inaccuracies by running validation for cases and data-driven critical points in documents."}