{"EnterFlow": "EnterFlow", "İş akış sürecini sistemli bir şekilde kontrol altına alın. İşletmenizin akışını anlık takip edin ve kurguyu değiştirin.": "İş akış sürecini sistemli bir şekilde kontrol altına alın. İşletmenizin akışını anlık takip edin ve kurguyu değiştirin.", "FLOW": "FLOW", "Onay": "<PERSON><PERSON>", "Akış": "Akış", "Merkezi": "<PERSON><PERSON><PERSON><PERSON>", "Süreç": "<PERSON><PERSON><PERSON><PERSON>", "DAHA AKICI, DAHA KONTROLLÜ, DAHA MERKEZİ": "DAHA AKICI, DAHA KONTROLLÜ, DAHA MERKEZİ", "Manuel olarak yapılan süreç takibinin dağınık ve karmaşıklığından kaynaklanan yapısal hatalardan kurtulun.": "<PERSON> o<PERSON>ak yapılan süreç takibinin dağınık ve karmaşıklığından kaynaklanan yapısal hatalardan kurtulun.", "ONAY TAKİP": "ONAY TAKİP", "İş akış isteklerinizi belgeler üzerinde takip edin.": "İş akış isteklerinizi belgeler üzerinde takip edin.", "Tüm iş süreçlerinde belirlediğiniz hiyerarşiye göre onay isteklerinizi EnterERP belgeler üzerinde izleyin.": "Tüm iş süreçlerinde belirlediğiniz hiyerarşiye göre onay isteklerinizi EnterERP belgeler üzerinde izleyin.", "AKIŞ YÖNETİMİ": "AKIŞ YÖNETİMİ", "Akışı kontrol altına alarak sorumlulukları takip edin.": "Akışı kontrol altına alarak sorumlulukları takip edin.", "Çalışanlarınızın onay, red ve bildirim gibi akış süreçlerini yöneterek aldıkları sorumlulukları sistem üzerinden evrak bazında kontrol edin.": "Çalışanlar<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> onay, red ve bildirim gibi akış süreçlerini yöneterek aldıkları sorumlulukları sistem üzerinden evrak bazında kontrol edin.", "MERKEZİ TAKİP": "MERKEZİ TAKİP", "EnterFlow ile süreçlerinizde akışa takılan tüm belgeleri görün.": "EnterFlow ile süreçlerinizde akışa takılan tüm belgeleri görün.", "Tüm departmanlarda kurulan EnterFlow akış parametrelerine takılan onay, red, doğrulama ve atama gibi iş süreçlerini tek bir panel üzerinden yöneterek takibini yapın.": "Tüm departmanlarda kurulan EnterFlow akış parametrelerine takılan onay, red, doğ<PERSON>lama ve atama gibi iş süreçlerini tek bir panel üzerinden yöneterek takibini yapın.", "SÜREÇ YAPILANDIRMA": "SÜREÇ YAPILANDIRMA", "Akış parametrelerinizi kolayca tanımlayın.": "Akış parametrelerinizi kolayca tanımlayın.", "İş akış tanımları ile iş süreçlerinize etki eden durumlar için onaylama, atama, bildirim ve doğrulama türleri oluşturarak görev dağılımını sistem üzerinden yönetin.": "İş akış tanımları ile iş süreçlerinize etki eden durumlar için onaylama, atama, bildirim ve doğrulama türleri oluşturarak görev dağılımını sistem üzerinden yönetin.", "Onaylama": "<PERSON><PERSON><PERSON><PERSON>", "Akış parametrelerindeki belgeleri kimin ne zaman onaylayacağını tanımlayın.": "Akış parametrelerindeki belgeleri kimin ne zaman onaylayacağını tanımlayın.", "Atama": "<PERSON><PERSON>", "Belgelerdeki durumlar ve veriye dayalı kritik noktalar için görev tanımlamaları oluşturun.": "Belgelerdeki durumlar ve veriye dayalı kritik noktalar için görev tanımlamaları oluşturun.", "Bildirim": "<PERSON><PERSON><PERSON><PERSON>", "Belgelerdeki durumlar ve veriye dayalı kritik noktalar için bildirim tanımlamaları çalıştırarak haberdar olun.": "Belgelerdeki durumlar ve veriye dayalı kritik noktalar için bildirim tanımlamaları çalıştırarak haberdar olun.", "Doğrulama": "Doğrulama", "Belgelerdeki durumlar ve veriye dayalı kritik noktalar için doğrulama çalıştırarak yanlışlardan kaçının.": "Belgelerdeki durumlar ve veriye dayalı kritik noktalar için doğrulama çalıştırarak yanlışlardan kaçının."}