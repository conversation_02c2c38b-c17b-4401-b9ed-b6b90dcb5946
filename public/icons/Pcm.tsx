const PcmIcon = ({...props}: React.SVGProps<SVGSVGElement>) => {
    return (
<svg width="41" height="36" viewBox="0 0 41 36" fill="none" xmlns="http://www.w3.org/2000/svg" stroke="currentColor" {...props}>
<path fill-rule="evenodd" clip-rule="evenodd" d="M4.63864 3.25479C3.91453 3.25479 3.32348 3.84502 3.32348 4.56913C3.32348 5.29406 3.91371 5.88428 4.63864 5.88428C5.36357 5.88428 5.95379 5.29324 5.95379 4.56913C5.95379 3.84502 5.36275 3.25479 4.63864 3.25479ZM9.34577 3.25479C8.62166 3.25479 8.03061 3.84502 8.03061 4.56913C8.03061 5.29406 8.62084 5.88428 9.34577 5.88428C10.0707 5.88428 10.6609 5.29324 10.6609 4.56913C10.6601 3.84502 10.0699 3.25479 9.34577 3.25479ZM14.0521 3.25479C13.3263 3.25479 12.7369 3.84338 12.7369 4.56913C12.7369 5.29569 13.3255 5.88428 14.0521 5.88428C14.777 5.88428 15.3672 5.29324 15.3672 4.56913C15.3672 3.84502 14.7762 3.25479 14.0521 3.25479ZM25.3187 4.56913C25.3187 5.29406 25.9089 5.88428 26.6338 5.88428H36.0473C36.7722 5.88428 37.3624 5.29324 37.3624 4.56913C37.3624 3.84502 36.7714 3.25479 36.0473 3.25479H26.6338C25.9097 3.25479 25.3187 3.84502 25.3187 4.56913ZM0.993591 7.88028V3.46296C0.993591 2.2474 1.98384 1.25879 3.1994 1.25879H37.4865C38.7021 1.25879 39.6882 2.2474 39.6882 3.46296V7.88028H0.993591Z" fill="#EBF7FF"/>
<mask id="path-2-inside-1_57_6066" fill="white">
<path fill-rule="evenodd" clip-rule="evenodd" d="M26.6346 3.90789H36.0481C36.413 3.90789 36.7085 4.20504 36.7085 4.56914C36.7085 4.93405 36.413 5.23121 36.0481 5.23121H26.6346C26.2697 5.23121 25.9742 4.93405 25.9742 4.56914C25.9742 4.20504 26.2697 3.90789 26.6346 3.90789ZM26.6346 6.04757H36.0481C36.862 6.04757 37.5265 5.38468 37.5265 4.56914C37.5265 3.75441 36.862 3.09153 36.0481 3.09153H26.6346C25.8207 3.09153 25.1562 3.75441 25.1562 4.56914C25.1562 5.38468 25.8207 6.04757 26.6346 6.04757ZM14.0529 3.90789C14.4178 3.90789 14.7133 4.20504 14.7133 4.56914C14.7133 4.93405 14.4178 5.23121 14.0529 5.23121C13.688 5.23121 13.3884 4.93405 13.3884 4.56914C13.3892 4.20504 13.6888 3.90789 14.0529 3.90789ZM14.0529 6.04757C14.8668 6.04757 15.5313 5.38468 15.5313 4.56914C15.5313 3.75441 14.8668 3.09153 14.0529 3.09153C13.2349 3.09153 12.5745 3.75441 12.5745 4.56914C12.5753 5.38468 13.2357 6.04757 14.0529 6.04757ZM9.34657 3.90789C9.71148 3.90789 10.007 4.20504 10.007 4.56914C10.007 4.93405 9.71148 5.23121 9.34657 5.23121C8.98165 5.23121 8.68613 4.93405 8.68613 4.56914C8.68613 4.20504 8.98165 3.90789 9.34657 3.90789ZM9.34657 6.04757C10.1605 6.04757 10.825 5.38468 10.825 4.56914C10.825 3.75441 10.1605 3.09153 9.34657 3.09153C8.53266 3.09153 7.86814 3.75441 7.86814 4.56914C7.86814 5.38468 8.53266 6.04757 9.34657 6.04757ZM4.63944 3.90789C5.00435 3.90789 5.29987 4.20504 5.29987 4.56914C5.29987 4.93405 5.00435 5.23121 4.63944 5.23121C4.27452 5.23121 3.979 4.93405 3.979 4.56914C3.979 4.20504 4.27452 3.90789 4.63944 3.90789ZM4.63944 6.04757C5.45335 6.04757 6.11786 5.38468 6.11786 4.56914C6.11786 3.75441 5.45335 3.09153 4.63944 3.09153C3.82552 3.09153 3.16101 3.75441 3.16101 4.56914C3.16182 5.38468 3.82552 6.04757 4.63944 6.04757ZM37.4873 34.5777C38.6123 34.5777 39.5258 33.6626 39.5258 32.5368V8.53338H1.15684V32.5368C1.15684 33.6626 2.07443 34.5777 3.19938 34.5777H37.4873ZM3.20019 1.42207C2.07525 1.42207 1.15766 2.33721 1.15766 3.46297V7.71702H39.5266V3.46297C39.5266 2.33721 38.6131 1.42207 37.4881 1.42207H3.20019ZM37.4873 0.605713H3.20019C1.62217 0.605713 0.34375 1.8874 0.34375 3.46297V32.5368C0.34375 34.1124 1.62217 35.3941 3.20019 35.3941H37.4873C39.0612 35.3941 40.3438 34.1124 40.3438 32.5368V3.46297C40.3438 1.8874 39.0612 0.605713 37.4873 0.605713Z"/>
<path d="M20.752 13.5314V16.1053C21.7055 16.2956 22.4263 17.1389 22.4263 18.1471C22.4263 19.1553 21.7055 19.9986 20.752 20.1888V23.9718C20.752 24.1979 20.5691 24.38 20.3438 24.38C20.1185 24.38 19.9356 24.1979 19.9356 23.9718V20.1888C18.9821 19.9986 18.2613 19.1553 18.2613 18.1471C18.2613 17.1389 18.9821 16.2956 19.9356 16.1053V13.5314C19.9356 13.306 20.1185 13.1232 20.3438 13.1232C20.5691 13.1232 20.752 13.306 20.752 13.5314Z"/>
<path d="M30.5483 13.5305V19.37C31.5018 19.561 32.2226 20.4035 32.2226 21.4125C32.2226 22.4207 31.5018 23.264 30.5483 23.4542V27.6609C30.5483 27.8862 30.3654 28.0691 30.1401 28.0691C29.9148 28.0691 29.7319 27.8862 29.7319 27.6609V23.4542C28.7784 23.264 28.0576 22.4207 28.0576 21.4125C28.0576 20.4035 28.7784 19.5602 29.7319 19.37V13.5305C29.7319 13.3052 29.9148 13.1224 30.1401 13.1224C30.3654 13.1224 30.5483 13.3052 30.5483 13.5305Z"/>
<path d="M10.9557 13.5305V19.37C11.9092 19.561 12.63 20.4035 12.63 21.4125C12.63 22.4207 11.9092 23.264 10.9557 23.4542V27.6609C10.9557 27.8862 10.7728 28.0691 10.5475 28.0691C10.3222 28.0691 10.1393 27.8862 10.1393 27.6609V23.4542C9.18578 23.264 8.46494 22.4207 8.46494 21.4125C8.46494 20.4035 9.18578 19.5602 10.1393 19.37V13.5305C10.1393 13.3052 10.3222 13.1224 10.5475 13.1224C10.7728 13.1224 10.9557 13.3052 10.9557 13.5305Z"/>
</mask>
<path fill-rule="evenodd" clip-rule="evenodd" d="M26.6346 3.90789H36.0481C36.413 3.90789 36.7085 4.20504 36.7085 4.56914C36.7085 4.93405 36.413 5.23121 36.0481 5.23121H26.6346C26.2697 5.23121 25.9742 4.93405 25.9742 4.56914C25.9742 4.20504 26.2697 3.90789 26.6346 3.90789ZM26.6346 6.04757H36.0481C36.862 6.04757 37.5265 5.38468 37.5265 4.56914C37.5265 3.75441 36.862 3.09153 36.0481 3.09153H26.6346C25.8207 3.09153 25.1562 3.75441 25.1562 4.56914C25.1562 5.38468 25.8207 6.04757 26.6346 6.04757ZM14.0529 3.90789C14.4178 3.90789 14.7133 4.20504 14.7133 4.56914C14.7133 4.93405 14.4178 5.23121 14.0529 5.23121C13.688 5.23121 13.3884 4.93405 13.3884 4.56914C13.3892 4.20504 13.6888 3.90789 14.0529 3.90789ZM14.0529 6.04757C14.8668 6.04757 15.5313 5.38468 15.5313 4.56914C15.5313 3.75441 14.8668 3.09153 14.0529 3.09153C13.2349 3.09153 12.5745 3.75441 12.5745 4.56914C12.5753 5.38468 13.2357 6.04757 14.0529 6.04757ZM9.34657 3.90789C9.71148 3.90789 10.007 4.20504 10.007 4.56914C10.007 4.93405 9.71148 5.23121 9.34657 5.23121C8.98165 5.23121 8.68613 4.93405 8.68613 4.56914C8.68613 4.20504 8.98165 3.90789 9.34657 3.90789ZM9.34657 6.04757C10.1605 6.04757 10.825 5.38468 10.825 4.56914C10.825 3.75441 10.1605 3.09153 9.34657 3.09153C8.53266 3.09153 7.86814 3.75441 7.86814 4.56914C7.86814 5.38468 8.53266 6.04757 9.34657 6.04757ZM4.63944 3.90789C5.00435 3.90789 5.29987 4.20504 5.29987 4.56914C5.29987 4.93405 5.00435 5.23121 4.63944 5.23121C4.27452 5.23121 3.979 4.93405 3.979 4.56914C3.979 4.20504 4.27452 3.90789 4.63944 3.90789ZM4.63944 6.04757C5.45335 6.04757 6.11786 5.38468 6.11786 4.56914C6.11786 3.75441 5.45335 3.09153 4.63944 3.09153C3.82552 3.09153 3.16101 3.75441 3.16101 4.56914C3.16182 5.38468 3.82552 6.04757 4.63944 6.04757ZM37.4873 34.5777C38.6123 34.5777 39.5258 33.6626 39.5258 32.5368V8.53338H1.15684V32.5368C1.15684 33.6626 2.07443 34.5777 3.19938 34.5777H37.4873ZM3.20019 1.42207C2.07525 1.42207 1.15766 2.33721 1.15766 3.46297V7.71702H39.5266V3.46297C39.5266 2.33721 38.6131 1.42207 37.4881 1.42207H3.20019ZM37.4873 0.605713H3.20019C1.62217 0.605713 0.34375 1.8874 0.34375 3.46297V32.5368C0.34375 34.1124 1.62217 35.3941 3.20019 35.3941H37.4873C39.0612 35.3941 40.3438 34.1124 40.3438 32.5368V3.46297C40.3438 1.8874 39.0612 0.605713 37.4873 0.605713Z" stroke="#5D6C74" stroke-width="2" mask="url(#path-2-inside-1_57_6066)"/>
<path d="M20.752 13.5314V16.1053C21.7055 16.2956 22.4263 17.1389 22.4263 18.1471C22.4263 19.1553 21.7055 19.9986 20.752 20.1888V23.9718C20.752 24.1979 20.5691 24.38 20.3438 24.38C20.1185 24.38 19.9356 24.1979 19.9356 23.9718V20.1888C18.9821 19.9986 18.2613 19.1553 18.2613 18.1471C18.2613 17.1389 18.9821 16.2956 19.9356 16.1053V13.5314C19.9356 13.306 20.1185 13.1232 20.3438 13.1232C20.5691 13.1232 20.752 13.306 20.752 13.5314Z" stroke="#5D6C74" stroke-width="2" mask="url(#path-2-inside-1_57_6066)"/>
<path d="M30.5483 13.5305V19.37C31.5018 19.561 32.2226 20.4035 32.2226 21.4125C32.2226 22.4207 31.5018 23.264 30.5483 23.4542V27.6609C30.5483 27.8862 30.3654 28.0691 30.1401 28.0691C29.9148 28.0691 29.7319 27.8862 29.7319 27.6609V23.4542C28.7784 23.264 28.0576 22.4207 28.0576 21.4125C28.0576 20.4035 28.7784 19.5602 29.7319 19.37V13.5305C29.7319 13.3052 29.9148 13.1224 30.1401 13.1224C30.3654 13.1224 30.5483 13.3052 30.5483 13.5305Z" stroke="#5D6C74" stroke-width="2" mask="url(#path-2-inside-1_57_6066)"/>
<path d="M10.9557 13.5305V19.37C11.9092 19.561 12.63 20.4035 12.63 21.4125C12.63 22.4207 11.9092 23.264 10.9557 23.4542V27.6609C10.9557 27.8862 10.7728 28.0691 10.5475 28.0691C10.3222 28.0691 10.1393 27.8862 10.1393 27.6609V23.4542C9.18578 23.264 8.46494 22.4207 8.46494 21.4125C8.46494 20.4035 9.18578 19.5602 10.1393 19.37V13.5305C10.1393 13.3052 10.3222 13.1224 10.5475 13.1224C10.7728 13.1224 10.9557 13.3052 10.9557 13.5305Z" stroke="#5D6C74" stroke-width="2" mask="url(#path-2-inside-1_57_6066)"/>
<path d="M10.5475 22.9014C11.381 22.9014 12.0585 22.2238 12.0585 21.3903C12.0585 20.5568 11.381 19.8792 10.5475 19.8792C9.71396 19.8792 9.03638 20.5568 9.03638 21.3903C9.03638 22.2238 9.71477 22.9014 10.5475 22.9014Z" fill="#EBF7FF"/>
<path d="M20.3438 16.6138C19.5103 16.6138 18.8327 17.2913 18.8327 18.1249C18.8327 18.9584 19.5111 19.6359 20.3438 19.6359C21.1773 19.6359 21.8549 18.9584 21.8549 18.1249C21.8549 17.2913 21.1773 16.6138 20.3438 16.6138Z" fill="#EBF7FF"/>
<path d="M31.6512 21.3903C31.6512 20.5568 30.9736 19.8792 30.1401 19.8792C29.3066 19.8792 28.629 20.5576 28.629 21.3903C28.629 22.2238 29.3074 22.9014 30.1401 22.9014C30.9736 22.9014 31.6512 22.2238 31.6512 21.3903Z" fill="#EBF7FF"/>
</svg>





    );
};

export default PcmIcon;
