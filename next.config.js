/** @type {import('next').NextConfig} */

const withBundleAnalyzer = require('@next/bundle-analyzer')({
    enabled: process.env.ANALYZE === 'true'
});

const { i18n } = require('./next-i18next.config');

const contentSecurityPolicy = `
    default-src 'self';
    script-src * 'self' 'unsafe-eval' 'unsafe-inline';
    style-src 'self' 'unsafe-inline';
    img-src * blob: data:;
    media-src 'self';
    font-src 'self';
    connect-src *;
`;

const securityHeaders = [
    {
        key: 'Content-Security-Policy',
        value: contentSecurityPolicy.replace(/\n/g, '')
    },
    {
        key: 'X-DNS-Prefetch-Control',
        value: 'on'
    },
    {
        key: 'Strict-Transport-Security',
        value: 'max-age=63072000; includeSubDomains; preload'
    },
    {
        key: 'X-XSS-Protection',
        value: '1; mode=block'
    },
    {
        key: 'X-Frame-Options',
        value: 'SAMEORIGIN'
    },
    {
        key: 'X-Content-Type-Options',
        value: 'nosniff'
    },
    {
        key: 'Referrer-Policy',
        value: 'strict-origin-when-cross-origin'
    }
];

const nextConfig = {
    reactStrictMode: true,
    images: {
        domains: ['main.entererp.com', 'placehold.co'],
        formats: ['image/avif', 'image/webp']
    },
    i18n,
    async headers() {
        return [
            {
                source: '/:path*',
                headers: securityHeaders
            }
        ];
    },
    async redirects() {
        return [
            {
                source: '/blog/1',
                destination: '/blog',
                permanent: true
            },
            {
                source: '/blog/categories/:path/1',
                destination: '/blog/categories/:path',
                permanent: true
            },
            {
                source: '/blog/tags/:path/1',
                destination: '/blog/tags/:path',
                permanent: true
            }
        ];
    }
};

module.exports = withBundleAnalyzer(nextConfig);
