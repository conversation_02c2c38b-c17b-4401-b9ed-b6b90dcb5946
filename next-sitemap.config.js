const { featureFlags } = require('./app.config');

/** @type {import('next-sitemap').IConfig} */
module.exports = {
    siteUrl: 'https://www.entererp.com',
    generateRobotsTxt: true,
    priority: 1,
    changefreq: 'daily',
    exclude: ['/404'],
    additionalPaths: async () => {
        const blogPosts = await (
            await fetch(`${process.env.API_URL}/blog/posts`, {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({
                    limit: 1000,
                    skip: featureFlags.blogPostsCountPerPage
                })
            })
        ).json();

        const blogPostsPaths = blogPosts.map((blogPost) => {
            if (blogPost.status !== 'published') return;
            return {
                loc: `/blog/${blogPost.slug}`,
                lastmod: blogPost.updatedAt,
                changefreq: 'daily',
                priority: 1
            };
        });

        return [...blogPostsPaths];
    }
};
