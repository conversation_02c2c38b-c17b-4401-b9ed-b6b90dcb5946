{"name": "entererp-site", "version": "0.0.1", "private": true, "sideEffects": false, "scripts": {"dev": "next dev -p 3005", "build": "next build", "start": "next start", "format": "prettier --write .", "analyze": "ANALYZE=true next build", "postbuild": "next-sitemap", "sync": "git pull && yarn install && yarn build && pm2 reload entererp-site"}, "dependencies": {"@headlessui/react": "1.7.17", "express-rate-limit": "6.10.0", "express-slow-down": "1.6.0", "i18next": "23.4.6", "lottie-web": "5.10.1", "next": "13.4.19", "next-i18next": "14.0.2", "next-seo": "6.1.0", "nodemailer": "6.9.4", "posthog-js": "1.174.2", "react": "18.2.0", "react-accessible-accordion": "5.0.0", "react-cookie-consent": "8.0.1", "react-country-flag": "3.1.0", "react-dom": "18.2.0", "react-hook-form": "7.46.0", "react-i18next": "13.2.2", "react-imask": "6.4.3", "react-paginate": "8.2.0", "react-swipeable": "7.0.1", "sharp": "0.32.5", "swiper": "10.0.3"}, "devDependencies": {"@next/bundle-analyzer": "13.4.19", "@tailwindcss/aspect-ratio": "0.4.2", "@tailwindcss/forms": "0.5.6", "@tailwindcss/line-clamp": "0.4.4", "@tailwindcss/typography": "0.5.9", "@types/express-slow-down": "1.3.2", "@types/node": "20.5.9", "@types/nodemailer": "6.4.9", "@types/react": "18.2.21", "@types/react-dom": "18.2.7", "autoprefixer": "10.4.15", "eslint": "8.48.0", "eslint-config-next": "13.4.19", "eslint-plugin-unused-imports": "3.0.0", "next-sitemap": "4.2.2", "postcss": "8.4.29", "postcss-import": "15.1.0", "prettier": "3.0.3", "prettier-plugin-tailwindcss": "0.5.4", "tailwind-merge": "1.14.0", "tailwindcss": "3.3.3", "typescript": "5.2.2"}, "packageManager": "yarn@1.22.22+sha512.a6b2f7906b721bba3d67d4aff083df04dad64c399707841b7acf00f6b133b7ac24255f2652fa22ae3534329dc6180534e98d17432037ff6fd140556e2bb3137e"}